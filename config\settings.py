import os

# 基础路径配置
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
STATIC_DIR = os.path.join(BASE_DIR, 'static')
UPLOAD_DIR = os.path.join(STATIC_DIR, 'uploads')
TEMP_DIR = os.path.join(STATIC_DIR, 'temp')
DATA_DIR = os.path.join(STATIC_DIR, 'data')
SKU_DIR = os.path.join(STATIC_DIR, 'SKU')
GOODS_DATA_DIR = os.path.join(STATIC_DIR, 'goods_data')
SPU_DATA_DIR = os.path.join(STATIC_DIR, 'spu_day_date')

# 文件配置
ACCOUNTS_FILE = '宜承账号.json'
ART_TASKS_FILE = os.path.join(DATA_DIR, 'art_tasks.json')
ART_TASKS_OVERVIEW_FILE = os.path.join(DATA_DIR, 'art_tasks_overview.json')
MESSAGES_FILE = os.path.join(DATA_DIR, 'messages.json')

# 角色配置
VALID_ROLES = ['admin', 'yunying', 'art', 'caigou', 'caiwu', 'kefu']
ROLE_NAME_MAP = {
    'admin': 'guanliyuan',
    'art': 'meigong',
    'yunying': 'yunying',
    'caigou': 'caigou',
    'caiwu': 'caiwu',
    'kefu': 'kefu',
    'operator': 'yunying'
}

# Cookie配置
COOKIE_MAX_AGE = 3600 * 24 * 7  # 7天
COOKIE_SAME_SITE = 'Lax'

# 分页配置
DEFAULT_PAGE_SIZE = 10
MAX_PAGE_SIZE = 100

# 文件上传配置
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx'}
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB

# 编码配置
ENCODINGS = ['gbk', 'utf-8', 'gb18030', 'gb2312', 'cp936']

# API响应状态码
class StatusCode:
    SUCCESS = 200
    CREATED = 201
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    CONFLICT = 409
    INTERNAL_ERROR = 500

# 确保所需目录存在
def ensure_directories():
    """确保所需的目录结构存在"""
    directories = [
        STATIC_DIR,
        UPLOAD_DIR,
        TEMP_DIR,
        DATA_DIR,
        SKU_DIR,
        GOODS_DATA_DIR,
        SPU_DATA_DIR,
        os.path.join(UPLOAD_DIR, 'art_tasks')
    ]
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"确保目录存在: {directory}")

# 初始化配置
def init_config():
    """初始化配置"""
    # 创建必要的目录
    for directory in [STATIC_DIR, UPLOAD_DIR, TEMP_DIR, DATA_DIR, SKU_DIR, GOODS_DATA_DIR, SPU_DATA_DIR]:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"创建目录: {directory}")
            
    # 初始化数据文件
    init_data_files = [
        (ART_TASKS_FILE, []),
        (ART_TASKS_OVERVIEW_FILE, {
            'total_tasks': 0,
            'completed_tasks': 0,
            'pending_tasks': 0,
            'in_progress_tasks': 0,
            'by_type': {},
            'by_priority': {},
            'by_assignee': {}
        }),
        (MESSAGES_FILE, [])
    ]
    
    for file_path, default_data in init_data_files:
        if not os.path.exists(file_path):
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            import json
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(default_data, f, ensure_ascii=False, indent=4)
            print(f"创建数据文件: {file_path}")
            
    print("配置初始化完成") 