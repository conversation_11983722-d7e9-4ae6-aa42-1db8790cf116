@echo off
echo 启动FRP客户端...
echo 请确保已下载frpc.exe并放置在当前目录
echo 请确保已正确配置frpc.ini文件

if not exist frpc.exe (
    echo 错误：未找到frpc.exe
    echo 请从 https://github.com/fatedier/frp/releases 下载适合您系统的版本
    echo 并将frpc.exe放置在当前目录
    pause
    exit /b
)

if not exist frpc.ini (
    echo 错误：未找到frpc.ini
    echo 请复制frpc.ini.example为frpc.ini并进行配置
    pause
    exit /b
)

echo 正在启动FRP客户端...
frpc.exe -c frpc.ini

pause 