/**
 * 商品整改项目管理
 * 用于显示和管理商品整改数据
 */

// 添加样式
function addProductRectificationStyles() {
    const styleId = 'product-rectification-styles';
    if (document.getElementById(styleId)) return;

    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
        .rectification-management {
            font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 28px;
            color: #2c3e50;
            position: relative;
            display: inline-block;
            padding-bottom: 10px;
        }

        .header h1:after {
            content: '';
            position: absolute;
            width: 50%;
            height: 3px;
            background: linear-gradient(90deg, #4b6cb7, #182848);
            bottom: 0;
            left: 25%;
            border-radius: 2px;
        }

        .stats-card {
            border-radius: 12px;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .card {
            border-radius: 12px;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .card-header {
            background: linear-gradient(90deg, #4b6cb7, #182848);
            color: white;
            font-weight: 500;
            border: none;
        }

        .table-responsive {
            border-radius: 8px;
        }

        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
            color: #2c3e50;
        }

        .rectification-row:hover {
            background-color: #f8f9fa;
        }

        .btn-primary {
            background: linear-gradient(90deg, #4b6cb7, #182848);
            border: none;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .pagination .page-link {
            border: none;
            color: #4b6cb7;
        }

        .pagination .page-item.active .page-link {
            background: linear-gradient(90deg, #4b6cb7, #182848);
            border: none;
        }

        .badge {
            font-size: 0.75em;
            padding: 0.5em 0.75em;
        }

        .text-truncate {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .modal-content {
            border-radius: 12px;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .modal-header {
            background: linear-gradient(90deg, #4b6cb7, #182848);
            color: white;
            border-radius: 12px 12px 0 0;
        }

        .btn-close {
            filter: invert(1);
        }
    `;
    document.head.appendChild(style);
}

// 创建商品整改管理命名空间，避免与其他模块冲突
window.ProductRectificationManagement = (function() {
    'use strict';
    
    // 私有变量，避免全局污染
    let currentPage = 1;
    let totalRecords = 0;
    let pageSize = 20;
    let sortField = 'created_at';
    let sortOrder = 'desc';
    let keyword = '';
    let shopFilter = '';
    let statusFilter = 'all';
    let problemTypeFilter = 'all';
    let startDate = '';
    let endDate = '';
    
    // 统计数据
    let statsData = {
        total: 0,
        init: 0,
        submit: 0,
        no_submit: 0,
        todayGrowth: 0,
        weeklyGrowth: 0,
        monthlyGrowth: 0
    };

    // 设置缓存过期时间（毫秒）
    const RECTIFICATION_CACHE_EXPIRY = 30 * 60 * 1000; // 30分钟

    // 状态映射
    const statusMapping = {
        '0': '待处理',
        '1': '已提交',
        '3': '未提交'
    };

    // 状态样式映射
    const statusBadgeMapping = {
        '0': 'badge bg-warning',
        '1': 'badge bg-success', 
        '3': 'badge bg-danger'
    };

    // 数据类别映射
    const categoryMapping = {
        'init': '初始数据',
        'submit': '已提交',
        'no_submit': '未提交'
    };

    // 加载商品整改数据
    function loadRectificationData() {
        // 显示加载状态
        const container = document.querySelector('.container');
        container.innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-3">正在加载商品整改数据...</p>
            </div>
        `;

        // 尝试从缓存加载数据
        const cachedData = loadFromCache();
        if (cachedData) {
            // 使用缓存数据进行初始渲染
            const { page, pageSize: cachedPageSize, data, total, statistics } = cachedData;
            // 设置全局变量
            currentPage = page || 1;
            pageSize = cachedPageSize || 20;
            totalRecords = total || 0;
            statsData = statistics || statsData;
            
            console.log("已从缓存加载数据，共加载 " + totalRecords + " 条记录");
            
            // 保存当前数据供图表更新使用
            window.currentRectificationData = data;
            
            renderRectificationManagement(data);
            
            // 在后台继续请求最新数据
            fetchRectificationData();
        } else {
            // 直接请求数据
            fetchRectificationData();
        }
    }

    // 保存数据到本地缓存
    function saveToCache(responseData) {
        try {
            const cacheData = {
                timestamp: Date.now(),
                page: responseData.page,
                pageSize: responseData.pageSize,
                total: responseData.total,
                data: responseData.data,
                statistics: responseData.statistics
            };
            localStorage.setItem('rectificationDataCache', JSON.stringify(cacheData));
            console.log("数据已缓存到本地，共 " + responseData.total + " 条记录");
            return true;
        } catch (error) {
            console.error("缓存数据失败:", error);
            return false;
        }
    }

    // 从本地缓存加载数据
    function loadFromCache() {
        try {
            const cacheData = localStorage.getItem('rectificationDataCache');
            if (!cacheData) return null;
            
            const parsedCache = JSON.parse(cacheData);
            const now = Date.now();
            
            // 检查缓存是否过期
            if (now - parsedCache.timestamp > RECTIFICATION_CACHE_EXPIRY) {
                console.log("缓存已过期，需要重新加载数据");
                localStorage.removeItem('rectificationDataCache');
                return null;
            }
            
            return parsedCache;
        } catch (error) {
            console.error("读取缓存数据失败:", error);
            return null;
        }
    }

    // 从API获取商品整改数据
    function fetchRectificationData() {
        // 显示加载状态
        const tableBody = document.getElementById('rectificationTableBody');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载数据...</p>
                    </td>
                </tr>
            `;
        }
        
        // 构建查询URL
        let url = `/api/product-rectification?page=${currentPage}&pageSize=${pageSize}&sortField=${sortField}&sortOrder=${sortOrder}`;
        
        // 添加关键词搜索参数
        if (keyword) {
            url += `&keyword=${encodeURIComponent(keyword)}`;
        }
        
        // 添加店铺筛选参数
        if (shopFilter && shopFilter !== 'all') {
            url += `&shop=${encodeURIComponent(shopFilter)}`;
        }
        
        // 添加状态筛选参数
        if (statusFilter && statusFilter !== 'all') {
            url += `&status=${encodeURIComponent(statusFilter)}`;
        }
        
        // 添加问题类型筛选参数
        if (problemTypeFilter && problemTypeFilter !== 'all') {
            url += `&problemType=${encodeURIComponent(problemTypeFilter)}`;
        }
        
        // 添加日期筛选参数
        if (startDate) {
            url += `&startDate=${encodeURIComponent(startDate)}`;
        }
        
        if (endDate) {
            url += `&endDate=${encodeURIComponent(endDate)}`;
        }
        
        fetch(url)
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    // 更新统计数据
                    if (result.statistics) {
                        statsData = result.statistics;
                    }
                    
                    // 更新总记录数
                    totalRecords = result.total || 0;
                    
                    // 保存到缓存
                    saveToCache(result);
                    
                    // 保存当前数据供图表更新使用
                    window.currentRectificationData = result.data;
                    
                    // 渲染数据
                    renderRectificationManagement(result.data);
                    
                    // 渲染筛选选项
                    generateFilterOptions(result.data, result.shops, result.problemTypes);
                    
                    console.log(`成功加载数据: ${result.data.length} 条记录，共 ${totalRecords} 条`);
                } else {
                    throw new Error(result.message || '加载数据失败');
                }
            })
            .catch(error => {
                console.error('获取商品整改数据失败:', error);
                
                // 显示错误信息
                const container = document.querySelector('.container');
                container.innerHTML = `
                    <div class="alert alert-danger" role="alert">
                        <h4 class="alert-heading">数据加载失败</h4>
                        <p>无法获取商品整改数据，请检查网络连接或稍后重试。</p>
                        <hr>
                        <p class="mb-0">错误信息: ${error.message}</p>
                        <button class="btn btn-outline-danger mt-3" onclick="window.ProductRectificationManagement.refreshData()">
                            <i class="fas fa-redo"></i> 重新加载
                        </button>
                    </div>
                `;
            });
    }

    // 渲染商品整改管理页面
    function renderRectificationManagement(data) {
        const container = document.querySelector('.container');
        
        // 保存当前筛选值
        const currentShopFilter = shopFilter;
        const currentStatusFilter = statusFilter;
        const currentProblemTypeFilter = problemTypeFilter;
        const currentStartDate = startDate;
        const currentEndDate = endDate;
        const currentPageSizeValue = pageSize;
        const currentKeyword = keyword;
        
        // 构建页面内容
        container.innerHTML = `
            <div class="rectification-management">
                <div class="header">
                    <h1>商品整改项目管理</h1>
                    <p>管理和跟踪商品整改项目状态</p>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-subtitle mb-2 text-muted">总计</h6>
                                        <h3 class="card-title mb-0">${statsData.total || 0}</h3>
                                    </div>
                                    <div class="stats-icon bg-primary">
                                        <i class="fas fa-list"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-subtitle mb-2 text-muted">待处理</h6>
                                        <h3 class="card-title mb-0">${statsData.init || 0}</h3>
                                    </div>
                                    <div class="stats-icon bg-warning">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-subtitle mb-2 text-muted">已提交</h6>
                                        <h3 class="card-title mb-0">${statsData.submit || 0}</h3>
                                    </div>
                                    <div class="stats-icon bg-success">
                                        <i class="fas fa-check"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-subtitle mb-2 text-muted">未提交</h6>
                                        <h3 class="card-title mb-0">${statsData.no_submit || 0}</h3>
                                    </div>
                                    <div class="stats-icon bg-danger">
                                        <i class="fas fa-times"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
        `;
        
        // 添加筛选和搜索区域
        container.innerHTML += `
                <!-- 筛选和搜索区域 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>筛选和搜索
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="keywordSearch" class="form-label">关键词搜索</label>
                                <input type="text" class="form-control" id="keywordSearch"
                                       placeholder="搜索商品名称、店铺名称..." value="${currentKeyword}">
                            </div>
                            <div class="col-md-2">
                                <label for="shopFilter" class="form-label">店铺筛选</label>
                                <select class="form-select" id="shopFilter">
                                    <option value="all">全部店铺</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="statusFilter" class="form-label">状态筛选</label>
                                <select class="form-select" id="statusFilter">
                                    <option value="all">全部状态</option>
                                    <option value="0" ${currentStatusFilter === '0' ? 'selected' : ''}>待处理</option>
                                    <option value="1" ${currentStatusFilter === '1' ? 'selected' : ''}>已提交</option>
                                    <option value="3" ${currentStatusFilter === '3' ? 'selected' : ''}>未提交</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="problemTypeFilter" class="form-label">问题类型</label>
                                <select class="form-select" id="problemTypeFilter">
                                    <option value="all">全部类型</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">日期范围</label>
                                <div class="input-group">
                                    <input type="date" class="form-control" id="startDateFilter"
                                           value="${currentStartDate}" title="开始日期">
                                    <span class="input-group-text">至</span>
                                    <input type="date" class="form-control" id="endDateFilter"
                                           value="${currentEndDate}" title="结束日期">
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button class="btn btn-primary me-2" onclick="window.ProductRectificationManagement.applyFilters()">
                                    <i class="fas fa-search"></i> 应用筛选
                                </button>
                                <button class="btn btn-outline-secondary me-2" onclick="window.ProductRectificationManagement.clearFilters()">
                                    <i class="fas fa-times"></i> 清除筛选
                                </button>
                                <button class="btn btn-success me-2" onclick="exportRectificationData()">
                                    <i class="fas fa-download"></i> 导出数据
                                </button>
                                <button class="btn btn-info" onclick="window.ProductRectificationManagement.refreshData()">
                                    <i class="fas fa-sync-alt"></i> 刷新数据
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>整改项目列表
                        </h5>
                        <div class="d-flex align-items-center">
                            <label for="pageSizeSelect" class="form-label me-2 mb-0">每页显示:</label>
                            <select class="form-select form-select-sm" id="pageSizeSelect" style="width: auto;">
                                <option value="10" ${currentPageSizeValue === 10 ? 'selected' : ''}>10</option>
                                <option value="20" ${currentPageSizeValue === 20 ? 'selected' : ''}>20</option>
                                <option value="50" ${currentPageSizeValue === 50 ? 'selected' : ''}>50</option>
                                <option value="100" ${currentPageSizeValue === 100 ? 'selected' : ''}>100</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 80px;">ID</th>
                                        <th style="width: 120px;">商品ID</th>
                                        <th style="width: 150px;">店铺名称</th>
                                        <th>问题标签</th>
                                        <th style="width: 100px;">状态</th>
                                        <th style="width: 100px;">类别</th>
                                        <th style="width: 120px;">创建时间</th>
                                        <th style="width: 100px;">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="rectificationTableBody">
                                    <!-- 数据将通过JavaScript动态填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted">
                                显示第 <span id="recordStart">0</span> - <span id="recordEnd">0</span> 条，
                                共 <span id="totalRecords">${totalRecords}</span> 条记录
                            </div>
                            <nav aria-label="分页导航">
                                <ul class="pagination pagination-sm mb-0" id="paginationContainer">
                                    <!-- 分页控件将通过JavaScript动态生成 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 恢复筛选值
        document.getElementById('shopFilter').value = currentShopFilter;
        document.getElementById('statusFilter').value = currentStatusFilter;
        document.getElementById('problemTypeFilter').value = currentProblemTypeFilter;
        document.getElementById('startDateFilter').value = currentStartDate;
        document.getElementById('endDateFilter').value = currentEndDate;
        document.getElementById('pageSizeSelect').value = currentPageSizeValue;
        document.getElementById('keywordSearch').value = currentKeyword;

        // 渲染表格数据
        renderTableData(data);

        // 绑定事件监听器
        bindEventListeners();
    }

    // 渲染表格数据
    function renderTableData(data) {
        const tableBody = document.getElementById('rectificationTableBody');

        if (!data || data.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <div class="text-muted">
                            <i class="fas fa-inbox fa-2x mb-3"></i>
                            <p>暂无数据</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        let tableContent = '';
        for (let i = 0; i < data.length; i++) {
            const item = data[i];
            const statusBadge = statusBadgeMapping[item.status] || 'badge bg-secondary';
            const statusText = statusMapping[item.status] || '未知';
            const categoryText = categoryMapping[item.data_category] || item.data_category;

            // 格式化创建时间
            const createdAt = item.created_at_timestamp ?
                new Date(item.created_at_timestamp * 1000).toLocaleDateString('zh-CN') :
                (item.inserted_at ? new Date(item.inserted_at).toLocaleDateString('zh-CN') : '-');

            tableContent += `
                <tr class="rectification-row" data-id="${item.id}" style="cursor: pointer;">
                    <td>${item.id}</td>
                    <td class="text-nowrap">${item.goods_id || '-'}</td>
                    <td class="text-nowrap">${item.shop_name || '-'}</td>
                    <td>
                        <span class="text-truncate d-inline-block" style="max-width: 200px;"
                              title="${item.problem_label || '-'}">
                            ${item.problem_label || '-'}
                        </span>
                    </td>
                    <td class="text-center">
                        <span class="${statusBadge}">
                            ${statusText}
                        </span>
                    </td>
                    <td class="text-center">${categoryText}</td>
                    <td class="text-nowrap">${createdAt}</td>
                    <td class="text-center">
                        <button class="btn btn-sm btn-outline-primary"
                                onclick="viewRectificationDetails('${item.id}', event)">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                </tr>
            `;
        }

        tableBody.innerHTML = tableContent;

        // 为每行添加点击事件，打开详情模态框
        document.querySelectorAll('.rectification-row').forEach(row => {
            row.addEventListener('click', function(e) {
                // 如果点击的是按钮，不触发行点击事件
                if (e.target.closest('button')) return;

                const id = this.dataset.id;
                if (id) {
                    viewRectificationDetails(id);
                }
            });
        });

        // 渲染分页控件
        renderPagination();

        // 更新记录显示信息
        updateRecordInfo();
    }

    // 渲染分页控件
    function renderPagination() {
        const paginationContainer = document.getElementById('paginationContainer');
        if (!paginationContainer) return;

        const totalPages = Math.ceil(totalRecords / pageSize);

        if (totalPages <= 1) {
            paginationContainer.innerHTML = '';
            return;
        }

        let paginationHTML = '';

        // 上一页按钮
        paginationHTML += `
            <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="changePage(${currentPage - 1}); return false;">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `;

        // 页码按钮
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="changePage(1); return false;">1</a>
                </li>
            `;
            if (startPage > 2) {
                paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${i}); return false;">${i}</a>
                </li>
            `;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="changePage(${totalPages}); return false;">${totalPages}</a>
                </li>
            `;
        }

        // 下一页按钮
        paginationHTML += `
            <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="changePage(${currentPage + 1}); return false;">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `;

        paginationContainer.innerHTML = paginationHTML;
    }

    // 更新记录显示信息
    function updateRecordInfo() {
        const recordStart = (currentPage - 1) * pageSize + 1;
        const recordEnd = Math.min(currentPage * pageSize, totalRecords);

        document.getElementById('recordStart').textContent = totalRecords > 0 ? recordStart : 0;
        document.getElementById('recordEnd').textContent = recordEnd;
        document.getElementById('totalRecords').textContent = totalRecords;
    }

    // 绑定事件监听器
    function bindEventListeners() {
        // 关键词搜索
        const keywordInput = document.getElementById('keywordSearch');
        if (keywordInput) {
            keywordInput.addEventListener('input', debounce(function() {
                keyword = this.value.trim();
                applyFilters();
            }, 500));
        }

        // 店铺筛选
        const shopFilterSelect = document.getElementById('shopFilter');
        if (shopFilterSelect) {
            shopFilterSelect.addEventListener('change', function() {
                shopFilter = this.value;
                applyFilters();
            });
        }

        // 状态筛选
        const statusFilterSelect = document.getElementById('statusFilter');
        if (statusFilterSelect) {
            statusFilterSelect.addEventListener('change', function() {
                statusFilter = this.value;
                applyFilters();
            });
        }

        // 问题类型筛选
        const problemTypeFilterSelect = document.getElementById('problemTypeFilter');
        if (problemTypeFilterSelect) {
            problemTypeFilterSelect.addEventListener('change', function() {
                problemTypeFilter = this.value;
                applyFilters();
            });
        }

        // 日期筛选
        const startDateInput = document.getElementById('startDateFilter');
        if (startDateInput) {
            startDateInput.addEventListener('change', function() {
                startDate = this.value;
                applyFilters();
            });
        }

        const endDateInput = document.getElementById('endDateFilter');
        if (endDateInput) {
            endDateInput.addEventListener('change', function() {
                endDate = this.value;
                applyFilters();
            });
        }

        // 每页显示数量
        const pageSizeSelect = document.getElementById('pageSizeSelect');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', function() {
                pageSize = parseInt(this.value);
                currentPage = 1; // 重置到第一页
                fetchRectificationData();
            });
        }
    }

    // 防抖函数
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 应用筛选
    function applyFilters() {
        currentPage = 1; // 重置到第一页
        fetchRectificationData();
    }

    // 清除筛选
    function clearFilters() {
        keyword = '';
        shopFilter = '';
        statusFilter = 'all';
        problemTypeFilter = 'all';
        startDate = '';
        endDate = '';
        currentPage = 1;

        // 清除表单值
        document.getElementById('keywordSearch').value = '';
        document.getElementById('shopFilter').value = 'all';
        document.getElementById('statusFilter').value = 'all';
        document.getElementById('problemTypeFilter').value = 'all';
        document.getElementById('startDateFilter').value = '';
        document.getElementById('endDateFilter').value = '';

        fetchRectificationData();
    }

    // 刷新数据
    function refreshData() {
        // 清除缓存
        localStorage.removeItem('rectificationDataCache');
        fetchRectificationData();
    }

    // 切换页面
    function changePage(page) {
        if (page < 1 || page > Math.ceil(totalRecords / pageSize)) return;
        currentPage = page;
        fetchRectificationData();
    }

    // 生成筛选选项
    function generateFilterOptions(data, shops, problemTypes) {
        // 生成店铺选项
        const shopSelect = document.getElementById('shopFilter');
        if (shopSelect && shops) {
            const currentValue = shopSelect.value;
            shopSelect.innerHTML = '<option value="all">全部店铺</option>';

            shops.forEach(shop => {
                const option = document.createElement('option');
                option.value = shop;
                option.textContent = shop;
                if (shop === currentValue) {
                    option.selected = true;
                }
                shopSelect.appendChild(option);
            });
        }

        // 生成问题类型选项
        const problemTypeSelect = document.getElementById('problemTypeFilter');
        if (problemTypeSelect && problemTypes) {
            const currentValue = problemTypeSelect.value;
            problemTypeSelect.innerHTML = '<option value="all">全部类型</option>';

            problemTypes.forEach(type => {
                const option = document.createElement('option');
                option.value = type;
                option.textContent = type;
                if (type === currentValue) {
                    option.selected = true;
                }
                problemTypeSelect.appendChild(option);
            });
        }
    }

    // 查看整改详情
    function viewRectificationDetails(id, event) {
        if (event) {
            event.stopPropagation();
        }

        // 从当前数据中查找对应记录
        const data = window.currentRectificationData || [];
        const item = data.find(d => d.id == id);

        if (!item) {
            alert('未找到对应的整改记录');
            return;
        }

        // 创建详情模态框
        const modalHTML = `
            <div class="modal fade" id="rectificationDetailModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">整改项目详情</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>项目ID:</strong> ${item.id}</p>
                                    <p><strong>商品ID:</strong> ${item.goods_id || '-'}</p>
                                    <p><strong>店铺名称:</strong> ${item.shop_name || '-'}</p>
                                    <p><strong>商城ID:</strong> ${item.mall_id || '-'}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>状态:</strong>
                                        <span class="${statusBadgeMapping[item.status] || 'badge bg-secondary'}">
                                            ${statusMapping[item.status] || '未知'}
                                        </span>
                                    </p>
                                    <p><strong>类别:</strong> ${categoryMapping[item.data_category] || item.data_category}</p>
                                    <p><strong>类型:</strong> ${item.type || '-'}</p>
                                    <p><strong>问卷ID:</strong> ${item.questionnaire_id || '-'}</p>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-12">
                                    <p><strong>问题标签:</strong></p>
                                    <p class="text-muted">${item.problem_label || '无'}</p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <p><strong>基础信息:</strong></p>
                                    <p class="text-muted">${item.base_info || '无'}</p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <p><strong>建议信息:</strong></p>
                                    <p class="text-muted">${item.advise_info || '无'}</p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <p><strong>商家计划:</strong></p>
                                    <p class="text-muted">${item.merchant_plan || '无'}</p>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>创建时间:</strong> ${item.created_at_timestamp ?
                                        new Date(item.created_at_timestamp * 1000).toLocaleString('zh-CN') :
                                        (item.inserted_at ? new Date(item.inserted_at).toLocaleString('zh-CN') : '-')}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>更新时间:</strong> ${item.updated_at_timestamp ?
                                        new Date(item.updated_at_timestamp * 1000).toLocaleString('zh-CN') : '-'}</p>
                                </div>
                            </div>
                            ${item.ext_map ? `
                            <div class="row">
                                <div class="col-12">
                                    <p><strong>扩展信息:</strong></p>
                                    <pre class="text-muted small">${item.ext_map}</pre>
                                </div>
                            </div>
                            ` : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        const existingModal = document.getElementById('rectificationDetailModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新模态框到页面
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('rectificationDetailModal'));
        modal.show();
    }

    // 导出数据
    function exportRectificationData() {
        // 构建导出URL
        let url = '/api/product-rectification/export?';

        const params = [];
        if (keyword) params.push(`keyword=${encodeURIComponent(keyword)}`);
        if (shopFilter && shopFilter !== 'all') params.push(`shop=${encodeURIComponent(shopFilter)}`);
        if (statusFilter && statusFilter !== 'all') params.push(`status=${encodeURIComponent(statusFilter)}`);
        if (problemTypeFilter && problemTypeFilter !== 'all') params.push(`problemType=${encodeURIComponent(problemTypeFilter)}`);
        if (startDate) params.push(`startDate=${encodeURIComponent(startDate)}`);
        if (endDate) params.push(`endDate=${encodeURIComponent(endDate)}`);

        url += params.join('&');

        // 创建下载链接
        const link = document.createElement('a');
        link.href = url;
        link.download = `商品整改数据_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // 初始化函数，对外暴露
    function generateProductRectification() {
        // 添加样式
        addProductRectificationStyles();

        // 加载商品整改数据
        loadRectificationData();
    }

    // 返回公共API
    return {
        generateProductRectification: generateProductRectification,
        applyFilters: applyFilters,
        clearFilters: clearFilters,
        refreshData: refreshData,
        changePage: changePage,
        viewRectificationDetails: viewRectificationDetails,
        exportRectificationData: exportRectificationData
    };
})();

// 将主函数暴露到全局作用域
window.generateProductRectification = window.ProductRectificationManagement.generateProductRectification;
window.applyFilters = window.ProductRectificationManagement.applyFilters;
window.clearFilters = window.ProductRectificationManagement.clearFilters;
window.refreshData = window.ProductRectificationManagement.refreshData;
window.changePage = window.ProductRectificationManagement.changePage;
window.viewRectificationDetails = window.ProductRectificationManagement.viewRectificationDetails;
window.exportRectificationData = window.ProductRectificationManagement.exportRectificationData;
