<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宜承-店铺数据管理系统</title>
    <!-- 添加网站图标 -->
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
    <!-- 引入Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- 引入ECharts库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js" onerror="loadFallbackECharts()"></script>
    <!-- 引入Chart.js库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    
    
    <!-- 预加载样式和功能 -->
    <script src="generateProductManagement.js"></script>
    <!-- 
    以下脚本暂时不可用，如需使用请先创建对应文件
    <script src="refactoredExample.js"></script>
    <script src="stateExample.js"></script>
    -->
    <script>
        // 修改为完整的页面加载函数
        function loadFallbackECharts() {
            console.log('ECharts CDN加载失败，使用本地备用库');
            var script = document.createElement('script');
            script.src = 'libs/echarts-simple.js';
            document.head.appendChild(script);
        }
        
        // 验证登录状态
        function checkLoginStatus() {
            const cookies = document.cookie.split(';');
            const loginAuth = cookies.find(cookie => cookie.trim().startsWith('loginAuth='));
            if (!loginAuth || loginAuth.split('=')[1].trim() !== 'true') {
                window.location.href = 'login.html';
            }
        }
        
        // 在页面加载时完成所有初始化工作
        window.onload = function() {
            // 检查登录状态
            checkLoginStatus();
            
            // 提取 generateProductManagement.js 中的样式到 window 对象
            loadAndExtractStyles();
            
            // 添加辅助函数用于应用样式
            window.applyProductManagementStyles = function(targetSelector = 'head', styleId = 'applied-product-management-styles') {
                if (window.productManagementStyles) {
                    // 检查是否已经应用过这个样式
                    if (!document.getElementById(styleId)) {
                        const styleElement = document.createElement('style');
                        styleElement.id = styleId;
                        styleElement.textContent = window.productManagementStyles;
                        const target = document.querySelector(targetSelector);
                        if (target) {
                            target.appendChild(styleElement);
                            console.log('成功应用产品管理样式');
                            return true;
                        } else {
                            console.warn('未找到目标元素，无法应用样式');
                        }
                    } else {
                        console.log('样式已存在，无需重复应用');
                        return true;
                    }
                } else {
                    console.warn('未找到产品管理样式，尝试直接加载');
                    // 尝试直接调用加载函数
                    return window.loadProductManagementStyles(targetSelector, styleId);
                }
                return false;
            };

            // 添加直接从源加载样式的方法
            window.loadProductManagementStyles = function(targetSelector = 'head', styleId = 'applied-product-management-styles') {
                // 检查是否已经应用过这个样式
                if (!document.getElementById(styleId)) {
                    // 检查是否可以直接调用函数
                    if (typeof addCustomStyles === 'function') {
                        // 清除已有的样式
                        const existingStyle = document.getElementById('product-management-styles');
                        if (existingStyle) {
                            existingStyle.remove();
                        }
                        
                        // 重新添加样式
                        addCustomStyles();
                        
                        // 获取新添加的样式
                        const newStyle = document.getElementById('product-management-styles');
                        if (newStyle) {
                            // 保存到window对象
                            window.productManagementStyles = newStyle.textContent;
                            
                            // 复制到目标位置
                            const styleElement = document.createElement('style');
                            styleElement.id = styleId;
                            styleElement.textContent = newStyle.textContent;
                            const target = document.querySelector(targetSelector);
                            if (target) {
                                target.appendChild(styleElement);
                                console.log('成功加载并应用产品管理样式');
                                return true;
                            }
                        }
                    } else {
                        console.error('无法加载样式，addCustomStyles函数不可用');
                    }
                } else {
                    console.log('样式已存在，无需重复加载');
                    return true;
                }
                return false;
            };
        };

        // 将函数移到全局作用域，便于其他页面调用
        function loadAndExtractStyles() {
            if (!window.loadedStyles) {
                window.loadedStyles = true;
                const styleFromProductManagement = document.getElementById('product-management-styles');
                if (styleFromProductManagement) {
                    window.productManagementStyles = styleFromProductManagement.textContent;
                } else {
                    // 如果样式还没被加载，则手动触发加载并提取
                    if (typeof addCustomStyles === 'function') {
                        addCustomStyles();
                        const style = document.getElementById('product-management-styles');
                        if (style) {
                            window.productManagementStyles = style.textContent;
                        }
                    }
                }
            }
            
            // 添加辅助函数用于应用样式
            window.applyProductManagementStyles = function(targetSelector = 'head', styleId = 'applied-product-management-styles') {
                if (window.productManagementStyles) {
                    // 检查是否已经应用过这个样式
                    if (!document.getElementById(styleId)) {
                        const styleElement = document.createElement('style');
                        styleElement.id = styleId;
                        styleElement.textContent = window.productManagementStyles;
                        const target = document.querySelector(targetSelector);
                        if (target) {
                            target.appendChild(styleElement);
                            console.log('成功应用产品管理样式');
                            return true;
                        } else {
                            console.warn('未找到目标元素，无法应用样式');
                        }
                    } else {
                        console.log('样式已存在，无需重复应用');
                        return true;
                    }
                } else {
                    console.warn('未找到产品管理样式，尝试直接加载');
                    // 尝试直接调用加载函数
                    return window.loadProductManagementStyles(targetSelector, styleId);
                }
                return false;
            };

            // 添加直接从源加载样式的方法
            window.loadProductManagementStyles = function(targetSelector = 'head', styleId = 'applied-product-management-styles') {
                // 检查是否已经应用过这个样式
                if (!document.getElementById(styleId)) {
                    // 检查是否可以直接调用函数
                    if (typeof addCustomStyles === 'function') {
                        // 清除已有的样式
                        const existingStyle = document.getElementById('product-management-styles');
                        if (existingStyle) {
                            existingStyle.remove();
                        }
                        
                        // 重新添加样式
                        addCustomStyles();
                        
                        // 获取新添加的样式
                        const newStyle = document.getElementById('product-management-styles');
                        if (newStyle) {
                            // 保存到window对象
                            window.productManagementStyles = newStyle.textContent;
                            
                            // 复制到目标位置
                            const styleElement = document.createElement('style');
                            styleElement.id = styleId;
                            styleElement.textContent = newStyle.textContent;
                            const target = document.querySelector(targetSelector);
                            if (target) {
                                target.appendChild(styleElement);
                                console.log('成功加载并应用产品管理样式');
                                return true;
                            }
                        }
                    } else {
                        console.error('无法加载样式，addCustomStyles函数不可用');
                    }
                } else {
                    console.log('样式已存在，无需重复加载');
                    return true;
                }
                return false;
            };
        }
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            padding: 0;
            background-color: #f8f9fa;
            color: #333;
        }
        .main-container {
            display: flex;
            min-height: 100vh;
        }
        .container {
            flex: 1;
            padding: 30px;
            transition: all 0.3s ease;
        }
        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
            color: white;
            padding: 0;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            position: relative;
            min-height: 100vh;
            height: 100vh;
            overflow: hidden;
            flex-shrink: 0;
            position: sticky;
            top: 0;
            display: flex;
            flex-direction: column;
        }
        .sidebar-header {
            text-align: center;
            padding: 20px 20px 15px 20px;
            margin-bottom: 0;
            border-bottom: 1px solid rgba(255,255,255,0.2);
            flex-shrink: 0;
        }
        .sidebar-menu {
            list-style: none;
            padding: 15px 0;
            margin: 0;
            flex: 1;
            overflow-y: auto;
            /* 美观的滚动条样式 - Firefox */
            scrollbar-width: thin;
            scrollbar-color: rgba(255,255,255,0.4) rgba(255,255,255,0.1);
        }
        /* WebKit浏览器的美观滚动条 - 菜单区域 */
        .sidebar-menu::-webkit-scrollbar {
            width: 8px;
        }
        .sidebar-menu::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.08);
            border-radius: 6px;
            margin: 10px 0;
            border: 1px solid rgba(255,255,255,0.05);
        }
        .sidebar-menu::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, rgba(255,255,255,0.4), rgba(255,255,255,0.25));
            border-radius: 6px;
            border: 1px solid rgba(255,255,255,0.1);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
        }
        .sidebar-menu::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, rgba(255,255,255,0.6), rgba(255,255,255,0.4));
            border-color: rgba(255,255,255,0.2);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            transform: scaleX(1.1);
        }
        .sidebar-menu::-webkit-scrollbar-thumb:active {
            background: linear-gradient(135deg, rgba(255,255,255,0.7), rgba(255,255,255,0.5));
            border-color: rgba(255,255,255,0.3);
            box-shadow: 0 2px 6px rgba(0,0,0,0.4);
        }
        /* 滚动条边角 */
        .sidebar-menu::-webkit-scrollbar-corner {
            background: transparent;
        }
        .sidebar-menu li {
            margin-bottom: 5px;
            padding: 0 20px;
        }
        .sidebar-menu a {
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            padding: 10px 12px;
            border-radius: 6px;
            transition: all 0.3s ease;
            font-size: 14px;
            position: relative;
            overflow: hidden;
        }
        .sidebar-menu a:hover {
            background-color: rgba(255,255,255,0.15);
            transform: translateX(5px);
        }
        .sidebar-menu a.active {
            background-color: rgba(255,255,255,0.2);
            transform: translateX(5px);
            border-left: 4px solid #fff;
            font-weight: 500;
        }
        .sidebar-menu i {
            margin-right: 12px;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 28px;
            color: #2c3e50;
            position: relative;
            display: inline-block;
            padding-bottom: 10px;
        }
        .header h1:after {
            content: '';
            position: absolute;
            width: 50%;
            height: 3px;
            background: linear-gradient(90deg, #4b6cb7, #182848);
            bottom: 0;
            left: 25%;
            border-radius: 2px;
        }
        .shop-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 25px;
        }
        .shop-card {
            border-radius: 12px;
            padding: 20px;
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            border: none;
            overflow: hidden;
            position: relative;
        }
        .shop-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .shop-card:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, #4b6cb7, #182848);
        }
        .shop-card h3 {
            margin-bottom: 15px;
            color: #2c3e50;
            font-size: 20px;
            font-weight: 600;
        }
        .shop-info {
            margin-bottom: 15px;
            color: #555;
        }
        .shop-info p {
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }
        .shop-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        .stat-item {
            background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
            padding: 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .stat-item:hover {
            transform: scale(1.03);
        }
        .stat-label {
            font-size: 13px;
            color: #666;
            margin-bottom: 5px;
        }
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }
        .shop-actions {
            margin-top: 15px;
            display: flex;
            justify-content: flex-end;
        }
        .action-btn {
            background: linear-gradient(90deg, #4b6cb7, #182848);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .action-btn:hover {
            opacity: 0.9;
            transform: translateY(-2px);
        }
        
        /* 新增样式 - 分页和搜索控件 */
        .control-panel {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .search-box {
            display: flex;
            flex: 1;
            max-width: 400px;
        }
        
        .search-box input {
            flex: 1;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 5px 0 0 5px;
            font-size: 14px;
        }
        
        .search-box button {
            padding: 10px 15px;
            background: linear-gradient(90deg, #4b6cb7, #182848);
            color: white;
            border: none;
            border-radius: 0 5px 5px 0;
            cursor: pointer;
        }
        
        .view-controls select {
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 30px;
            gap: 10px;
        }
        
        .page-btn {
            padding: 8px 15px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .page-btn:hover:not([disabled]) {
            background: #f0f0f0;
        }
        
        .page-btn.active {
            background: linear-gradient(90deg, #4b6cb7, #182848);
            color: white;
            border: none;
        }
        
        .page-btn[disabled] {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .view-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .view-switcher {
            display: flex;
            gap: 5px;
        }
        .view-btn {
            padding: 8px 15px;
            border: 1px solid #ddd;
            background: #fff;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        .view-btn.active {
            background: linear-gradient(90deg, #4b6cb7, #182848);
            color: white;
            border-color: transparent;
        }
        .shop-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        .shop-table th,
        .shop-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .shop-table th {
            background: linear-gradient(90deg, #4b6cb7, #182848);
            color: white;
            font-weight: 500;
        }
        .shop-table tr:hover {
            background-color: #f8f9fa;
        }
        .shop-table .action-btn {
            padding: 6px 12px;
            font-size: 14px;
        }
        .shop-count {
            margin: 15px 0;
            color: #666;
            font-size: 14px;
        }
        
        .no-shops {
            width: 100%;
            padding: 30px;
            text-align: center;
            background: white;
            border-radius: 10px;
            color: #666;
            font-size: 16px;
        }
        
        @media (max-width: 768px) {
            .shop-list {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            }
            
            .control-panel {
                flex-direction: column;
                gap: 15px;
            }
            
            .search-box {
                max-width: 100%;
            }
        }
        /* 添加用户信息区样式 */
        .user-info {
            margin-top: auto;
            padding: 15px 20px;
            background: rgba(0,0,0,0.15);
            border-top: 1px solid rgba(255,255,255,0.15);
            color: white;
            display: flex;
            flex-direction: column;
            gap: 8px;
            flex-shrink: 0;
        }
        .user-info .username {
            display: flex;
            align-items: center;
            font-size: 14px;
        }
        .user-info .username i {
            margin-right: 10px;
            font-size: 18px;
        }
        .user-profile-link {
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 4px;
            padding: 6px 8px;
            border-radius: 4px;
            background: rgba(255,255,255,0.05);
            color: white;
            text-decoration: none;
            font-size: 13px;
        }
        .user-profile-link:hover {
            transform: translateX(5px);
            background: rgba(255,255,255,0.1);
        }
        .user-profile-link i {
            margin-right: 10px;
            font-size: 16px;
        }
        .logout-btn {
            width: 100%;
            padding: 8px;
            background: rgba(255,255,255,0.1);
            color: white;
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 13px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .logout-btn i {
            margin-right: 8px;
            font-size: 16px;
        }
        .logout-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6e8efb, #a777e3);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: bold;
            margin-right: 10px;
        }
        .user-info-container {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .user-details {
            flex: 1;
        }
        .user-name {
            font-weight: bold;
            margin-bottom: 2px;
        }
        .user-role {
            font-size: 12px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>宜承管理系统</h2>
            </div>
            <ul class="sidebar-menu">
                <!-- 菜单项将基于用户权限动态生成 -->
            </ul>
            <!-- 添加用户信息区域 -->
            <div class="user-info">
                <div class="user-info-container">
                    <div class="user-avatar" id="userAvatar"></div>
                    <div class="user-details">
                        <div class="user-name" id="currentUsername">用户</div>
                        <div class="user-role" id="userRole">操作员</div>
                    </div>
                </div>
                <a class="user-profile-link" id="profileLink">个人中心</a>
                <a class="user-profile-link" id="accountManageLink" style="display: none;">账号与权限管理</a>
                <button class="logout-btn" id="logoutBtn">退出登录</button>
            </div>
        </div>
        <div class="container"></div>
    </div>
    <script>
        // 获取用户角色
        function getUserRole() {
            const cookies = document.cookie.split(';');
            const roleCookie = cookies.find(cookie => cookie.trim().startsWith('userRole='));
            return roleCookie ? decodeURIComponent(roleCookie.split('=')[1].trim()) : null;
        }
        
        // 获取用户名
        function getUsername() {
            const cookies = document.cookie.split(';');
            const usernameCookie = cookies.find(cookie => cookie.trim().startsWith('username='));
            return usernameCookie ? decodeURIComponent(usernameCookie.split('=')[1].trim()) : '用户';
        }
        
        // 获取登录时使用的用户名（user）
        function getUser() {
            const cookies = document.cookie.split(';');
            const userCookie = cookies.find(cookie => cookie.trim().startsWith('user='));
            return userCookie ? decodeURIComponent(userCookie.split('=')[1].trim()) : '用户';
        }
        
        // 获取用户真实姓名
        function getName() {
            const cookies = document.cookie.split(';');
            const nameCookie = cookies.find(cookie => cookie.trim().startsWith('name='));
            return nameCookie ? decodeURIComponent(nameCookie.split('=')[1].trim()) : '';
        }
        
        // 获取角色显示名称
        function getRoleName() {
            const cookies = document.cookie.split(';');
            const roleNameCookie = cookies.find(cookie => cookie.trim().startsWith('roleName='));
            
            // 拼音到中文的映射表
            const pinyinToChineseMap = {
                'guanliyuan': '管理员',
                'meigong': '美工',
                'yunying': '运营',
                'caigou': '采购',
                'caiwu': '财务',
                'kefu': '客服',
                'caozuoyuan': '操作员'
            };
            
            // 优先使用 roleName cookie
            if (roleNameCookie) {
                const roleNameValue = decodeURIComponent(roleNameCookie.split('=')[1].trim());
                console.log('使用roleName cookie:', roleNameValue);
                // 将拼音转换为中文显示
                return pinyinToChineseMap[roleNameValue] || roleNameValue;
            }
            
            // 如果没有roleName cookie，根据userRole生成一个
            const userRole = getUserRole();
            console.log('getRoleName中获取的角色:', userRole);
            if (!userRole) return '用户';
            
            // 定义角色映射表，方便维护和扩展
            const roleMap = {
                'admin': '管理员',
                'art': '美工',
                'yunying': '运营',
                'caigou': '采购',
                'caiwu': '财务',
                'kefu': '客服',
                'operator': '操作员'
            };
            
            const mappedRole = roleMap[userRole] || userRole;
            console.log('角色映射:', userRole, '->', mappedRole);
            return mappedRole;
        }
        
        // 退出登录
        function logout() {
            // 删除所有相关的cookie
            document.cookie = 'loginAuth=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            document.cookie = 'userRole=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            document.cookie = 'username=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            document.cookie = 'user=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            document.cookie = 'name=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            document.cookie = 'roleName=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            document.cookie = 'userPermissions=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            
            // 重定向到登录页面
            window.location.href = 'login.html';
        }

        // 在页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 先检查登录状态
            const cookies = document.cookie.split(';');
            const loginAuth = cookies.find(cookie => cookie.trim().startsWith('loginAuth='));
            if (!loginAuth || loginAuth.split('=')[1].trim() !== 'true') {
                console.log('未检测到登录状态，即将跳转登录页...');
                window.location.href = 'login.html';
                return;
            }
            
            const userRole = getUserRole();
            console.log('页面加载时读取到的角色:', userRole);
            
            // 显示当前用户名（使用登录时的user）
            const usernameElement = document.getElementById('currentUsername');
            if (usernameElement) {
                // 优先使用name字段(真实姓名)，如果没有则使用user(用户名)
                const nameFromCookie = getName();
                usernameElement.textContent = nameFromCookie || getUser();
            }
            
            // 设置头像显示用户名首字母
            const userAvatar = document.getElementById('userAvatar');
            if (userAvatar) {
                const displayName = getName() || getUser();
                userAvatar.textContent = displayName.charAt(0).toUpperCase();
            }
            
            // 显示用户角色
            const userRoleElement = document.getElementById('userRole');
            if (userRoleElement) {
                userRoleElement.textContent = getRoleName();
            }
            
            // 添加个人中心链接点击事件
            const profileLink = document.getElementById('profileLink');
            if (profileLink) {
                profileLink.addEventListener('click', function() {
                    window.location.href = 'userprofile.html';
                });
            }
            
            // 处理账号管理链接
            const accountManageLink = document.getElementById('accountManageLink');
            if (accountManageLink) {
                // 只有管理员才显示账号管理链接
                if (userRole === 'admin') {
                    accountManageLink.style.display = 'flex';
                    accountManageLink.addEventListener('click', function() {
                        window.location.href = 'account-management.html';
                    });
                }
            }
            
            // 添加退出登录按钮点击事件
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', logout);
            }
            
            // 获取所有导航链接
            const navLinks = document.querySelectorAll('.sidebar-menu a');
            const sidebarMenu = document.querySelector('.sidebar-menu');
            
            // 根据用户角色处理导航菜单
            console.log('当前角色:', userRole, typeof userRole);
            
            // 定义导航菜单项数据
            const menuItems = [
                { id: 'data-overview', name: '数据概览', icon: '📊' },
                { id: 'sales-stats', name: '销售统计', icon: '📈' },
                { id: 'sale-analytics', name: '销售数据分析', icon: '📊' },
                { id: 'shop-management', name: '店铺管理', icon: '🏪' },
                { id: 'spu-management', name: 'SPU管理', icon: '📦' },
                { id: 'promotion-data-management', name: '推广数据管理', icon: '📢' },
                { id: 'violation-management', name: '商品违规整改', icon: '⚠️' },
                { id: 'product-rectification-management', name: '商品整改项目管理', icon: '🔧' },
                { id: 'finance-management', name: '财务管理', icon: '💰' },
                { id: 'link-management', name: '链接管理', icon: '🔗' },
                { id: 'team-management', name: '团队管理', icon: '👥' },
                { id: 'spu-trend', name: 'SPU全年销售趋势', icon: '📉' },
                { id: 'art-task-management', name: '美工任务管理', icon: '🎨' }
            ];
            
            // 获取用户权限
            let userPermissions = [];
            
            // 获取用户权限，仅从cookie中获取
            const cookiesArray = document.cookie.split(';');
            const userPermissionsCookie = cookiesArray.find(cookie => cookie.trim().startsWith('userPermissions='));
            if (userPermissionsCookie) {
                try {
                    // 获取cookie值
                    let cookieValue = userPermissionsCookie.split('=')[1].trim();
                    console.log('原始Cookie格式:', cookieValue);
                    
                    // 尝试使用JSON解析
                    try {
                        userPermissions = JSON.parse(cookieValue);
                        console.log('成功直接解析权限:', userPermissions);
                    } catch (jsonError) {
                        console.error('JSON解析权限Cookie失败，尝试其他方式:', jsonError);
                        
                        // 特殊格式处理：处理形如 "[\"data-overview\"\054 \"sales-stats\"...]" 的格式
                        cookieValue = decodeURIComponent(cookieValue);
                        console.log('解码后Cookie值:', cookieValue);
                        
                        // 移除前后引号
                        cookieValue = cookieValue.replace(/^"|"$/g, '');
                        
                        if (cookieValue.startsWith('[') && cookieValue.endsWith(']')) {
                            // 替换 \054 为逗号，并移除多余空格
                            cookieValue = cookieValue
                                .replace(/\\054\s*/g, ',')
                                // 替换转义的引号 \" 为普通引号 "
                                .replace(/\\"/g, '"');
                                
                            console.log('处理后的Cookie:', cookieValue);
                            
                            try {
                                userPermissions = JSON.parse(cookieValue);
                                console.log('成功用JSON解析处理后的cookie:', userPermissions);
                            } catch (specialFormatError) {
                                console.error('特殊格式解析失败，进行手动解析:', specialFormatError);
                                
                                // 特殊处理单个权限的情况，例如 ["art-task-management"]
                                if (cookieValue === '["art-task-management"]' || cookieValue === '[art-task-management]') {
                                    userPermissions = ['art-task-management'];
                                    console.log('特殊处理单个权限:', userPermissions);
                                } else {
                                    try {
                                        // 第二种方法: 尝试按指定格式详细解析
                                        // 提取形如 "data-overview" 的元素
                                        const regex = /"([^"]+)"/g;
                                        const matches = cookieValue.match(regex);
                                        console.log('正则匹配结果:', matches);
                                        
                                        if (matches) {
                                            userPermissions = matches.map(match => match.replace(/"/g, ''));
                                            console.log('正则匹配解析权限:', userPermissions);
                                        } else {
                                            // 最后尝试: 按引号分割
                                            const strippedValue = cookieValue.substring(1, cookieValue.length - 1);
                                            console.log('去除[]后的值:', strippedValue);
                                            
                                            // 简化解析逻辑，处理可能的多种格式
                                            userPermissions = strippedValue.split(',')
                                                .map(item => item.trim().replace(/^"/, '').replace(/"$/, ''))
                                                .filter(item => item.length > 0);
                                            console.log('逗号分割解析权限:', userPermissions);
                                        }
                                    } catch (finalError) {
                                        console.error('所有解析方法都失败:', finalError);
                                        userPermissions = [];
                                    }
                                }
                            }
                        } else if (cookieValue === 'art-task-management') {
                            // 特殊情况：如果cookie值就是一个权限字符串
                            userPermissions = ['art-task-management'];
                            console.log('直接使用单个权限字符串:', userPermissions);
                        }
                    }
                    
                    // 确保权限是数组
                    if (!Array.isArray(userPermissions)) {
                        console.warn('解析后的 userPermissions 不是一个数组，重置为空数组');
                        userPermissions = [];
                    }
                    
                    // 确保art角色至少有美工任务管理权限
                    const userRole = cookiesArray.find(cookie => cookie.trim().startsWith('userRole='));
                    if (userRole && userRole.split('=')[1].trim() === 'art') {
                        console.log('当前用户是美工角色，确保拥有美工任务管理权限');
                        if (!userPermissions.includes('art-task-management')) {
                            userPermissions.push('art-task-management');
                        }
                    }
                } catch (e) {
                    console.error('解析权限Cookie完全失败:', e);
                    // 检查是否为美工角色，默认赋予美工任务管理权限
                    const userRole = cookiesArray.find(cookie => cookie.trim().startsWith('userRole='));
                    if (userRole && userRole.split('=')[1].trim() === 'art') {
                        console.log('权限解析失败但是用户是美工角色，设置美工任务管理权限');
                        userPermissions = ['art-task-management'];
                    } else if (userRole && userRole.split('=')[1].trim() === 'admin') {
                        console.log('权限解析失败但是用户是管理员角色，设置所有权限');
                        userPermissions = [
                            'data-overview', 'sales-stats', 'sale-analytics', 'shop-management',
                            'spu-management', 'violation-management', 'product-rectification-management', 'finance-management',
                            'link-management', 'team-management', 'spu-trend', 'art-task-management',
                            'promotion-data-management'
                        ];
                    } else {
                        userPermissions = [];
                    }
                }
            } else {
                console.warn('用户没有权限设置');
                // 检查是否为美工角色，默认赋予美工任务管理权限
                const userRole = cookiesArray.find(cookie => cookie.trim().startsWith('userRole='));
                if (userRole && userRole.split('=')[1].trim() === 'art') {
                    console.log('用户是美工角色，设置默认美工任务管理权限');
                    userPermissions = ['art-task-management'];
                } else if (userRole && userRole.split('=')[1].trim() === 'admin') {
                    console.log('用户是管理员角色，设置所有权限');
                    userPermissions = [
                        'data-overview', 'sales-stats', 'sale-analytics', 'shop-management',
                        'spu-management', 'violation-management', 'product-rectification-management', 'finance-management',
                        'link-management', 'team-management', 'spu-trend', 'art-task-management',
                        'promotion-data-management'
                    ];
                } else {
                    userPermissions = [];
                }
            }
            
            console.log('最终用户权限:', userPermissions);
            
            // 根据权限重新构建菜单
            sidebarMenu.innerHTML = '';
            
            // 只添加用户有权限的菜单项
            menuItems.forEach(item => {
                if (userPermissions.includes(item.id)) {
                    const li = document.createElement('li');
                    li.innerHTML = `<a href="#"><i>${item.icon}</i>${item.name}</a>`;
                    sidebarMenu.appendChild(li);
                }
            });
            
            // 重新获取导航链接（因为已经被替换）
            const updatedNavLinks = document.querySelectorAll('.sidebar-menu a');
            
            // 为每个导航链接添加点击事件
            updatedNavLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 移除所有导航项的激活状态
                    updatedNavLinks.forEach(item => item.classList.remove('active'));
                    
                    // 为当前点击的导航项添加激活状态
                    this.classList.add('active');
                    
                    // 清空右侧内容区域
                    const container = document.querySelector('.container');
                    container.innerHTML = '';
                    
                    // 根据菜单项加载不同的内容
                    const menuText = this.textContent.trim();
                    if (menuText.includes('数据概览')) {
                        // 动态加载并执行generateDashboard.js
                        const script = document.createElement('script');
                        script.src = 'generateDashboard.js';
                        script.onload = function() {
                            generateDashboard();
                        };
                        document.body.appendChild(script);
                    } else if (menuText.includes('店铺管理')) {
                        // 动态加载并执行generateShopCards.js
                        const script = document.createElement('script');
                        script.src = 'generateShopCards.js';
                        script.onload = function() {
                            updateShopList();
                        };
                        document.body.appendChild(script);
                    } else if (menuText.includes('销售统计')) {
                        // 动态加载并执行generateSalesStats.js
                        const script = document.createElement('script');
                        script.src = 'generateSalesStats.js';
                        script.onload = function() {
                            generateSalesStats();
                        };
                        document.body.appendChild(script);
                    } else if (menuText.includes('销售数据分析')) {
                        // 动态加载并执行generateSaleAnalytics.js
                        const script = document.createElement('script');
                        script.src = 'generateSaleAnalytics.js';
                        script.onload = function() {
                            if (typeof generateSaleAnalytics === 'function') {
                                generateSaleAnalytics();
                            } else {
                                console.error('generateSaleAnalytics函数未找到');
                            }
                        };
                        document.body.appendChild(script);
                    } else if (menuText.includes('SPU管理')) {
                        // 动态加载并执行generateProductManagement.js
                        const script = document.createElement('script');
                        script.src = 'generateProductManagement.js';
                        script.onload = function() {
                            generateProductManagement();
                        };
                        document.body.appendChild(script);
                    } else if (menuText.includes('推广数据管理')) {
                        // 动态加载并执行generatePromotionDataManagement.js
                        const script = document.createElement('script');
                        script.src = 'generatePromotionDataManagement.js';
                        script.onload = function() {
                            generatePromotionDataManagement();
                        };
                        document.body.appendChild(script);
                    } else if (menuText.includes('商品违规整改')) {
                        // 动态加载并执行generateViolationManagement.js
                        const script = document.createElement('script');
                        script.src = 'generateViolationManagement.js';
                        script.onload = function() {
                            generateViolationManagement();
                        };
                        document.body.appendChild(script);
                    } else if (menuText.includes('商品整改项目管理')) {
                        // 动态加载并执行generateProductRectification.js
                        const script = document.createElement('script');
                        script.src = '商品整改项目/generateProductRectification.js';
                        script.onload = function() {
                            generateProductRectification();
                        };
                        document.body.appendChild(script);
                    } else if (menuText.includes('财务管理')) {
                        // 动态加载并执行generateFinanceManagement.js
                        const script = document.createElement('script');
                        script.src = 'generateFinanceManagement.js';
                        script.onload = function() {
                            generateFinanceManagement();
                        };
                        document.body.appendChild(script);
                    } else if (menuText.includes('链接管理')) {
                        // 动态加载并执行generateLinkManagement.js
                        const script = document.createElement('script');
                        script.src = 'generateLinkManagement.js';
                        script.onload = function() {
                            generateLinkManagement();
                        };
                        document.body.appendChild(script);
                    } else if (menuText.includes('团队管理')) {
                        // 动态加载并执行generateTeamManagement.js
                        const script = document.createElement('script');
                        script.src = 'generateTeamManagement.js';
                        script.onload = function() {
                            generateTeamManagement();
                        };
                        document.body.appendChild(script);
                    } else if (menuText.includes('SPU全年销售趋势')) {
                        // 重定向到SPU分析页面
                        window.open('/static/spu-analytics.html', '_blank');
                    } else if (menuText.includes('美工任务管理')) {
                        // 动态加载并执行 generateArtTaskManagement.js
                        const script = document.createElement('script');
                        script.src = 'generateArtTaskManagement.js';
                        script.onload = function() {
                            generateArtTaskManagement();
                        };
                        document.body.appendChild(script);
                    }
                });
            });
            
            // 默认激活第一个导航项
            if (updatedNavLinks.length > 0) {
                updatedNavLinks[0].click();
            }
        });
    </script>
    <!-- 引入Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>