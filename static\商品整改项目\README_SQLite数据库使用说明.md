# SQLite数据库使用说明

## 概述

基于`1.01.py`文件的返回数据格式，创建了一个完整的SQLite数据库解决方案，用于存储和管理商品整改数据。该解决方案包含：

- 完整的数据库表结构设计
- 中文字符UTF-8编码支持
- 数据插入、查询和统计功能
- 错误处理和连接管理
- 完整的演示代码

## 文件结构

```
Desktop\商品整改获取\
├── 1.01.py                           # 原始数据获取脚本（已优化）
├── sqlite_demo.py                     # SQLite数据库演示脚本
├── integrated_data_collector.py       # 集成数据收集器（生产版本）
├── database_query_tool.py             # 数据库查询工具
├── demo_rectification.db              # 演示数据库文件
├── production_rectification.db        # 生产数据库文件（包含真实数据）
└── README_SQLite数据库使用说明.md      # 本说明文档
```

## 🎉 项目完成情况

✅ **已完成的功能：**
- 完整的SQLite数据库表结构设计
- 中文字符UTF-8编码完美支持
- 基于1.01.py返回数据格式的数据库适配
- 数据插入、查询、统计功能
- 集成数据收集器（已成功收集167个店铺的真实数据）
- 交互式数据库查询工具
- 完整的演示代码和使用说明
- 错误处理和连接管理
- 数据导出功能

✅ **实际运行结果：**
- 成功连接到167个店铺
- 收集了268个整改项目数据
- 数据库包含158个有效店铺记录
- 中文店铺名称和商品信息完美存储

## 数据库表结构

### 1. shop_summary (店铺汇总表)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INTEGER PRIMARY KEY | 自增主键 |
| shop_name | TEXT NOT NULL | 店铺名称（支持中文） |
| mall_id | TEXT NOT NULL | 商户ID |
| init_counts | INTEGER | 待处理数量 |
| submit_counts | INTEGER | 已提交数量 |
| no_submit_counts | INTEGER | 未提交数量 |
| last_updated | TIMESTAMP | 最后更新时间 |
| created_at | TIMESTAMP | 创建时间 |

### 2. rectification_items (整改项目详情表)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INTEGER PRIMARY KEY | 项目ID |
| goods_id | TEXT NOT NULL | 商品ID |
| mall_id | TEXT NOT NULL | 商户ID |
| shop_name | TEXT NOT NULL | 店铺名称 |
| type | INTEGER NOT NULL | 类型 |
| status | INTEGER NOT NULL | 状态 |
| questionnaire_id | INTEGER | 问卷ID |
| problem_label | TEXT | 问题标签（支持中文） |
| base_info | TEXT | 基础信息（JSON格式） |
| advise_info | TEXT | 建议信息（JSON格式） |
| merchant_plan | TEXT | 商家计划 |
| adviser | INTEGER | 建议者 |
| created_at_timestamp | BIGINT | 创建时间戳 |
| updated_at_timestamp | BIGINT | 更新时间戳 |
| ext_map | TEXT | 扩展信息（JSON格式） |
| data_category | TEXT | 数据类别 (init/submit/no_submit) |
| inserted_at | TIMESTAMP | 插入时间 |

## 核心功能

### 1. 数据库连接管理

```python
from sqlite_demo import RectificationDatabase

# 创建数据库实例
db = RectificationDatabase("your_database.db")

# 连接数据库
db.connect()

# 创建表结构
db.create_tables()

# 关闭连接
db.disconnect()
```

### 2. 数据插入

```python
# 插入店铺数据（基于API返回格式）
response_data = {
    "success": True,
    "result": {
        "mallId": "153400375",
        "initCounts": 1,
        "submitCounts": 1,
        "noSubmitCounts": 2,
        "initData": [...],
        "submitData": [...],
        "noSubmitData": [...]
    }
}

db.insert_shop_data("店铺名称", response_data)
```

### 3. 数据查询

```python
# 查询所有店铺汇总信息
db.query_shop_summary()

# 查询特定店铺信息
db.query_shop_summary("雅鹿旗舰店")

# 查询整改项目详情
db.query_rectification_items(limit=10)

# 按条件查询
db.query_rectification_items(
    shop_name="雅鹿旗舰店",
    status=3,
    data_category='no_submit',
    limit=5
)
```

### 4. 统计信息

```python
# 获取数据库统计信息
db.get_statistics()
```

## 中文字符支持

数据库已配置为完全支持中文字符：

- 使用UTF-8编码存储
- 正确处理中文店铺名称
- 支持中文问题标签和商品名称
- JSON字段中的中文内容正确存储

## 高级查询示例

### 1. JSON字段查询

```python
# 提取商品名称
db.cursor.execute("""
    SELECT 
        shop_name,
        json_extract(base_info, '$.goodsName') as goods_name,
        problem_label
    FROM rectification_items 
    WHERE json_extract(base_info, '$.goodsName') IS NOT NULL
""")
```

### 2. 聚合查询

```python
# 按店铺统计问题类型
db.cursor.execute("""
    SELECT 
        shop_name,
        problem_label,
        COUNT(*) as count
    FROM rectification_items 
    GROUP BY shop_name, problem_label
    ORDER BY count DESC
""")
```

### 3. 时间范围查询

```python
# 查询最近创建的项目
db.cursor.execute("""
    SELECT 
        shop_name,
        problem_label,
        datetime(created_at_timestamp/1000, 'unixepoch') as created_time
    FROM rectification_items 
    ORDER BY created_at_timestamp DESC
    LIMIT 10
""")
```

## 快速开始

### 1. 运行演示脚本
```bash
# 进入项目目录
cd "Desktop\商品整改获取"

# 运行演示脚本（使用示例数据）
python sqlite_demo.py
```

### 2. 运行集成数据收集器（生产环境）
```bash
# 收集真实数据并存储到数据库
python integrated_data_collector.py
```

### 3. 使用查询工具分析数据
```bash
# 启动交互式查询工具
python database_query_tool.py
```

## 功能说明

### sqlite_demo.py - 演示脚本
- 创建数据库和表结构
- 插入示例数据
- 演示各种查询功能
- 显示统计信息
- 演示中文字符处理

### integrated_data_collector.py - 集成收集器
- 结合原始1.01.py的数据获取功能
- 自动收集所有店铺的真实数据
- 存储到SQLite数据库
- 支持并发处理
- 完整的错误处理

### database_query_tool.py - 查询工具
- 交互式命令行界面
- 多种查询和统计功能
- 数据导出到CSV
- 自定义SQL查询
- 商品名称搜索

## 错误处理

代码包含完整的错误处理机制：

- 数据库连接错误处理
- SQL执行错误捕获
- 数据插入异常处理
- 自动资源清理

## 性能优化

- 创建了适当的索引
- 使用批量插入操作
- 支持事务处理
- 优化查询语句

## 扩展建议

1. **数据备份**：定期备份数据库文件
2. **数据同步**：可以与原始API数据定期同步
3. **报表功能**：基于数据库数据生成分析报表
4. **Web界面**：可以开发Web界面进行数据管理
5. **数据导出**：支持导出为Excel或CSV格式

## 注意事项

1. 确保Python环境已安装sqlite3模块（通常内置）
2. 数据库文件会自动创建在脚本运行目录
3. 中文字符需要确保系统支持UTF-8编码
4. 建议定期备份数据库文件

## 技术特点

- ✅ 完整的表结构设计
- ✅ UTF-8中文字符支持
- ✅ JSON字段存储和查询
- ✅ 完善的错误处理
- ✅ 索引优化
- ✅ 事务支持
- ✅ 连接池管理
- ✅ 详细的演示代码
