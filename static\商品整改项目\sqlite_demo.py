#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQLite数据库演示脚本
基于1.01.py文件的返回数据格式创建数据库和演示操作
"""

import sqlite3
import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional

class RectificationDatabase:
    """商品整改数据库管理类"""
    
    def __init__(self, db_path: str = "rectification_data.db"):
        """
        初始化数据库连接
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.connection = None
        self.cursor = None
        
    def connect(self):
        """建立数据库连接"""
        try:
            # 确保使用UTF-8编码
            self.connection = sqlite3.connect(
                self.db_path, 
                check_same_thread=False,
                isolation_level=None  # 自动提交模式
            )
            # 设置文本工厂为str，确保中文字符正确处理
            self.connection.text_factory = str
            self.cursor = self.connection.cursor()
            
            # 启用外键约束
            self.cursor.execute("PRAGMA foreign_keys = ON")
            print(f"✅ 成功连接到数据库: {self.db_path}")
            
        except sqlite3.Error as e:
            print(f"❌ 数据库连接失败: {e}")
            raise
    
    def disconnect(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("✅ 数据库连接已关闭")
    
    def create_tables(self):
        """创建数据库表结构"""
        try:
            # 创建店铺汇总表
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS shop_summary (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    shop_name TEXT NOT NULL UNIQUE,
                    mall_id TEXT NOT NULL,
                    init_counts INTEGER DEFAULT 0,
                    submit_counts INTEGER DEFAULT 0,
                    no_submit_counts INTEGER DEFAULT 0,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建整改数据详情表
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS rectification_items (
                    id INTEGER PRIMARY KEY,
                    goods_id TEXT NOT NULL,
                    mall_id TEXT NOT NULL,
                    shop_name TEXT NOT NULL,
                    type INTEGER NOT NULL,
                    status INTEGER NOT NULL,
                    questionnaire_id INTEGER DEFAULT 0,
                    problem_label TEXT,
                    base_info TEXT,
                    advise_info TEXT,
                    merchant_plan TEXT,
                    adviser INTEGER,
                    created_at_timestamp BIGINT,
                    updated_at_timestamp BIGINT,
                    ext_map TEXT,
                    data_category TEXT NOT NULL CHECK(data_category IN ('init', 'submit', 'no_submit')),
                    inserted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (shop_name) REFERENCES shop_summary(shop_name)
                )
            """)
            
            # 创建索引以提高查询性能
            self.cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_shop_name ON rectification_items(shop_name)
            """)
            
            self.cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_goods_id ON rectification_items(goods_id)
            """)
            
            self.cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_status ON rectification_items(status)
            """)
            
            self.cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_data_category ON rectification_items(data_category)
            """)
            
            print("✅ 数据库表结构创建成功")
            
        except sqlite3.Error as e:
            print(f"❌ 创建表失败: {e}")
            raise
    
    def show_schema(self):
        """显示数据库表结构"""
        print("\n" + "="*60)
        print("📋 数据库表结构信息")
        print("="*60)
        
        # 获取所有表名
        self.cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = self.cursor.fetchall()
        
        for table in tables:
            table_name = table[0]
            print(f"\n🏷️  表名: {table_name}")
            print("-" * 40)
            
            # 获取表结构
            self.cursor.execute(f"PRAGMA table_info({table_name})")
            columns = self.cursor.fetchall()
            
            print(f"{'列名':<20} {'类型':<15} {'是否为空':<8} {'默认值':<15}")
            print("-" * 60)
            
            for col in columns:
                col_name = col[1]
                col_type = col[2]
                not_null = "NOT NULL" if col[3] else "NULL"
                default_val = col[4] if col[4] else ""
                print(f"{col_name:<20} {col_type:<15} {not_null:<8} {default_val:<15}")
    
    def insert_shop_data(self, shop_name: str, response_data: Dict[str, Any]):
        """
        插入店铺数据到数据库
        
        Args:
            shop_name: 店铺名称
            response_data: API返回的数据
        """
        try:
            if not response_data.get('success'):
                print(f"⚠️  店铺 {shop_name} 数据不成功，跳过插入")
                return
            
            result = response_data.get('result', {})
            mall_id = result.get('mallId', '')
            
            # 插入或更新店铺汇总信息
            self.cursor.execute("""
                INSERT OR REPLACE INTO shop_summary 
                (shop_name, mall_id, init_counts, submit_counts, no_submit_counts, last_updated)
                VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (
                shop_name,
                mall_id,
                result.get('initCounts', 0),
                result.get('submitCounts', 0),
                result.get('noSubmitCounts', 0)
            ))
            
            # 插入详细数据
            data_categories = [
                ('initData', 'init'),
                ('submitData', 'submit'),
                ('noSubmitData', 'no_submit')
            ]
            
            for data_key, category in data_categories:
                items = result.get(data_key, [])
                for item in items:
                    self.cursor.execute("""
                        INSERT OR REPLACE INTO rectification_items
                        (id, goods_id, mall_id, shop_name, type, status, questionnaire_id,
                         problem_label, base_info, advise_info, merchant_plan, adviser,
                         created_at_timestamp, updated_at_timestamp, ext_map, data_category)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        item.get('id'),
                        item.get('goodsId'),
                        item.get('mallId'),
                        shop_name,
                        item.get('type'),
                        item.get('status'),
                        item.get('questionnaireId'),
                        item.get('problemLabel'),
                        json.dumps(item.get('baseInfo'), ensure_ascii=False) if item.get('baseInfo') else None,
                        json.dumps(item.get('adviseInfo'), ensure_ascii=False) if item.get('adviseInfo') else None,
                        item.get('merchantPlan'),
                        item.get('adviser'),
                        item.get('createdAt'),
                        item.get('updatedAt'),
                        json.dumps(item.get('extMap'), ensure_ascii=False) if item.get('extMap') else None,
                        category
                    ))
            
            print(f"✅ 店铺 {shop_name} 数据插入成功")
            
        except sqlite3.Error as e:
            print(f"❌ 插入店铺 {shop_name} 数据失败: {e}")
            raise
    
    def query_shop_summary(self, shop_name: Optional[str] = None):
        """
        查询店铺汇总信息
        
        Args:
            shop_name: 店铺名称，如果为None则查询所有店铺
        """
        try:
            if shop_name:
                self.cursor.execute("""
                    SELECT shop_name, mall_id, init_counts, submit_counts, no_submit_counts, last_updated
                    FROM shop_summary WHERE shop_name = ?
                """, (shop_name,))
            else:
                self.cursor.execute("""
                    SELECT shop_name, mall_id, init_counts, submit_counts, no_submit_counts, last_updated
                    FROM shop_summary ORDER BY last_updated DESC
                """)
            
            results = self.cursor.fetchall()
            
            print(f"\n📊 店铺汇总信息 {'(' + shop_name + ')' if shop_name else '(所有店铺)'}")
            print("-" * 80)
            print(f"{'店铺名称':<20} {'商户ID':<15} {'待处理':<8} {'已提交':<8} {'未提交':<8} {'更新时间':<20}")
            print("-" * 80)
            
            for row in results:
                print(f"{row[0]:<20} {row[1]:<15} {row[2]:<8} {row[3]:<8} {row[4]:<8} {row[5]:<20}")
            
            return results
            
        except sqlite3.Error as e:
            print(f"❌ 查询店铺汇总信息失败: {e}")
            return []

    def query_rectification_items(self, shop_name: Optional[str] = None,
                                 status: Optional[int] = None,
                                 data_category: Optional[str] = None,
                                 limit: int = 10):
        """
        查询整改项目详情

        Args:
            shop_name: 店铺名称
            status: 状态筛选
            data_category: 数据类别 ('init', 'submit', 'no_submit')
            limit: 返回记录数限制
        """
        try:
            query = """
                SELECT id, goods_id, shop_name, problem_label, status, data_category,
                       datetime(created_at_timestamp/1000, 'unixepoch') as created_time
                FROM rectification_items WHERE 1=1
            """
            params = []

            if shop_name:
                query += " AND shop_name = ?"
                params.append(shop_name)

            if status is not None:
                query += " AND status = ?"
                params.append(status)

            if data_category:
                query += " AND data_category = ?"
                params.append(data_category)

            query += " ORDER BY created_at_timestamp DESC LIMIT ?"
            params.append(limit)

            self.cursor.execute(query, params)
            results = self.cursor.fetchall()

            print(f"\n📋 整改项目详情 (最多{limit}条)")
            print("-" * 100)
            print(f"{'ID':<10} {'商品ID':<15} {'店铺名称':<15} {'问题标签':<15} {'状态':<6} {'类别':<10} {'创建时间':<20}")
            print("-" * 100)

            for row in results:
                print(f"{row[0]:<10} {row[1]:<15} {row[2]:<15} {row[3]:<15} {row[4]:<6} {row[5]:<10} {row[6]:<20}")

            return results

        except sqlite3.Error as e:
            print(f"❌ 查询整改项目详情失败: {e}")
            return []

    def get_statistics(self):
        """获取数据库统计信息"""
        try:
            # 总体统计
            self.cursor.execute("SELECT COUNT(*) FROM shop_summary")
            total_shops = self.cursor.fetchone()[0]

            self.cursor.execute("SELECT COUNT(*) FROM rectification_items")
            total_items = self.cursor.fetchone()[0]

            # 按类别统计
            self.cursor.execute("""
                SELECT data_category, COUNT(*)
                FROM rectification_items
                GROUP BY data_category
            """)
            category_stats = self.cursor.fetchall()

            # 按状态统计
            self.cursor.execute("""
                SELECT status, COUNT(*)
                FROM rectification_items
                GROUP BY status
            """)
            status_stats = self.cursor.fetchall()

            print(f"\n📈 数据库统计信息")
            print("=" * 50)
            print(f"总店铺数: {total_shops}")
            print(f"总整改项目数: {total_items}")

            print(f"\n按类别统计:")
            for category, count in category_stats:
                print(f"  {category}: {count}")

            print(f"\n按状态统计:")
            for status, count in status_stats:
                print(f"  状态{status}: {count}")

        except sqlite3.Error as e:
            print(f"❌ 获取统计信息失败: {e}")


def create_sample_data():
    """创建示例数据"""
    return {
        "success": True,
        "errorCode": 1000000,
        "errorMsg": None,
        "result": {
            "mallId": "153400375",
            "initCounts": 1,
            "submitCounts": 1,
            "noSubmitCounts": 2,
            "initData": [
                {
                    "id": 1392817,
                    "goodsId": "765687485979",
                    "mallId": "153400375",
                    "type": 2,
                    "status": 0,
                    "questionnaireId": 0,
                    "problemLabel": "材质描述不符",
                    "baseInfo": "{\"skuUrl\":\"https://img.pddpic.com/gaudit-image/2025-06-21/efaa07476769a07053a0acc38b695b1b.jpeg\",\"goodsId\":\"765687485979\",\"subtitle\":\"商品基础信息\",\"goodsName\":\"雅鹿5双装肤色丝袜女春夏超薄款防勾肉色高筒过膝大腿防滑美腿袜\"}",
                    "adviseInfo": "{\"材质描述不符\":{\"advice\":[\"修正商品防滑防勾丝功能宣传与实际效果不符的问题\",\"调整材质描述与实物质量的一致性\",\"规范SKU颜色及数量标注避免发错货\"]}}",
                    "merchantPlan": None,
                    "adviser": 1,
                    "createdAt": 1751541755111,
                    "updatedAt": 1751541755111,
                    "extMap": "{\"afsProblemLabels\":[\"商详内容-商品参数声明「防滑防勾丝」，用户反馈「不防滑」\",\"sku展示颜色为「灰色」，用户反馈「发错颜色」\",\"sku展示「5双装」，用户反馈「收到10双及3双未收到」\"],\"linkOrderSn\":\"0\",\"createRectificationSource\":\"hive\",\"checkId\":-1,\"diffTime\":119681000}"
                }
            ],
            "submitData": [
                {
                    "id": 1383449,
                    "goodsId": "649538212470",
                    "mallId": "153400375",
                    "type": 2,
                    "status": 1,
                    "questionnaireId": 0,
                    "problemLabel": "质量问题",
                    "baseInfo": "{\"skuUrl\":\"https://img.pddpic.com/gaudit-image/2024-09-12/dd7447be3065c562c44d423a4591e1e9.jpeg\",\"goodsId\":\"649538212470\",\"subtitle\":\"商品基础信息\",\"goodsName\":\"雅鹿0D黑丝袜女防勾不掉档春秋款光腿神器超薄高透黑丝性感连裤袜\"}",
                    "adviseInfo": "{\"质量问题\":{\"advice\":[\"修正商品名称中「防勾不掉档」的功能性描述，避免与实际产品易勾丝、易破损的特性矛盾\",\"调整商品参数及图片中关于材质（包芯丝/超细氨纶）的说明，确保与实物触感及耐用性一致\",\"核对sku颜色标注（如浅黑、炭黑）与实际发货颜色的一致性\"]}}",
                    "merchantPlan": "我们会修正商品名称中「防勾不掉档」的功能性描述，避免与实际产品易勾丝、易破损的特性矛盾；调整商品参数及图片中关于材质（包芯丝/超细氨纶）的说明，确保与实物触感及耐用性一致；核对sku颜色标注（如浅黑、炭黑）与实际发货颜色的一致性",
                    "adviser": 1,
                    "createdAt": 1751496838101,
                    "updatedAt": 1751681174538,
                    "extMap": "{\"afsProblemLabels\":[\"商品名称声明「防勾不掉档」，用户反馈「刚穿上就勾丝」、「勾丝严重」、「一穿就破」\",\"商详内容-商品参数声明材质为「包芯丝」，用户反馈「材质粗糙」、「勾丝严重」\",\"sku展示颜色为「高透浅黑」，用户反馈「颜色发错」、「实际收到黑色」\"],\"linkOrderSn\":\"0\",\"createRectificationSource\":\"hive\",\"checkId\":-1}"
                }
            ],
            "noSubmitData": [
                {
                    "id": 1222473,
                    "goodsId": "763652719074",
                    "mallId": "153400375",
                    "type": 2,
                    "status": 3,
                    "questionnaireId": 0,
                    "problemLabel": "材质描述不符",
                    "baseInfo": "{\"skuUrl\":\"https://img.pddpic.com/gaudit-image/2025-06-17/b28c0573dc28a95e0ac197476e2370d5.jpeg\",\"goodsId\":\"763652719074\",\"subtitle\":\"商品基础信息\",\"goodsName\":\"肤色丝袜女超薄隐形透明新款菠萝耐穿肉色自然裸感防勾0D光腿神器\"}",
                    "adviseInfo": "{\"材质描述不符\":{\"advice\":[\"修正商品尺寸描述准确性，确保推荐尺码与实际商品尺寸一致\",\"调整颜色描述与实际商品颜色一致，避免色差误导\",\"优化材质与工艺，确保防勾丝功能与宣传一致\"]}}",
                    "merchantPlan": None,
                    "adviser": 1,
                    "createdAt": 1751309661605,
                    "updatedAt": 1751568864417,
                    "extMap": "{\"afsProblemLabels\":[\"商品名称展示为「防勾0D光腿神器」，用户反馈「刚穿就勾丝了」、「容易勾丝」\",\"商详内容-商品参数声明「防勾丝」，用户反馈「破了个洞还挑丝了」、「一撑就变形了」\",\"sku展示「70-120斤穿」或「115-145斤穿」，用户反馈「发错了码」、「推荐的尺码小了」、「太短了根本穿不上去\"]\",\"linkOrderSn\":\"0\",\"checkId\":-1}"
                },
                {
                    "id": 1189020,
                    "goodsId": "617321818173",
                    "mallId": "153400375",
                    "type": 2,
                    "status": 3,
                    "questionnaireId": 0,
                    "problemLabel": "材质描述不符",
                    "baseInfo": "{\"skuUrl\":\"https://img.pddpic.com/gaudit-image/2024-05-24/13c3cdb42f4feea78227128d6b8b31ca.jpeg\",\"goodsId\":\"617321818173\",\"subtitle\":\"商品基础信息\",\"goodsName\":\"黑色丝袜超薄款女春夏季0D超轻薄高透光隐形光腿神器防勾丝连裤袜\"}",
                    "adviseInfo": "{\"材质描述不符\":{\"advice\":[\"修正商品参数中关于材质和厚薄的描述，确保与实际产品一致\",\"调整SKU中D数规格的标注，避免消费者对透明度产生误解\",\"优化商品图片与实物颜色、厚薄的匹配度，避免色差和透光度差异\"]}}",
                    "merchantPlan": None,
                    "adviser": 1,
                    "createdAt": 1751293501607,
                    "updatedAt": 1751552707635,
                    "extMap": "{\"afsProblemLabels\":[\"商详内容-图片声明「0D超薄」、「勾丝不易脱散」，用户反馈「刚穿就破」、\"一扯就烂\"、\"未勾丝即自行破损\"\",\"商品参数声明「包芯丝材质」，用户反馈「材质差、易勾丝、易脱丝」\",\"sku标注「适合70-110斤/100-150斤」，用户反馈「腰围过小无法穿着」、\"尺寸与标注体重范围不符\"\"],\"linkOrderSn\":\"0\",\"checkId\":-1}"
                }
            ]
        }
    }


def demonstrate_database_operations():
    """演示数据库操作"""
    print("🚀 开始SQLite数据库演示")
    print("=" * 60)

    # 创建数据库实例
    db = RectificationDatabase("demo_rectification.db")

    try:
        # 1. 连接数据库
        print("\n1️⃣ 连接数据库...")
        db.connect()

        # 2. 创建表结构
        print("\n2️⃣ 创建数据库表结构...")
        db.create_tables()

        # 3. 显示表结构
        print("\n3️⃣ 显示数据库表结构...")
        db.show_schema()

        # 4. 插入示例数据
        print("\n4️⃣ 插入示例数据...")
        sample_data = create_sample_data()

        # 插入多个店铺的示例数据
        test_shops = [
            ("雅鹿旗舰店", sample_data),
            ("测试店铺A", sample_data),
            ("中文店铺名称测试", sample_data)
        ]

        for shop_name, data in test_shops:
            db.insert_shop_data(shop_name, data)

        # 5. 查询店铺汇总信息
        print("\n5️⃣ 查询店铺汇总信息...")
        db.query_shop_summary()

        # 6. 查询特定店铺信息
        print("\n6️⃣ 查询特定店铺信息...")
        db.query_shop_summary("雅鹿旗舰店")

        # 7. 查询整改项目详情
        print("\n7️⃣ 查询整改项目详情...")
        db.query_rectification_items(limit=5)

        # 8. 按条件查询
        print("\n8️⃣ 按条件查询 - 查询状态为3的项目...")
        db.query_rectification_items(status=3, limit=3)

        # 9. 按数据类别查询
        print("\n9️⃣ 按数据类别查询 - 查询未提交的项目...")
        db.query_rectification_items(data_category='no_submit', limit=3)

        # 10. 获取统计信息
        print("\n🔟 获取数据库统计信息...")
        db.get_statistics()

        # 11. 演示中文字符处理
        print("\n1️⃣1️⃣ 演示中文字符查询...")
        print("查询包含'材质描述不符'问题的项目:")
        db.cursor.execute("""
            SELECT shop_name, problem_label,
                   json_extract(base_info, '$.goodsName') as goods_name
            FROM rectification_items
            WHERE problem_label LIKE '%材质描述不符%'
            LIMIT 3
        """)

        results = db.cursor.fetchall()
        print(f"{'店铺名称':<15} {'问题标签':<15} {'商品名称':<30}")
        print("-" * 60)
        for row in results:
            goods_name = row[2] if row[2] else "N/A"
            # 截断过长的商品名称
            if len(goods_name) > 25:
                goods_name = goods_name[:25] + "..."
            print(f"{row[0]:<15} {row[1]:<15} {goods_name:<30}")

        print("\n✅ 数据库演示完成！")
        print("\n💡 提示:")
        print("- 数据库文件已保存为: demo_rectification.db")
        print("- 可以使用SQLite浏览器工具查看数据库内容")
        print("- 中文字符已正确存储和查询")

    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")

    finally:
        # 关闭数据库连接
        print("\n🔚 关闭数据库连接...")
        db.disconnect()


def demonstrate_advanced_queries():
    """演示高级查询功能"""
    print("\n" + "="*60)
    print("🔍 高级查询演示")
    print("="*60)

    db = RectificationDatabase("demo_rectification.db")

    try:
        db.connect()

        # 1. JSON字段查询
        print("\n1️⃣ JSON字段查询 - 提取商品名称...")
        db.cursor.execute("""
            SELECT
                shop_name,
                json_extract(base_info, '$.goodsName') as goods_name,
                problem_label
            FROM rectification_items
            WHERE json_extract(base_info, '$.goodsName') IS NOT NULL
            LIMIT 3
        """)

        results = db.cursor.fetchall()
        print(f"{'店铺名称':<15} {'商品名称':<40} {'问题标签':<15}")
        print("-" * 70)
        for row in results:
            goods_name = row[1][:35] + "..." if len(row[1]) > 35 else row[1]
            print(f"{row[0]:<15} {goods_name:<40} {row[2]:<15}")

        # 2. 聚合查询
        print("\n2️⃣ 聚合查询 - 按店铺统计问题类型...")
        db.cursor.execute("""
            SELECT
                shop_name,
                problem_label,
                COUNT(*) as count
            FROM rectification_items
            GROUP BY shop_name, problem_label
            ORDER BY count DESC
        """)

        results = db.cursor.fetchall()
        print(f"{'店铺名称':<15} {'问题标签':<15} {'数量':<6}")
        print("-" * 36)
        for row in results:
            print(f"{row[0]:<15} {row[1]:<15} {row[2]:<6}")

        # 3. 时间范围查询
        print("\n3️⃣ 时间范围查询 - 最近创建的项目...")
        db.cursor.execute("""
            SELECT
                shop_name,
                problem_label,
                datetime(created_at_timestamp/1000, 'unixepoch') as created_time
            FROM rectification_items
            ORDER BY created_at_timestamp DESC
            LIMIT 5
        """)

        results = db.cursor.fetchall()
        print(f"{'店铺名称':<15} {'问题标签':<15} {'创建时间':<20}")
        print("-" * 50)
        for row in results:
            print(f"{row[0]:<15} {row[1]:<15} {row[2]:<20}")

    except Exception as e:
        print(f"❌ 高级查询演示失败: {e}")

    finally:
        db.disconnect()


if __name__ == "__main__":
    # 运行基本演示
    demonstrate_database_operations()

    # 运行高级查询演示
    demonstrate_advanced_queries()

    print("\n" + "="*60)
    print("🎉 SQLite数据库演示全部完成！")
    print("="*60)
