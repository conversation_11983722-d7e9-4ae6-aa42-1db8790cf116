#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库文件安全删除功能
"""

import os
import sqlite3
import time

def safe_remove_database(db_path):
    """
    安全删除数据库文件，如果无法删除则重命名为备份文件
    """
    if not os.path.exists(db_path):
        return True
        
    try:
        # 先尝试关闭可能的数据库连接
        import gc
        gc.collect()
        
        # 尝试删除文件
        os.remove(db_path)
        print(f"已删除数据库文件: {db_path}")
        return True
    except PermissionError:
        # 如果无法删除，重命名为备份文件
        backup_path = f"{db_path}.backup_{int(time.time())}"
        try:
            os.rename(db_path, backup_path)
            print(f"无法删除数据库文件，已重命名为备份: {backup_path}")
            return True
        except Exception as rename_error:
            print(f"重命名数据库文件失败: {rename_error}")
            # 如果重命名也失败，则跳过删除，直接覆盖
            return False
    except Exception as delete_error:
        print(f"删除数据库文件时出错: {delete_error}")
        return False

def test_database_removal():
    """测试数据库文件删除功能"""
    test_db_path = "test_database.db"
    
    # 创建测试数据库
    print("创建测试数据库...")
    conn = sqlite3.connect(test_db_path)
    cursor = conn.cursor()
    cursor.execute("CREATE TABLE test (id INTEGER PRIMARY KEY, name TEXT)")
    cursor.execute("INSERT INTO test (name) VALUES ('test_data')")
    conn.commit()
    
    # 保持连接打开，模拟文件被占用的情况
    print("保持数据库连接打开，模拟文件被占用...")
    
    # 尝试删除数据库文件
    print("尝试安全删除数据库文件...")
    result = safe_remove_database(test_db_path)
    
    if result:
        print("✅ 数据库文件处理成功")
    else:
        print("❌ 数据库文件处理失败")
    
    # 关闭连接
    conn.close()
    
    # 清理测试文件
    if os.path.exists(test_db_path):
        try:
            os.remove(test_db_path)
            print("清理测试数据库文件")
        except:
            print("无法清理测试数据库文件，请手动删除")
    
    # 清理备份文件
    backup_files = [f for f in os.listdir('.') if f.startswith('test_database.db.backup_')]
    for backup_file in backup_files:
        try:
            os.remove(backup_file)
            print(f"清理备份文件: {backup_file}")
        except:
            print(f"无法清理备份文件: {backup_file}")

if __name__ == "__main__":
    print("🔧 测试数据库文件安全删除功能")
    print("=" * 50)
    test_database_removal()
    print("=" * 50)
    print("✅ 测试完成") 