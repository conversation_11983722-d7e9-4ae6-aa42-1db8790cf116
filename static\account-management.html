<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宜承-账号与权限管理</title>
    <!-- 添加网站图标 -->
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
    <!-- 引入Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- 引入自定义样式 -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            padding: 0;
            background-color: #f8f9fa;
            color: #333;
        }
        .main-container {
            display: flex;
            min-height: 100vh;
        }
        .container {
            flex: 1;
            padding: 30px;
            transition: all 0.3s ease;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 28px;
            color: #2c3e50;
            position: relative;
            display: inline-block;
            padding-bottom: 10px;
        }
        .header h1:after {
            content: '';
            position: absolute;
            width: 50%;
            height: 3px;
            background: linear-gradient(90deg, #4b6cb7, #182848);
            bottom: 0;
            left: 25%;
            border-radius: 2px;
        }
        .card {
            border-radius: 12px;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            overflow: hidden;
            margin-bottom: 30px;
        }
        .card-header {
            background: linear-gradient(90deg, #4b6cb7, #182848);
            color: white;
            font-weight: 500;
            padding: 15px 20px;
            border: none;
        }
        .btn-primary {
            background: linear-gradient(90deg, #4b6cb7, #182848);
            border: none;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .user-table {
            width: 100%;
            border-collapse: collapse;
        }
        .user-table th {
            background-color: #f5f7fa;
            color: #2c3e50;
            font-weight: 500;
            padding: 12px 15px;
            text-align: left;
        }
        .user-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            vertical-align: middle;
        }
        .user-table tr:hover {
            background-color: #f8f9fa;
        }
        .action-buttons button {
            margin-right: 5px;
        }
        .role-badge {
            padding: 5px 10px;
            border-radius: 50px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }
        .role-admin {
            background-color: #4b6cb7;
            color: white;
        }
        .role-yunying {
            background-color: #28a745;
            color: white;
        }
        .role-art {
            background-color: #fd7e14;
            color: white;
        }
        .module-card {
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            background-color: white;
        }
        .permission-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 15px;
        }
        .permission-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .permission-item label {
            margin-left: 8px;
            margin-bottom: 0;
            user-select: none;
        }
        .form-check-input:checked {
            background-color: #4b6cb7;
            border-color: #4b6cb7;
        }
        .back-button {
            margin-bottom: 20px;
        }
        .alert {
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="header">
            <h1>账号与权限管理</h1>
        </div>
        
        <div class="back-button">
            <button class="btn btn-outline-primary" onclick="window.location.href='shop.html'">
                <i class="bi bi-arrow-left"></i> 返回主页
            </button>
        </div>
        
        <div id="accountManagerContainer">
            <!-- 这里将通过JavaScript动态加载内容 -->
        </div>
    </div>

    <!-- 用户编辑模态框 -->
    <div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editUserModalLabel">编辑用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="editUserForm">
                    <!-- 用户编辑表单将在这里动态加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveUserBtn">保存更改</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增用户模态框 -->
    <div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addUserModalLabel">新增用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="addUserForm">
                    <!-- 新增用户表单将在这里动态加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="createUserBtn">创建用户</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 全局变量
        let accounts = [];
        let currentEditUser = null;
        let menuItems = [
            { id: 'data-overview', name: '数据概览', icon: '📊' },
            { id: 'sales-stats', name: '销售统计', icon: '📈' },
            { id: 'sale-analytics', name: '销售数据分析', icon: '📊' },
            { id: 'shop-management', name: '店铺管理', icon: '🏪' },
            { id: 'spu-management', name: 'SPU管理', icon: '📦' },
            { id: 'promotion-data-management', name: '推广数据管理', icon: '📢' },
            { id: 'violation-management', name: '商品违规整改', icon: '⚠️' },
            { id: 'finance-management', name: '财务管理', icon: '💰' },
            { id: 'link-management', name: '链接管理', icon: '🔗' },
            { id: 'team-management', name: '团队管理', icon: '👥' },
            { id: 'spu-trend', name: 'SPU全年销售趋势', icon: '📉' },
            { id: 'art-task-management', name: '美工任务管理', icon: '🎨' }
        ];

        // 页面加载时检查权限
        document.addEventListener('DOMContentLoaded', function() {
            // 检查登录状态
            checkLoginStatus();
            // 加载账号数据
            loadAccounts();
            
            // 绑定保存按钮事件
            document.getElementById('saveUserBtn').addEventListener('click', saveUserEdit);
            document.getElementById('createUserBtn').addEventListener('click', createNewUser);
        });

        // 验证登录状态
        function checkLoginStatus() {
            const cookies = document.cookie.split(';');
            const loginAuth = cookies.find(cookie => cookie.trim().startsWith('loginAuth='));
            const userRole = getUserRole();
            
            if (!loginAuth || loginAuth.split('=')[1].trim() !== 'true') {
                window.location.href = 'login.html';
                return;
            }
            
            // 检查是否是管理员
            if (userRole !== 'admin') {
                alert('只有管理员可以访问此页面');
                window.location.href = 'shop.html';
                return;
            }
        }

        // 获取用户角色
        function getUserRole() {
            const cookies = document.cookie.split(';');
            const roleCookie = cookies.find(cookie => cookie.trim().startsWith('userRole='));
            return roleCookie ? decodeURIComponent(roleCookie.split('=')[1].trim()) : null;
        }

        // 加载账号数据
        function loadAccounts() {
            fetch('/api/users')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('无法加载账号数据');
                    }
                    return response.json();
                })
                .then(result => {
                    if (result.success) {
                        accounts = result.data || [];
                        renderAccountManager();
                    } else {
                        throw new Error(result.message || '加载数据失败');
                    }
                })
                .catch(error => {
                    console.error('加载账号数据失败:', error);
                    showAlert('加载账号数据失败，请刷新页面重试', 'danger');
                });
        }

        // 渲染账号管理界面
        function renderAccountManager() {
            const container = document.getElementById('accountManagerContainer');
            
            // 清空容器
            container.innerHTML = '';
            
            // 添加账号管理卡片
            const accountCard = document.createElement('div');
            accountCard.className = 'card';
            accountCard.innerHTML = `
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">用户账号管理</h5>
                    <button class="btn btn-light" onclick="showAddUserModal()">
                        <i class="bi bi-plus"></i> 新增用户
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="user-table">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>角色</th>
                                    <th>导航权限</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="userTableBody">
                                ${accounts.map(user => `
                                    <tr>
                                        <td>${user.user || user.username}</td>
                                        <td>
                                            <span class="role-badge role-${user.role}">${getRoleDisplayName(user.role)}</span>
                                        </td>
                                        <td>
                                            ${renderUserPermissions(user)}
                                        </td>
                                        <td class="action-buttons">
                                            <button class="btn btn-sm btn-outline-primary" onclick="editUser('${user.user || user.username}')">
                                                编辑
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteUser('${user.user || user.username}')">
                                                删除
                                            </button>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
            
            container.appendChild(accountCard);
        }
        
        // 渲染用户权限
        function renderUserPermissions(user) {
            if (user.role === 'admin') {
                return '<span class="badge bg-primary">全部权限</span>';
            }
            
            const userPerms = user.permissions || [];
            if (userPerms.length === 0) {
                // 默认权限
                if (user.role === 'art') {
                    return '<span class="badge bg-warning">美工任务管理</span>';
                } else if (user.role === 'yunying') {
                    return '<span class="badge bg-success">除财务外的所有功能</span>';
                } else if (user.role === 'caiwu') {
                    return '<span class="badge bg-info">财务管理</span>';
                } else if (user.role === 'caigou') {
                    return '<span class="badge bg-secondary">数据概览, 店铺管理, SPU管理</span>';
                } else if (user.role === 'kefu') {
                    return '<span class="badge bg-secondary">数据概览, 店铺管理, 留言板</span>';
                }
                return '<span class="badge bg-danger">无权限</span>';
            }
            
            // 显示具体权限
            return userPerms.map(permId => {
                const menuItem = menuItems.find(item => item.id === permId);
                if (menuItem) {
                    let badgeClass = 'bg-secondary';
                    if (permId === 'finance-management') badgeClass = 'bg-info';
                    if (permId === 'art-task-management') badgeClass = 'bg-warning';
                    
                    return `<span class="badge ${badgeClass} me-1">${menuItem.name}</span>`;
                }
                return '';
            }).join(' ');
        }

        // 获取角色显示名称
        function getRoleDisplayName(role) {
            switch(role) {
                case 'admin': return '管理员';
                case 'caigou': return '采购';
                case 'caiwu': return '财务';
                case 'kefu': return '客服';
                case 'yunying': return '运营';
                case 'art': return '美工';
                default: return role || '普通用户';
            }
        }

        // 显示提示信息
        function showAlert(message, type = 'success') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;
            
            const container = document.getElementById('accountManagerContainer');
            container.insertBefore(alertDiv, container.firstChild);
            
            // 5秒后自动关闭
            setTimeout(() => {
                alertDiv.classList.remove('show');
                setTimeout(() => alertDiv.remove(), 300);
            }, 5000);
        }
        
        // 显示加载状态
        function showLoading(message = '加载中...') {
            // 添加加载指示器
            const loadingDiv = document.createElement('div');
            loadingDiv.id = 'loadingIndicator';
            loadingDiv.className = 'alert alert-info text-center';
            loadingDiv.innerHTML = `
                <div class="spinner-border spinner-border-sm me-2" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                ${message}
            `;
            
            const container = document.getElementById('accountManagerContainer');
            container.insertBefore(loadingDiv, container.firstChild);
        }
        
        // 隐藏加载状态
        function hideLoading() {
            const loadingIndicator = document.getElementById('loadingIndicator');
            if (loadingIndicator) {
                loadingIndicator.remove();
            }
        }
        
        // 编辑用户
        function editUser(username) {
            const user = accounts.find(u => (u.user === username) || (u.username === username));
            if (!user) {
                showAlert('找不到该用户', 'danger');
                return;
            }
            
            currentEditUser = user;
            const userName = user.user || user.username;
            
            // 填充编辑表单
            const form = document.getElementById('editUserForm');
            form.innerHTML = `
                <div class="mb-3">
                    <label for="editUsername" class="form-label">用户名</label>
                    <input type="text" class="form-control" id="editUsername" value="${userName}" readonly>
                </div>
                <div class="mb-3">
                    <label for="editPassword" class="form-label">密码 (留空表示不修改)</label>
                    <input type="password" class="form-control" id="editPassword">
                </div>
                <div class="mb-3">
                    <label for="editRole" class="form-label">角色</label>
                    <select class="form-select" id="editRole">
                        <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>管理员</option>
                        <option value="yunying" ${user.role === '运营' || user.role === 'yunying' ? 'selected' : ''}>运营</option>
                        <option value="art" ${user.role === '美工' || user.role === 'art' ? 'selected' : ''}>美工</option>
                        <option value="caigou" ${user.role === '采购' || user.role === 'caigou' ? 'selected' : ''}>采购</option>
                        <option value="caiwu" ${user.role === '财务' || user.role === 'caiwu' ? 'selected' : ''}>财务</option>
                        <option value="kefu" ${user.role === '客服' || user.role === 'kefu' ? 'selected' : ''}>客服</option>
                    </select>
                </div>
                <div class="mb-3" id="permissionsSection">
                    <label class="form-label">导航权限设置</label>
                    <p class="text-muted small">当前用户拥有的权限:</p>
                    <div class="mb-2" id="currentPermissions">
                        ${renderUserPermissions(user)}
                    </div>
                    <div class="permission-container">
                        ${menuItems.map(item => {
                            // 检查用户是否拥有该权限
                            const hasPermission = user.permissions && Array.isArray(user.permissions) && 
                                user.permissions.includes(item.id);
                            
                            return `
                            <div class="permission-item">
                                <input class="form-check-input" type="checkbox" id="perm-${item.id}" 
                                    ${hasPermission || 
                                    (user.role === 'admin') ||
                                    (user.role === 'art' && item.id === 'art-task-management') ||
                                    (user.role === 'yunying' && item.id !== 'finance-management') ||
                                    (user.role === 'caigou' && item.id !== 'finance-management') ||
                                    (user.role === 'caiwu' && item.id !== 'finance-management') ||
                                    (user.role === 'kefu' && item.id !== 'finance-management') ? 'checked' : ''}>
                                <label class="form-check-label" for="perm-${item.id}">
                                    ${item.icon} ${item.name}
                                </label>
                            </div>
                            `;
                        }).join('')}
                    </div>
                </div>
            `;
            
            // 添加角色变化的事件监听
            setTimeout(() => {
                const roleSelect = document.getElementById('editRole');
                if (roleSelect) {
                    roleSelect.addEventListener('change', handleRoleChange);
                    // 初始化权限显示
                    handleRoleChange();
                }
            }, 100);
            
            // 显示编辑模态框
            const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
            modal.show();
        }
        
        // 处理角色变化
        function handleRoleChange() {
            const roleSelect = document.getElementById('editRole');
            const permissionsSection = document.getElementById('permissionsSection');
            const role = roleSelect.value;
            
            if (role === 'admin') {
                // 管理员有所有权限，不需要显示权限设置
                permissionsSection.style.display = 'none';
                // 选中所有权限
                menuItems.forEach(item => {
                    const checkbox = document.getElementById(`perm-${item.id}`);
                    if (checkbox) checkbox.checked = true;
                });
            } else if (role === 'art') {
                // 美工只有美工任务管理权限
                permissionsSection.style.display = 'block';
                // 只选中美工任务权限
                menuItems.forEach(item => {
                    const checkbox = document.getElementById(`perm-${item.id}`);
                    if (checkbox) checkbox.checked = item.id === 'art-task-management';
                });
            } else if (role === 'yunying') {
                // 运营没有财务权限
                permissionsSection.style.display = 'block';
                // 除财务外全选
                menuItems.forEach(item => {
                    const checkbox = document.getElementById(`perm-${item.id}`);
                    if (checkbox) checkbox.checked = item.id !== 'finance-management';
                });
            } else {
                // 其他角色，显示权限设置
                permissionsSection.style.display = 'block';
                // 初始不选择任何权限
                menuItems.forEach(item => {
                    const checkbox = document.getElementById(`perm-${item.id}`);
                    if (checkbox) checkbox.checked = false;
                });
            }
        }
        
        // 保存用户编辑
        function saveUserEdit() {
            if (!currentEditUser) {
                showAlert('没有正在编辑的用户', 'danger');
                return;
            }
            
            const password = document.getElementById('editPassword').value;
            const role = document.getElementById('editRole').value;
            
            // 获取权限
            const permissions = [];
            document.querySelectorAll('#permissionsSection input[type="checkbox"]:checked').forEach(checkbox => {
                const permId = checkbox.id.replace('perm-', '');
                if (permId) {
                    permissions.push(permId);
                }
            });
            
            // 更新数据
            const userData = {
                username: currentEditUser.user || currentEditUser.username,
                role: role,
                permissions: permissions
            };
            
            // 如果有设置密码，则添加密码字段
            if (password) {
                userData.password = password;
            }
            
            // 显示加载状态
            showLoading('正在保存...');
            
            // 发送请求到服务器更新用户
            const username = currentEditUser.user || currentEditUser.username;
            fetch(`/api/users/${username}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            })
            .then(response => response.json())
            .then(result => {
                hideLoading();
                if (result.success) {
                    // 更新本地用户对象
                    if (result.user) {
                        Object.assign(currentEditUser, result.user);
                    } else {
                        currentEditUser.role = role;
                        currentEditUser.permissions = permissions;
                    }
                    
                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('editUserModal'));
                    if (modal) {
                        modal.hide();
                    }
                    
                    // 重新渲染用户列表
                    renderAccountManager();
                    
                    showAlert(result.message || '用户信息更新成功', 'success');
                    currentEditUser = null;
                } else {
                    showAlert(result.message || '更新失败，请重试', 'danger');
                }
            })
            .catch(error => {
                hideLoading();
                console.error('更新用户失败:', error);
                showAlert('更新失败，请检查网络连接或服务器状态', 'danger');
            });
        }
        
        // 删除用户
        function deleteUser(username) {
            // 防止删除唯一的管理员
            const adminUsers = accounts.filter(u => u.role === 'admin');
            const userToDelete = accounts.find(u => u.user === username);
            
            if (adminUsers.length === 1 && userToDelete.role === 'admin') {
                showAlert('无法删除唯一的管理员账号', 'danger');
                return;
            }
            
            if (confirm(`确定要删除用户 "${username}" 吗？`)) {
                // 发送删除请求到API
                fetch(`/api/users/${username}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        // 从本地数组中移除用户
                        const index = accounts.findIndex(u => u.user === username);
                        if (index !== -1) {
                            accounts.splice(index, 1);
                        }
                        showAlert(result.message || '用户删除成功');
                        renderAccountManager();
                    } else {
                        showAlert(result.message || '删除失败，请重试', 'danger');
                    }
                })
                .catch(error => {
                    console.error('删除用户失败:', error);
                    showAlert('删除失败，请检查网络连接或服务器状态', 'danger');
                });
            }
        }

        // 显示添加用户模态框
        function showAddUserModal() {
            // 填充添加用户表单
            const form = document.getElementById('addUserForm');
            form.innerHTML = `
                <div class="mb-3">
                    <label for="newUsername" class="form-label">用户名</label>
                    <input type="text" class="form-control" id="newUsername" required>
                </div>
                <div class="mb-3">
                    <label for="newDisplayName" class="form-label">显示名称</label>
                    <input type="text" class="form-control" id="newDisplayName" required>
                </div>
                <div class="mb-3">
                    <label for="newPassword" class="form-label">密码</label>
                    <input type="password" class="form-control" id="newPassword" required>
                </div>
                <div class="mb-3">
                    <label for="newRole" class="form-label">角色</label>
                    <select class="form-select" id="newRole">
                        <option value="admin">管理员</option>
                        <option value="yunying" selected>运营</option>
                        <option value="art">美工</option>
                        <option value="caigou">采购</option>
                        <option value="caiwu">财务</option>
                        <option value="kefu">客服</option>
                    </select>
                </div>
                <div class="mb-3" id="newPermissionsSection">
                    <label class="form-label">导航权限设置</label>
                    <div class="permission-container">
                        ${menuItems.map(item => `
                            <div class="permission-item">
                                <input class="form-check-input" type="checkbox" id="new-perm-${item.id}" 
                                    ${(item.id !== 'finance-management') ? 'checked' : ''}>
                                <label class="form-check-label" for="new-perm-${item.id}">
                                    ${item.icon} ${item.name}
                                </label>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
            
            // 添加角色变化的事件监听
            setTimeout(() => {
                const roleSelect = document.getElementById('newRole');
                if (roleSelect) {
                    roleSelect.addEventListener('change', handleNewRoleChange);
                    // 初始化权限显示
                    handleNewRoleChange();
                }
            }, 100);
            
            // 显示添加模态框
            const modal = new bootstrap.Modal(document.getElementById('addUserModal'));
            modal.show();
        }
        
        // 处理新建用户的角色变化
        function handleNewRoleChange() {
            const roleSelect = document.getElementById('newRole');
            const permissionsSection = document.getElementById('newPermissionsSection');
            const role = roleSelect.value;
            
            if (role === 'admin') {
                // 管理员有所有权限，不需要显示权限设置
                permissionsSection.style.display = 'none';
                // 选中所有权限
                menuItems.forEach(item => {
                    const checkbox = document.getElementById(`new-perm-${item.id}`);
                    if (checkbox) checkbox.checked = true;
                });
            } else if (role === 'art') {
                // 美工只有美工任务管理权限
                permissionsSection.style.display = 'block';
                // 只选中美工任务权限
                menuItems.forEach(item => {
                    const checkbox = document.getElementById(`new-perm-${item.id}`);
                    if (checkbox) checkbox.checked = item.id === 'art-task-management';
                });
            } else if (role === 'yunying') {
                // 运营没有财务权限
                permissionsSection.style.display = 'block';
                // 除财务外全选
                menuItems.forEach(item => {
                    const checkbox = document.getElementById(`new-perm-${item.id}`);
                    if (checkbox) checkbox.checked = item.id !== 'finance-management';
                });
            } else {
                // 其他角色，显示权限设置
                permissionsSection.style.display = 'block';
                // 初始不选择任何权限
                menuItems.forEach(item => {
                    const checkbox = document.getElementById(`new-perm-${item.id}`);
                    if (checkbox) checkbox.checked = false;
                });
            }
        }
        
        // 创建新用户
        function createNewUser() {
            const username = document.getElementById('newUsername').value.trim();
            const displayName = document.getElementById('newDisplayName').value.trim();
            const password = document.getElementById('newPassword').value;
            const role = document.getElementById('newRole').value;
            
            // 验证输入
            if (!username || !displayName || !password) {
                showAlert('请填写所有必填字段', 'danger');
                return;
            }
            
            // 检查用户名是否已存在
            if (accounts.some(u => (u.user === username) || (u.username === username))) {
                showAlert('用户名已存在', 'danger');
                return;
            }
            
            // 获取权限
            let permissions = [];
            // 对于管理员，自动拥有所有权限
            if (role === 'admin') {
                permissions = menuItems.map(item => item.id);
            } else {
                // 对于其他角色，从复选框获取权限
                menuItems.forEach(item => {
                    const checkbox = document.getElementById(`new-perm-${item.id}`);
                    if (checkbox && checkbox.checked) {
                        permissions.push(item.id);
                    }
                });
            }
            
            // 创建用户数据
            const userData = {
                username: username,
                password: password,
                role: role,
                name: displayName,
                permissions: permissions
            };
            
            // 发送到API创建用户
            fetch('/api/users', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    // 添加新用户到本地数组
                    if (result.user) {
                        accounts.push(result.user);
                    }
                    showAlert(result.message || '用户创建成功');
                    renderAccountManager();
                    
                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
                    modal.hide();
                } else {
                    showAlert(result.message || '创建失败，请重试', 'danger');
                }
            })
            .catch(error => {
                console.error('创建用户失败:', error);
                showAlert('创建失败，请检查网络连接或服务器状态', 'danger');
            });
        }
        
        // 保存账号到文件
        function saveAccountsToFile() {
            // 此函数已废弃，不再使用
            console.warn('saveAccountsToFile函数已废弃，请使用对应的API');
        }
    </script>
</body>
</html> 