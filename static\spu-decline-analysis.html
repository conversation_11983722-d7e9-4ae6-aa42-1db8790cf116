<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SPU同比下降分析 - 电商数据分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --success-color: #10b981;
            --info-color: #3b82f6;
            --dark-color: #1f2937;
            --light-gray: #f8fafc;
            --border-color: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--text-primary);
            line-height: 1.6;
        }

        .main-container {
            background: var(--light-gray);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .page-header {
            background: white;
            border-radius: var(--radius-xl);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .page-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
            font-weight: 400;
        }

        .filter-section {
            background: white;
            border-radius: var(--radius-xl);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .filter-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-label {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .form-control, .form-select {
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: 0.75rem 1rem;
            font-size: 0.95rem;
            transition: all 0.2s ease;
            background: white;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgb(99 102 241 / 0.1);
            outline: none;
        }

        .btn {
            border-radius: var(--radius-md);
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            transition: all 0.2s ease;
            border: none;
            font-size: 0.95rem;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
            box-shadow: var(--shadow-sm);
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-outline-primary {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-1px);
        }

        .btn-outline-success {
            border: 2px solid var(--success-color);
            color: var(--success-color);
            background: transparent;
        }

        .btn-outline-success:hover {
            background: var(--success-color);
            color: white;
            transform: translateY(-1px);
        }

        .stats-card {
            background: white;
            border-radius: var(--radius-xl);
            padding: 2rem;
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--info-color));
        }

        .stats-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .stats-icon {
            width: 3.5rem;
            height: 3.5rem;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .stats-value {
            font-size: 2.25rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            line-height: 1;
        }

        .stats-label {
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 1rem;
        }

        .stats-meta {
            color: var(--text-secondary);
            font-size: 0.85rem;
            margin-top: 0.5rem;
        }

        .decline-badge {
            font-size: 0.8rem;
            font-weight: 600;
            padding: 0.375rem 0.75rem;
            border-radius: var(--radius-md);
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .decline-severe { 
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }
        
        .decline-significant { 
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
        }
        
        .decline-moderate { 
            background: linear-gradient(135deg, #eab308, #ca8a04);
            color: white;
        }
        
        .decline-mild { 
            background: linear-gradient(135deg, #6b7280, #4b5563);
            color: white;
        }

        .data-card {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .data-card-header {
            background: linear-gradient(135deg, var(--light-gray), white);
            padding: 1.5rem 2rem;
            border-bottom: 1px solid var(--border-color);
        }

        .data-card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .table {
            margin-bottom: 0;
        }

        .table thead th {
            background: var(--light-gray);
            border: none;
            font-weight: 600;
            color: var(--text-primary);
            padding: 1rem 1.5rem;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .table tbody td {
            padding: 1rem 1.5rem;
            border-color: var(--border-color);
            vertical-align: middle;
            font-size: 0.95rem;
        }

        .table tbody tr {
            transition: all 0.2s ease;
        }

        .table tbody tr:hover {
            background: var(--light-gray);
            transform: scale(1.001);
        }

        .rank-badge {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            font-weight: 600;
            padding: 0.375rem 0.75rem;
            border-radius: var(--radius-md);
            font-size: 0.85rem;
        }

        .code-text {
            background: var(--light-gray);
            color: var(--text-primary);
            padding: 0.25rem 0.5rem;
            border-radius: var(--radius-sm);
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .trend-indicator {
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .trend-down {
            color: var(--danger-color);
        }

        .loading-spinner {
            display: none;
            text-align: center;
            padding: 4rem 2rem;
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .spinner-border {
            width: 3rem;
            height: 3rem;
            border-width: 0.25rem;
        }

        .loading-text {
            margin-top: 1.5rem;
            color: var(--text-secondary);
            font-size: 1.1rem;
            font-weight: 500;
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .empty-icon {
            font-size: 4rem;
            color: var(--success-color);
            margin-bottom: 1.5rem;
        }

        .empty-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--success-color);
            margin-bottom: 1rem;
        }

        .empty-description {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin-bottom: 2rem;
            line-height: 1.7;
        }

        .pagination {
            gap: 0.5rem;
        }

        .page-link {
            border: 2px solid var(--border-color);
            color: var(--text-primary);
            padding: 0.5rem 1rem;
            border-radius: var(--radius-md);
            font-weight: 500;
            transition: all 0.2s ease;
            background: white;
        }

        .page-link:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            background: rgb(99 102 241 / 0.05);
            transform: translateY(-1px);
        }

        .page-item.active .page-link {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            box-shadow: var(--shadow-sm);
        }

        .page-item.disabled .page-link {
            color: var(--text-secondary);
            background: var(--light-gray);
            border-color: var(--border-color);
        }

        .card-footer {
            background: var(--light-gray);
            border-top: 1px solid var(--border-color);
            padding: 1.5rem 2rem;
        }

        .amount-text {
            font-weight: 600;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
        }

        .amount-primary {
            color: var(--primary-color);
        }

        .amount-danger {
            color: var(--danger-color);
        }

        .text-truncate-custom {
            max-width: 200px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 排序样式 */
        .sortable {
            user-select: none;
            transition: all 0.2s ease;
        }

        .sortable:hover {
            background: rgba(99, 102, 241, 0.1);
            color: var(--primary-color);
        }

        .sortable.sort-active {
            background: rgba(99, 102, 241, 0.15);
            color: var(--primary-color);
            font-weight: 600;
        }

        .sort-icon {
            opacity: 0.5;
            transition: all 0.2s ease;
            font-size: 0.75rem;
        }

        .sortable:hover .sort-icon,
        .sortable.sort-active .sort-icon {
            opacity: 1;
        }

        .sortable.sort-asc .sort-icon:before {
            content: "\f143"; /* bi-arrow-up */
        }

        .sortable.sort-desc .sort-icon:before {
            content: "\f12f"; /* bi-arrow-down */
        }

        /* 对比时间样式 */
        .compare-time {
            font-size: 0.8rem;
            line-height: 1.3;
        }

        .compare-time .current-year {
            font-weight: 600;
        }

        .compare-time .vs-divider {
            text-align: center;
            font-weight: 500;
        }

        .compare-time .last-year {
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 1rem 0;
            }

            .page-header, .filter-section, .data-card {
                margin: 0 1rem 1.5rem 1rem;
                padding: 1.5rem;
            }

            .page-title {
                font-size: 1.5rem;
            }

            .stats-card {
                margin-bottom: 1rem;
            }

            .table-responsive {
                max-height: 400px;
            }

            .pagination {
                flex-wrap: wrap;
                justify-content: center;
            }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .slide-up {
            animation: slideUp 0.3s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="container-fluid">
            <!-- 页面标题 -->
            <div class="page-header fade-in">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="page-title">
                            <i class="bi bi-graph-down-arrow text-danger"></i>
                            SPU同比下降分析
                        </h1>
                        <p class="page-subtitle">快速找出表现下滑的商品，及时调整运营策略</p>
                    </div>
                    <div>
                        <button class="btn btn-outline-primary" onclick="location.href='spu-analytics.html'">
                            <i class="bi bi-arrow-left me-2"></i>返回SPU分析
                        </button>
                    </div>
                </div>
            </div>

            <!-- 筛选区域 -->
            <div class="filter-section fade-in">
                <h3 class="filter-title">
                    <i class="bi bi-funnel"></i>
                    筛选条件
                </h3>
                <div class="row g-4">
                    <div class="col-md-3">
                        <label class="form-label">日期范围</label>
                        <input type="text" class="form-control" id="dateRange" placeholder="选择日期范围">
                        <small class="text-muted">默认使用当月数据</small>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">数据维度</label>
                        <select class="form-select" id="dimension">
                            <option value="style_code">款式编码</option>
                            <option value="product_code">商品编码</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">最小下降幅度</label>
                        <select class="form-select" id="minDeclineRate">
                            <option value="5">5%</option>
                            <option value="10" selected>10%</option>
                            <option value="20">20%</option>
                            <option value="30">30%</option>
                            <option value="50">50%</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">最小销售额</label>
                        <select class="form-select" id="minAmount">
                            <option value="500">500元</option>
                            <option value="1000" selected>1000元</option>
                            <option value="2000">2000元</option>
                            <option value="5000">5000元</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">搜索</label>
                        <input type="text" class="form-control" id="searchInput" placeholder="款式/商品编码">
                    </div>
                    <div class="col-md-1 d-flex align-items-end">
                        <button class="btn btn-primary w-100" onclick="loadDeclineAnalysis()">
                            <i class="bi bi-search me-1"></i>分析
                        </button>
                    </div>
                </div>
            </div>

            <!-- 加载提示 -->
            <div class="loading-spinner" id="loadingSpinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="loading-text">正在分析SPU同比数据...</p>
            </div>

            <!-- 统计概览 -->
            <div id="statisticsSection" style="display: none;">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stats-card slide-up">
                            <div class="stats-icon bg-danger bg-opacity-10 text-danger">
                                <i class="bi bi-graph-down"></i>
                            </div>
                            <div class="stats-value text-danger" id="decliningCount">0</div>
                            <div class="stats-label">下降SPU数量</div>
                            <div class="stats-meta">占比 <span id="declineRate">0%</span></div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card slide-up" style="animation-delay: 0.1s;">
                            <div class="stats-icon bg-success bg-opacity-10 text-success">
                                <i class="bi bi-graph-up"></i>
                            </div>
                            <div class="stats-value text-success" id="growingCount">0</div>
                            <div class="stats-label">增长SPU数量</div>
                            <div class="stats-meta">对比总数 <span id="totalCompared">0</span></div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card slide-up" style="animation-delay: 0.2s;">
                            <div class="stats-icon bg-primary bg-opacity-10 text-primary">
                                <i class="bi bi-currency-yen"></i>
                            </div>
                            <div class="stats-value text-primary" id="totalCurrentAmount">0</div>
                            <div class="stats-label">今年总销售额</div>
                            <div class="stats-meta" id="currentDateRange">-</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card slide-up" style="animation-delay: 0.3s;">
                            <div class="stats-icon bg-warning bg-opacity-10 text-warning">
                                <i class="bi bi-arrow-up-right"></i>
                            </div>
                            <div class="stats-value trend-indicator" id="overallGrowthRate">0%</div>
                            <div class="stats-label">整体同比增长</div>
                            <div class="stats-meta" id="lastYearDateRange">-</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 结果表格 -->
            <div id="resultsSection" style="display: none;">
                <div class="data-card slide-up">
                    <div class="data-card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="data-card-title mb-0">
                                <i class="bi bi-table"></i>
                                同比下降SPU详情
                            </h5>
                            <div>
                                <button class="btn btn-outline-success btn-sm" onclick="exportToCSV()">
                                    <i class="bi bi-download me-1"></i>导出CSV
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th class="sortable" data-sort="style_code" style="cursor: pointer;">
                                        款式编码 <i class="bi bi-arrow-down-up ms-1 sort-icon"></i>
                                    </th>
                                    <th class="product-code-header sortable" data-sort="product_code" style="cursor: pointer;">
                                        商品编码 <i class="bi bi-arrow-down-up ms-1 sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-sort="product_name" style="cursor: pointer;">
                                        商品名称 <i class="bi bi-arrow-down-up ms-1 sort-icon"></i>
                                    </th>
                                    <th>对比时间</th>
                                    <th class="sortable" data-sort="current_amount" style="cursor: pointer;">
                                        今年销售额 <i class="bi bi-arrow-down-up ms-1 sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-sort="last_year_amount" style="cursor: pointer;">
                                        去年销售额 <i class="bi bi-arrow-down-up ms-1 sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-sort="decline_amount" style="cursor: pointer;">
                                        下降金额 <i class="bi bi-arrow-down-up ms-1 sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-sort="growth_rate" style="cursor: pointer;">
                                        同比增长率 <i class="bi bi-arrow-down-up ms-1 sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-sort="decline_level" style="cursor: pointer;">
                                        下降程度 <i class="bi bi-arrow-down-up ms-1 sort-icon"></i>
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="resultsTableBody">
                            </tbody>
                        </table>
                    </div>
                    <div class="card-footer">
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center mb-0" id="pagination">
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- 空状态 -->
            <div id="emptyState" class="empty-state" style="display: none;">
                <div class="empty-icon">
                    <i class="bi bi-emoji-smile"></i>
                </div>
                <h4 class="empty-title">太好了！</h4>
                <div class="empty-description">
                    在当前筛选条件下，没有发现显著下降的SPU。<br>
                    您可以调整筛选条件进行更详细的分析。
                </div>
                <button class="btn btn-outline-primary" onclick="adjustFilters()">
                    <i class="bi bi-sliders me-2"></i>调整筛选条件
                </button>
            </div>
        </div>
    </div>

    <!-- 脚本引用 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.1/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>

    <script>
        // 全局变量
        let currentData = [];
        let currentStats = {};
        let currentPage = 1;
        let totalPages = 1;
        let currentSort = {
            field: 'decline_amount',
            direction: 'desc'
        };

        // 页面初始化
        $(document).ready(function() {
            // 初始化日期选择器
            $('#dateRange').daterangepicker({
                locale: {
                    format: 'YYYY-MM-DD',
                    separator: ' - ',
                    applyLabel: '确定',
                    cancelLabel: '取消',
                    fromLabel: '从',
                    toLabel: '到',
                    customRangeLabel: '自定义',
                    weekLabel: 'W',
                    daysOfWeek: ['日', '一', '二', '三', '四', '五', '六'],
                    monthNames: ['一月', '二月', '三月', '四月', '五月', '六月',
                               '七月', '八月', '九月', '十月', '十一月', '十二月'],
                    firstDay: 1
                },
                ranges: {
                    '本月': [moment().startOf('month'), moment().subtract(1, 'day')],
                    '上月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
                    '最近30天': [moment().subtract(30, 'days'), moment().subtract(1, 'day')],
                    '最近90天': [moment().subtract(90, 'days'), moment().subtract(1, 'day')]
                },
                autoUpdateInput: false
            });

            // 日期选择事件
            $('#dateRange').on('apply.daterangepicker', function(ev, picker) {
                $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
            });

            $('#dateRange').on('cancel.daterangepicker', function(ev, picker) {
                $(this).val('');
            });

            // 回车搜索
            $('#searchInput').on('keypress', function(e) {
                if (e.which === 13) {
                    loadDeclineAnalysis();
                }
            });

            // 表头排序点击事件
            $(document).on('click', '.sortable', function() {
                const sortField = $(this).data('sort');
                if (sortField) {
                    handleSort(sortField);
                }
            });

            // 默认加载当月数据
            loadDeclineAnalysis();
        });

        // 处理排序
        function handleSort(field) {
            if (currentSort.field === field) {
                // 同一字段，切换排序方向
                currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
            } else {
                // 不同字段，默认降序
                currentSort.field = field;
                currentSort.direction = 'desc';
            }
            
            // 更新表头显示
            updateSortHeaders();
            
            // 对当前数据进行排序
            sortCurrentData();
            
            // 重新渲染表格
            updateResultsTable(currentData, {
                page: currentPage,
                page_size: 50,
                total: currentData.length,
                total_pages: Math.ceil(currentData.length / 50)
            });
        }

        // 更新排序表头显示
        function updateSortHeaders() {
            // 清除所有排序状态
            $('.sortable').removeClass('sort-active sort-asc sort-desc');
            
            // 设置当前排序列的状态
            $(`.sortable[data-sort="${currentSort.field}"]`)
                .addClass('sort-active')
                .addClass(currentSort.direction === 'asc' ? 'sort-asc' : 'sort-desc');
        }

        // 对当前数据进行排序
        function sortCurrentData() {
            if (!currentData || currentData.length === 0) {
                return;
            }

            currentData.sort((a, b) => {
                let aValue = a[currentSort.field];
                let bValue = b[currentSort.field];

                // 处理数值类型
                if (['current_amount', 'last_year_amount', 'decline_amount', 'growth_rate'].includes(currentSort.field)) {
                    aValue = parseFloat(aValue) || 0;
                    bValue = parseFloat(bValue) || 0;
                }
                // 处理字符串类型
                else {
                    aValue = String(aValue || '').toLowerCase();
                    bValue = String(bValue || '').toLowerCase();
                }

                let result = 0;
                if (aValue < bValue) {
                    result = -1;
                } else if (aValue > bValue) {
                    result = 1;
                }

                return currentSort.direction === 'asc' ? result : -result;
            });
        }

        // 加载下降分析数据
        async function loadDeclineAnalysis(page = 1) {
            try {
                currentPage = page;
                showLoading(true);
                hideAllSections();

                // 获取筛选参数
                const params = getFilterParams(page);

                // 发起API请求
                const response = await fetch(`/api/spu-decline-analysis?${params}`);
                const result = await response.json();

                if (result.success) {
                    currentData = result.data || [];
                    currentStats = result.statistics || {};
                    
                    if (result.pagination) {
                        currentPage = result.pagination.page;
                        totalPages = result.pagination.total_pages;
                    }

                    // 显示统计信息
                    updateStatistics(currentStats);

                    // 显示结果表格
                    if (currentData.length > 0) {
                        updateResultsTable(currentData, result.pagination);
                        updatePagination(currentPage, totalPages);
                        updateSortHeaders(); // 更新排序表头显示
                        showSection('resultsSection');
                    } else {
                        showSection('emptyState');
                    }
                    
                    showSection('statisticsSection');
                } else {
                    console.error('API错误:', result.error);
                    alert('加载数据失败: ' + result.error);
                }

            } catch (error) {
                console.error('加载数据失败:', error);
                alert('加载数据失败，请稍后重试');
            } finally {
                showLoading(false);
            }
        }

        // 获取筛选参数
        function getFilterParams(page = 1) {
            const params = new URLSearchParams();
            
            // 日期范围
            const dateRange = $('#dateRange').val();
            if (dateRange) {
                const dates = dateRange.split(' - ');
                params.append('date_start', dates[0]);
                params.append('date_end', dates[1]);
            }

            // 其他参数
            params.append('dimension', $('#dimension').val());
            params.append('min_decline_rate', $('#minDeclineRate').val());
            params.append('min_amount', $('#minAmount').val());
            
            const searchTerm = $('#searchInput').val().trim();
            if (searchTerm) {
                params.append('search', searchTerm);
            }
            
            // 分页参数
            params.append('page', page);
            params.append('page_size', 50); // 目前固定，可以后续添加控件

            return params.toString();
        }

        // 更新统计信息
        function updateStatistics(stats) {
            $('#decliningCount').text(stats.declining_count || 0);
            $('#growingCount').text(stats.growing_count || 0);
            $('#totalCompared').text(stats.total_compared_spus || 0);
            $('#declineRate').text((stats.decline_rate || 0) + '%');

            // 格式化销售额
            const currentAmount = formatCurrency(stats.total_current_amount || 0);
            $('#totalCurrentAmount').text(currentAmount);

            // 整体增长率
            const growthRate = stats.overall_growth_rate || 0;
            const growthElement = $('#overallGrowthRate');
            growthElement.text(growthRate.toFixed(2) + '%');
            
            // 根据增长率设置颜色
            if (growthRate < 0) {
                growthElement.removeClass('text-success').addClass('trend-down');
            } else {
                growthElement.removeClass('trend-down').addClass('text-success');
            }

            // 日期范围
            if (stats.date_range) {
                $('#currentDateRange').text(`${stats.date_range.current.start} 至 ${stats.date_range.current.end}`);
                $('#lastYearDateRange').text(`对比 ${stats.date_range.last_year.start} 至 ${stats.date_range.last_year.end}`);
            }
        }

        // 更新结果表格
        function updateResultsTable(data, pagination) {
            const tbody = $('#resultsTableBody');
            tbody.empty();

            // 根据维度显示/隐藏商品编码列
            const dimension = $('#dimension').val();
            const productCodeHeader = $('.product-code-header');
            const productCodeCells = $('.product-code-cell');
            
            if (dimension === 'style_code') {
                productCodeHeader.hide();
                productCodeCells.hide();
            } else {
                productCodeHeader.show();
                productCodeCells.show();
            }

            data.forEach((item, index) => {
                const rank = (pagination.page - 1) * pagination.page_size + index + 1;
                
                // 构建对比时间显示
                const compareTimeDisplay = currentStats.date_range ? 
                    `<div class="compare-time">
                        <div class="current-year text-primary">
                            <i class="bi bi-calendar-check me-1"></i>
                            ${currentStats.date_range.current.start} ~ ${currentStats.date_range.current.end}
                        </div>
                        <div class="vs-divider text-muted my-1">
                            <small>对比</small>
                        </div>
                        <div class="last-year text-secondary">
                            <i class="bi bi-calendar me-1"></i>
                            ${currentStats.date_range.last_year.start} ~ ${currentStats.date_range.last_year.end}
                        </div>
                    </div>` : 
                    '<span class="text-muted">-</span>';

                const row = `
                    <tr data-item='${JSON.stringify(item)}'>
                        <td><span class="rank-badge">${rank}</span></td>
                        <td><span class="code-text">${item.style_code || '-'}</span></td>
                        <td class="product-code-cell" ${dimension === 'style_code' ? 'style="display:none"' : ''}>
                            <span class="code-text">${item.product_code || '-'}</span>
                        </td>
                        <td>
                            <div class="text-truncate-custom" title="${item.product_name || '-'}">
                                ${item.product_name || '-'}
                            </div>
                        </td>
                        <td>${compareTimeDisplay}</td>
                        <td><span class="amount-text amount-primary">¥${formatNumber(item.current_amount)}</span></td>
                        <td><span class="amount-text">¥${formatNumber(item.last_year_amount)}</span></td>
                        <td><span class="amount-text amount-danger">¥${formatNumber(item.decline_amount)}</span></td>
                        <td>
                            <span class="trend-indicator trend-down">
                                ${item.growth_rate.toFixed(2)}%
                                <i class="bi bi-arrow-down"></i>
                            </span>
                        </td>
                        <td>
                            <span class="decline-badge ${getDeclineLevelClass(item.decline_level)}">
                                ${item.decline_level}
                            </span>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });
        }

        // 获取下降程度对应的CSS类
        function getDeclineLevelClass(level) {
            const levelMap = {
                '严重下降': 'decline-severe',
                '显著下降': 'decline-significant', 
                '轻微下降': 'decline-moderate',
                '微降': 'decline-mild'
            };
            
            for (let key in levelMap) {
                if (level && level.includes(key.replace('下降', ''))) {
                    return levelMap[key];
                }
            }
            
            return 'decline-mild';
        }

        // 辅助函数
        function showLoading(show) {
            if (show) {
                $('#loadingSpinner').show();
            } else {
                $('#loadingSpinner').hide();
            }
        }

        function hideAllSections() {
            $('#statisticsSection, #resultsSection, #emptyState').hide();
        }

        function showSection(sectionId) {
            $('#' + sectionId).show();
        }

        function formatNumber(num) {
            return parseFloat(num || 0).toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }

        function formatCurrency(amount) {
            if (amount >= 10000) {
                return '¥' + (amount / 10000).toFixed(1) + '万';
            } else {
                return '¥' + formatNumber(amount);
            }
        }

        function updatePagination(page, totalPages) {
            const paginationUl = $('#pagination');
            paginationUl.empty();

            if (totalPages <= 1) return;

            // 上一页
            paginationUl.append(`
                <li class="page-item ${page === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="event.preventDefault(); loadDeclineAnalysis(${page - 1})">
                        <i class="bi bi-chevron-left"></i>
                    </a>
                </li>
            `);

            const startPage = Math.max(1, page - 2);
            const endPage = Math.min(totalPages, page + 2);

            if (startPage > 1) {
                paginationUl.append(`<li class="page-item"><a class="page-link" href="#" onclick="event.preventDefault(); loadDeclineAnalysis(1)">1</a></li>`);
                if (startPage > 2) {
                    paginationUl.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                paginationUl.append(`
                    <li class="page-item ${i === page ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="event.preventDefault(); loadDeclineAnalysis(${i})">${i}</a>
                    </li>
                `);
            }
            
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationUl.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
                }
                paginationUl.append(`<li class="page-item"><a class="page-link" href="#" onclick="event.preventDefault(); loadDeclineAnalysis(${totalPages})">${totalPages}</a></li>`);
            }

            // 下一页
            paginationUl.append(`
                <li class="page-item ${page === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="event.preventDefault(); loadDeclineAnalysis(${page + 1})">
                        <i class="bi bi-chevron-right"></i>
                    </a>
                </li>
            `);
        }

        // 导出CSV
        function exportToCSV() {
            if (currentData.length === 0) {
                alert('暂无数据可导出');
                return;
            }

            // 根据维度调整表头
            const dimension = $('#dimension').val();
            const headers = dimension === 'style_code' 
                ? ['排名', '款式编码', '商品名称', '对比时间段', '今年销售额', '去年销售额', '下降金额', '同比增长率', '下降程度']
                : ['排名', '款式编码', '商品编码', '商品名称', '对比时间段', '今年销售额', '去年销售额', '下降金额', '同比增长率', '下降程度'];
            
            // 构建对比时间段文本
            const compareTimeText = currentStats.date_range 
                ? `${currentStats.date_range.current.start}~${currentStats.date_range.current.end} vs ${currentStats.date_range.last_year.start}~${currentStats.date_range.last_year.end}`
                : '同期对比';

            const csvContent = [
                headers.join(','),
                ...currentData.map((item, index) => {
                    const baseData = [
                        index + 1,
                        item.style_code || '',
                        ...(dimension === 'product_code' ? [item.product_code || ''] : []),
                        `"${(item.product_name || '').replace(/"/g, '""')}"`,
                        `"${compareTimeText}"`,
                        item.current_amount,
                        item.last_year_amount,
                        item.decline_amount,
                        item.growth_rate.toFixed(2) + '%',
                        item.decline_level
                    ];
                    return baseData.join(',');
                })
            ].join('\n');

            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `SPU同比下降分析_${moment().format('YYYY-MM-DD_HHmmss')}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // 提示导出成功
            const toast = `
                <div class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="bi bi-check-circle me-2"></i>数据导出成功！
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;
            $('body').append(toast);
            $('.toast').toast('show');
            setTimeout(() => $('.toast').remove(), 3000);
        }

        // 调整筛选条件
        function adjustFilters() {
            $('#minDeclineRate').val('5');  // 降低下降幅度阈值
            $('#minAmount').val('500');     // 降低最小销售额
            loadDeclineAnalysis();
        }
    </script>
</body>
</html> 