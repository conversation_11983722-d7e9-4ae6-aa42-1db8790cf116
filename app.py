from pathlib import Path
from flask import Flask, request, jsonify, render_template, make_response, send_file
from flask_cors import CORS
import json
import csv
import os
import random
import sqlite3
from datetime import datetime, timedelta
from utils.sales_data import read_sales_data, calculate_dashboard_data, calculate_trends, get_multi_day_sales_data
import glob
import pandas as pd
import re
from functools import wraps
from werkzeug.utils import secure_filename
import uuid
import io
import numpy as np
import math
import tempfile
from collections import defaultdict
import time

app = Flask(__name__, static_folder='static')

# 安全删除数据库文件的工具函数
def safe_remove_database(db_path):
    """
    安全删除数据库文件，如果无法删除则重命名为备份文件
    """
    if not os.path.exists(db_path):
        return True
        
    try:
        # 先尝试关闭可能的数据库连接
        import gc
        gc.collect()
        
        # 短暂等待，让系统释放文件句柄
        import time
        time.sleep(0.1)
        
        # 尝试删除文件
        os.remove(db_path)
        print(f"已删除数据库文件: {db_path}")
        return True
    except PermissionError:
        print(f"数据库文件被占用，尝试重命名: {db_path}")
        # 如果无法删除，尝试多次重命名
        for attempt in range(3):
            backup_path = f"{db_path}.backup_{int(time.time())}_{attempt}"
            try:
                os.rename(db_path, backup_path)
                print(f"已重命名数据库文件为备份: {backup_path}")
                return True
            except Exception as rename_error:
                print(f"重命名尝试 {attempt + 1} 失败: {rename_error}")
                if attempt < 2:  # 不是最后一次尝试
                    time.sleep(0.5)  # 等待一下再试
                continue
        
        # 所有重命名尝试都失败，记录警告但继续执行
        print(f"警告: 无法删除或重命名数据库文件 {db_path}，将直接覆盖")
        return False
    except Exception as delete_error:
        print(f"删除数据库文件时出错: {delete_error}")
        return False
CORS(app, supports_credentials=True) # 启用跨域资源共享并支持credentials

# --- 用户和权限管理 ---
ACCOUNTS_FILE = '宜承账号.json'
VALID_ROLES = ['admin', 'yunying', 'art', 'caigou', 'caiwu', 'kefu']

def read_accounts():
    """读取账号信息"""
    try:
        if not os.path.exists(ACCOUNTS_FILE):
            # 如果文件不存在，创建一个包含默认admin账号的示例文件
            default_data = {
                "zhanghu": [
                    {
                        "user": "admin",
                        "password": "admin", # 初始密码，建议修改
                        "role": "admin",
                        "tags": ["系统管理员"]
                    }
                ]
            }
            with open(ACCOUNTS_FILE, 'w', encoding='utf-8') as f:
                json.dump(default_data, f, ensure_ascii=False, indent=4)
            print(f"警告: {ACCOUNTS_FILE} 不存在，已创建包含默认 admin 账号的示例文件。")
            return default_data
        
        with open(ACCOUNTS_FILE, 'r', encoding='utf-8') as f:
            accounts_data = json.load(f)
            if "zhanghu" not in accounts_data or not isinstance(accounts_data["zhanghu"], list):
                 print(f"错误: {ACCOUNTS_FILE} 格式不正确，缺少 'zhanghu' 列表。")
                 # 返回一个默认结构以防止崩溃，但可能是空的
                 return {"zhanghu": []}
            return accounts_data
    except json.JSONDecodeError:
        print(f"错误: {ACCOUNTS_FILE} JSON 解析失败。")
        return {"zhanghu": []}
    except Exception as e:
        print(f"读取账号文件失败: {e}")
        return {"zhanghu": []}

def write_accounts(accounts_data):
    """写入账号信息"""
    try:
        with open(ACCOUNTS_FILE, 'w', encoding='utf-8') as f:
            json.dump(accounts_data, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        print(f"写入账号文件失败: {e}")
        return False

def admin_required(f):
    """检查用户是否为管理员的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user_role = request.cookies.get('userRole')
        if user_role != 'admin':
            return jsonify({"success": False, "message": "权限不足，需要管理员权限"}), 403
        return f(*args, **kwargs)
    return decorated_function

# --- 现有函数 (部分可能需要微调) ---

# 读取CSV文件，处理中文编码
def read_csv_file(file_path):
    shops = []
    try:
        with open(file_path, 'r', encoding='gbk') as f:
            reader = csv.reader(f)
            headers = next(reader)  # 跳过表头
            for row in reader:
                if row:  # 确保行不为空
                    shops.append(row)
        return shops
    except Exception as e:
        print(f"读取CSV文件失败: {e}")
        return []

# 读取推广金额数据
def read_promotion_data(date_str=None, date_range=None):
    """
    读取推广金额数据
    参数:
        date_str: 单日日期字符串，格式为YYYYMMDD
        date_range: 日期范围列表，每个元素为YYYYMMDD格式的字符串
    返回:
        dict: 以店铺名为键，推广金额为值的字典
    """
    promo_data = {}
    
    # 处理多日期范围
    if date_range and isinstance(date_range, list) and len(date_range) > 0:
        print(f"读取多日期推广费数据: {date_range[0]} 至 {date_range[-1]}")
        # 遍历每个日期，累加推广费
        for single_date in date_range:
            single_date_data = read_single_day_promotion(single_date)
            # 合并数据
            for shop_name, amount in single_date_data.items():
                if shop_name in promo_data:
                    promo_data[shop_name] += amount
                else:
                    promo_data[shop_name] = amount
        return promo_data
    
    # 单日处理
    return read_single_day_promotion(date_str)

def read_single_day_promotion(date_str):
    """
    读取单日推广金额数据
    参数:
        date_str: 日期字符串，格式为YYYYMMDD
    返回:
        dict: 以店铺名为键，推广金额为值的字典
    """
    promo_data = {}
    if not date_str:
        return promo_data
        
    csv_path = os.path.join('static', 'temp', f"{date_str}账户余额.csv")

    # 如果指定日期的文件不存在，尝试查找最近的文件
    if not os.path.exists(csv_path):
        print(f"推广金额文件不存在: {csv_path}")

        # 获取temp目录下所有账户余额文件
        temp_dir = os.path.join('static', 'temp')
        if os.path.exists(temp_dir):
            files = [f for f in os.listdir(temp_dir) if f.endswith('账户余额.csv')]

            if files:
                # 按文件名排序（日期格式YYYYMMDD会自然排序）
                files.sort(reverse=True)
                csv_path = os.path.join(temp_dir, files[0])
                print(f"使用最近的账户余额文件: {files[0]}")
            else:
                print("未找到任何账户余额文件")
                return promo_data
        else:
            print(f"目录不存在: {temp_dir}")
            return promo_data

    # 尝试不同的编码方式读取文件
    encodings = ['gbk', 'utf-8', 'gb18030', 'gb2312', 'cp936']
    for encoding in encodings:
        try:
            with open(csv_path, 'r', encoding=encoding) as f:
                reader = csv.reader(f)
                for row in reader:
                    if row and len(row) >= 4:
                        shop_name = row[0].strip()
                        category = row[1].strip() if len(row) > 1 else ""
                        type_name = row[2].strip() if len(row) > 2 else ""
                        amount_str = row[3].strip() if len(row) > 3 else ""

                        # 判断是否为推广费相关数据
                        if category == "推广费日账单" and type_name == "正常支出数据" and amount_str:
                            try:
                                # 尝试转换金额为浮点数
                                amount = float(amount_str)
                                if shop_name in promo_data:
                                    promo_data[shop_name] += amount
                                else:
                                    promo_data[shop_name] = amount
                            except ValueError:
                                pass
                                #print(f"无法转换金额为数字: {amount_str}")
            print(f"成功使用 {encoding} 编码读取推广费数据: {csv_path}")
            return promo_data
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"使用 {encoding} 编码读取推广费数据失败: {e}")
            continue

    print(f"无法读取推广费数据文件: {csv_path}")
    return promo_data

# 登录验证接口 (修改)
@app.route('/api/login', methods=['POST'])
def login():
    try:
        data = request.json
        username = data.get('username')
        password = data.get('password')

        accounts_data = read_accounts()
        accounts = accounts_data.get('zhanghu', [])

        # 验证用户名和密码
        for account in accounts:
            if account.get('user') == username and account.get('password') == password:
                # 获取角色，不设置默认值，必须在账号中明确定义
                if 'role' not in account or not account['role']:
                    print(f"用户 {username} 登录失败：账号未设置角色")
                    return jsonify({"success": False, "message": "账号未设置角色，请联系管理员"}), 400
                
                role = account['role']
                tags = account.get('tags', []) # 获取标签
                permissions = account.get('permissions', []) # 获取权限
                
                # 确保权限是一个列表
                if permissions is None:
                    permissions = []
                
                # 如果是美工角色且没有权限，添加美工任务管理权限
                if role == 'art' and (not permissions or len(permissions) == 0):
                    permissions = ['art-task-management']

                # 创建响应并设置 Cookies
                response_data = {"success": True, "role": role, "user": username, "tags": tags}
                response = make_response(jsonify(response_data))

                response.set_cookie('user', username, max_age=3600*24*7, samesite='Lax') 
                response.set_cookie('userRole', role, max_age=3600*24*7, samesite='Lax')
                response.set_cookie('name', account.get('name', ''), max_age=3600*24*7, samesite='Lax')
                response.set_cookie('loginAuth', 'true', max_age=3600*24*7, samesite='Lax')
                
                # 设置角色名称cookie（使用拼音）
                role_name_map = {
                    'admin': 'guanliyuan',
                    'art': 'meigong',
                    'yunying': 'yunying',
                    'caigou': 'caigou',
                    'caiwu': 'caiwu',
                    'kefu': 'kefu',
                    'operator': 'caozuoyuan'
                }
                role_name = role_name_map.get(role, role)  # 如果找不到对应的角色名，使用原始角色值
                response.set_cookie('roleName', role_name, max_age=3600*24*7, samesite='Lax')
                
                # 将权限设置为cookie，使用标准JSON格式并确保转义正确
                if permissions:
                    # 确保美工角色一定有美工任务管理权限
                    if role == 'art' and 'art-task-management' not in permissions:
                        permissions.append('art-task-management')
                        
                    # 简单数组不需要复杂的序列化，直接转换为JSON字符串
                    permissions_json = json.dumps(permissions)
                    response.set_cookie('userPermissions', permissions_json, max_age=3600*24*7, samesite='Lax')
                elif role == 'art':
                    # 如果是美工角色但没有权限，默认设置美工任务管理权限
                    permissions_json = json.dumps(['art-task-management'])
                    response.set_cookie('userPermissions', permissions_json, max_age=3600*24*7, samesite='Lax')
                
                print(f"用户 {username} ({role}) 登录成功")
                return response

        print(f"用户 {username} 登录失败：用户名或密码错误")
        return jsonify({"success": False, "message": "用户名或密码错误"}), 401
    except Exception as e:
        print(f"登录验证失败: {e}")
        return jsonify({"success": False, "message": "系统错误，请稍后重试"}), 500

# --- 新的用户管理 API ---

@app.route('/api/users', methods=['GET'])
@admin_required
def get_users():
    """获取所有用户列表 (管理员)"""
    try:
        accounts_data = read_accounts()
        users_list = []
        for acc in accounts_data.get('zhanghu', []):
            # 不返回密码
            user_info = {
                "user": acc.get("user", ""),
                "username": acc.get("user", ""),  # 兼容前端
                "role": acc.get("role", ""),
                "name": acc.get("name", ""),
                "tags": acc.get("tags", []),
                "permissions": acc.get("permissions", [])
            }
            users_list.append(user_info)
        return jsonify({"success": True, "data": users_list})
    except Exception as e:
        print(f"获取用户列表失败: {e}")
        return jsonify({"success": False, "message": "获取用户列表失败"}), 500

@app.route('/api/users', methods=['POST'])
@admin_required
def create_user():
    """创建新用户 (管理员)"""
    try:
        data = request.json
        username = data.get('username')
        password = data.get('password')
        role = data.get('role')
        name = data.get('name', '')  # 显示名称
        tags = data.get('tags', [])  # 可选的标签
        permissions = data.get('permissions', [])  # 用户权限

        if not username or not password or not role:
            return jsonify({"success": False, "message": "缺少必要信息 (username, password, role)"}), 400

        if role not in VALID_ROLES:
            return jsonify({"success": False, "message": f"无效的角色，可选角色: {', '.join(VALID_ROLES)}"}), 400
        
        if not isinstance(tags, list):
             return jsonify({"success": False, "message": "标签必须是一个列表"}), 400
             
        if not isinstance(permissions, list):
             return jsonify({"success": False, "message": "权限必须是一个列表"}), 400

        accounts_data = read_accounts()
        accounts = accounts_data.get('zhanghu', [])

        # 检查用户名是否已存在
        if any(acc.get('user') == username for acc in accounts):
            return jsonify({"success": False, "message": f"用户名 '{username}' 已存在"}), 409 # 409 Conflict

        # 添加新用户
        new_user = {
            "user": username,
            "password": password, # 注意：实际应用中密码应加密存储
            "role": role,
            "name": name,
            "tags": tags,
            "permissions": permissions
        }
        accounts.append(new_user)
        accounts_data['zhanghu'] = accounts

        if write_accounts(accounts_data):
            print(f"管理员创建了新用户: {username} ({role})")
            # 不返回密码
            user_copy = new_user.copy()
            del user_copy['password']
            return jsonify({"success": True, "message": "用户创建成功", "user": user_copy}), 201 # 201 Created
        else:
            return jsonify({"success": False, "message": "保存用户信息失败"}), 500

    except Exception as e:
        print(f"创建用户失败: {e}")
        return jsonify({"success": False, "message": "创建用户失败"}), 500

@app.route('/api/users/<username>', methods=['DELETE'])
@admin_required
def delete_user(username):
    """删除用户 (管理员)"""
    try:
        if username.lower() == 'admin':
            return jsonify({"success": False, "message": "不能删除 admin 账号"}), 403

        accounts_data = read_accounts()
        accounts = accounts_data.get('zhanghu', [])
        original_length = len(accounts)

        # 过滤掉要删除的用户
        accounts_filtered = [acc for acc in accounts if acc.get('user') != username]

        if len(accounts_filtered) == original_length:
            return jsonify({"success": False, "message": f"未找到用户 '{username}'"}), 404

        accounts_data['zhanghu'] = accounts_filtered

        if write_accounts(accounts_data):
            print(f"管理员删除了用户: {username}")
            return jsonify({"success": True, "message": f"用户 '{username}' 删除成功"})
        else:
            return jsonify({"success": False, "message": "保存用户信息失败"}), 500

    except Exception as e:
        print(f"删除用户失败: {e}")
        return jsonify({"success": False, "message": "删除用户失败"}), 500

# --- 其他现有 API (保持不变或微调) ---

# 获取店铺数据接口
@app.route('/api/shops', methods=['GET'])
def get_shops():
    try:
        start_date_str = request.args.get('startDate') # Expected format YYYY-MM-DD
        end_date_str = request.args.get('endDate')     # Expected format YYYY-MM-DD

        date_range_for_display = ""
        query_date_range = []   # List of YYYYMMDD strings for sales/promo data iteration

        if start_date_str and end_date_str:
            print(f"按日期范围查询店铺数据: {start_date_str} ~ {end_date_str}")
            start_dt = datetime.strptime(start_date_str, "%Y-%m-%d")
            end_dt = datetime.strptime(end_date_str, "%Y-%m-%d")
            
            current_dt = start_dt
            while current_dt <= end_dt:
                query_date_range.append(current_dt.strftime("%Y%m%d"))
                current_dt += timedelta(days=1)
            
            if start_date_str == end_date_str:
                date_range_for_display = start_date_str
            else:
                date_range_for_display = f"{start_date_str}~{end_date_str}"
        else:
            # Default to yesterday if no specific range is provided
            print(f"未提供日期范围，默认查询昨天的数据")
            yesterday_dt = datetime.now() - timedelta(days=1)
            yesterday_yyyymmdd = yesterday_dt.strftime("%Y%m%d")
            
            query_date_range.append(yesterday_yyyymmdd)
            date_range_for_display = yesterday_dt.strftime("%Y-%m-%d")

        if not query_date_range: # Final fallback
            print("警告: query_date_range 为空, 这不应该发生。回退到昨天。")
            yesterday_yyyymmdd = get_yesterday_date()
            query_date_range = [yesterday_yyyymmdd]
            date_range_for_display = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
            
        print(f"最终查询日期范围 (YYYYMMDD): {query_date_range}")
        print(f"用于显示的日期范围: {date_range_for_display}")

        # 读取CSV文件
        shops_data_source = read_csv_file('店铺-账号信息.csv')

        # 获取指定日期范围内的新上链接数据
        shop_link_counts = {}
        
        # 遍历日期范围，累计每个日期的新链接数据
        for date_yyyymmdd in query_date_range:
            csv_filename = f"{date_yyyymmdd}新上链接.csv"
            csv_path = os.path.join('static', 'temp', csv_filename)
            
            if os.path.exists(csv_path):
                # 处理此日期的新上链接数据
                encodings = ['gbk', 'utf-8', 'gb18030', 'gb2312', 'cp936']
                for encoding in encodings:
                    try:
                        with open(csv_path, 'r', encoding=encoding) as f:
                            reader = csv.reader(f)
                            next(reader)  # 跳过表头
                            for row in reader:
                                if row and len(row) > 0:
                                    shop_name = row[0].strip()
                                    if shop_name in shop_link_counts:
                                        shop_link_counts[shop_name] += 1
                                    else:
                                        shop_link_counts[shop_name] = 1
                        print(f"成功使用 {encoding} 编码读取新上链接CSV文件: {csv_path}")
                        break 
                    except UnicodeDecodeError:
                        continue
                    except Exception as e:
                        print(f"使用 {encoding} 编码读取新上链接CSV文件时发生错误: {str(e)}")
                        continue
            else:
                print(f"日期 {date_yyyymmdd} 的新上链接文件不存在: {csv_path}")
        
        # 如果在查询日期范围内没有找到任何新上链接文件，则尝试获取最近的文件作为备选
        if not shop_link_counts:
            print(f"在整个日期范围内未找到任何新上链接文件，尝试查找最近的文件")
            temp_dir = os.path.join('static', 'temp')
            if os.path.exists(temp_dir):
                files = [f for f in os.listdir(temp_dir) if f.endswith('新上链接.csv') and '新上链接' in f]
                if files:
                    files.sort(key=lambda name: os.path.getmtime(os.path.join(temp_dir, name)), reverse=True)
                    latest_file = files[0]
                    print(f"使用最近的新上链接文件: {latest_file}")
                    
                    latest_path = os.path.join(temp_dir, latest_file)
                    encodings = ['gbk', 'utf-8', 'gb18030', 'gb2312', 'cp936']
                    for encoding in encodings:
                        try:
                            with open(latest_path, 'r', encoding=encoding) as f:
                                reader = csv.reader(f)
                                next(reader)  # 跳过表头
                                for row in reader:
                                    if row and len(row) > 0:
                                        shop_name = row[0].strip()
                                        if shop_name in shop_link_counts:
                                            shop_link_counts[shop_name] += 1
                                        else:
                                            shop_link_counts[shop_name] = 1
                            print(f"成功使用 {encoding} 编码读取最近的新上链接CSV文件: {latest_path}")
                            break
                        except UnicodeDecodeError:
                            continue
                        except Exception as e:
                            print(f"使用 {encoding} 编码读取最近的新上链接CSV文件时发生错误: {str(e)}")
                            continue
                else:
                    print(f"在 {temp_dir} 中未找到任何新上链接文件。")
            else:
                print(f"目录 {temp_dir} 不存在。")

        # 获取销售数据
        sales_data = {}
        # Iterate through each date in the query_date_range (YYYYMMDD format)
        for date_str_yyyymmdd in query_date_range:
            sales_file_path = os.path.join('static', 'temp', f"{date_str_yyyymmdd}销售数据.csv")
            if os.path.exists(sales_file_path):
                encodings = ['gbk', 'utf-8', 'gb18030', 'gb2312', 'cp936']
                processed_file = False
                for encoding in encodings:
                    try:
                        with open(sales_file_path, 'r', encoding=encoding) as f:
                            reader = csv.reader(f)
                            next(reader)  # 跳过表头
                            for row in reader:
                                if row and len(row) >= 28: # Ensure enough columns
                                    shop_name = row[0].strip()
                                    if shop_name not in sales_data:
                                        sales_data[shop_name] = {
                                            'salesAmount': 0.0, 'ordersCount': 0, 'buyersCount': 0,
                                            'conversionRate': 0.0, 'refundAmount': 0.0, 'refundOrders': 0,
                                            'refundDSR': 0.0, 'refundRating': 0.0, 'days_contributed': 0
                                        }
                                    
                                    sales_data[shop_name]['salesAmount'] += float(row[2]) if row[2] else 0.0
                                    sales_data[shop_name]['ordersCount'] += int(row[3]) if row[3] else 0
                                    sales_data[shop_name]['buyersCount'] += int(row[4]) if row[4] and len(row) > 4 else 0
                                    sales_data[shop_name]['refundAmount'] += float(row[9]) if row[9] and len(row) > 9 else 0.0
                                    sales_data[shop_name]['refundOrders'] += int(row[10]) if row[10] and len(row) > 10 else 0
                                    
                                    # For rates, DSR, rating - take the value from the last day in the range
                                    if date_str_yyyymmdd == query_date_range[-1]:
                                        sales_data[shop_name]['conversionRate'] = float(row[6]) * 100 if row[6] and len(row) > 6 else 0.0
                                        sales_data[shop_name]['refundDSR'] = float(row[26]) if row[26] and len(row) > 26 else 0.0
                                        sales_data[shop_name]['refundRating'] = float(row[27]) if row[27] and len(row) > 27 else 0.0
                                    
                                    sales_data[shop_name]['days_contributed'] +=1 # Track how many days contributed data

                        print(f"成功使用 {encoding} 编码读取销售数据CSV文件: {sales_file_path}")
                        processed_file = True
                        break 
                    except UnicodeDecodeError:
                        continue
                    except Exception as e:
                        print(f"使用 {encoding} 编码读取销售数据CSV文件 {sales_file_path} 时发生错误: {str(e)}")
                        continue
                if not processed_file:
                    print(f"无法使用任何编码处理销售文件: {sales_file_path}")
            else:
                print(f"销售数据文件不存在: {sales_file_path}")
        
        # For conversionRate, DSR, rating: if multiple days, they are taken from the last day.
        # If only one day, they are from that day. If no data for last day, they'll be 0.
        # This logic is implicitly handled by the `if date_str_yyyymmdd == query_date_range[-1]:` block.


        # 获取推广金额数据 -
        # query_date_range is a list of YYYYMMDD strings
        promo_data = read_promotion_data(date_range=query_date_range) 
        print(f"成功读取 {len(query_date_range)} 天的推广费数据 (可能为空)")


        # 处理数据
        shops_output = []
        for shop_info_row in shops_data_source:
            shop_name = shop_info_row[2] if len(shop_info_row) > 2 else "未知店铺"
            operator = shop_info_row[6] if len(shop_info_row) > 6 and shop_info_row[6].strip() != "" else "暂无数据"

            new_links_count = shop_link_counts.get(shop_name, 0)

            shop_sales_stats = sales_data.get(shop_name, {})
            daily_sales = shop_sales_stats.get('salesAmount', 0.0)
            daily_orders = shop_sales_stats.get('ordersCount', 0)
            daily_buyers = shop_sales_stats.get('buyersCount',0)
            conversion_rate = shop_sales_stats.get('conversionRate', 0.0) # This is from the last day of range
            refund_amount = shop_sales_stats.get('refundAmount',0.0)
            refund_orders = shop_sales_stats.get('refundOrders',0)
            refund_DSR = shop_sales_stats.get('refundDSR', 0.0) # From last day
            refund_rating = shop_sales_stats.get('refundRating', 0.0) # From last day

            promo_amount_for_shop = promo_data.get(shop_name, 0.0) # Already aggregated by read_promotion_data

            shops_output.append({
                "id": 0,
                "name": shop_name,
                "operator": operator,
                "DSR": str(round(float(refund_DSR) * 100 if refund_DSR else 0.0, 2)) + '%',
                "dailySales": daily_sales,
                "dailyOrders": daily_orders,
                "dailyBuyers": daily_buyers,
                "conversionRate": conversion_rate,
                "refundAmount": refund_amount,
                "refundOrders": refund_orders,
                "productCount": new_links_count,
                "newLinksDate": date_range_for_display,  # 显示整个查询日期范围
                "rating": str(round(float(refund_rating) if refund_rating else 0.0, 2)),
                "promoAmount": promo_amount_for_shop
            })

        return jsonify({"success": True, "data": shops_output})
    except Exception as e:
        print(f"获取店铺数据失败: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"success": False, "message": "系统错误，请稍后重试"}), 500

# 读取店铺-账号信息，建立运营人员 -> 店铺列表的映射 (检查依赖)
def get_operator_shop_mapping():
    mapping = {}
    operators = set()
    brands = set()  # 新增：存储所有品牌
    shop_to_brand = {}  # 新增：店铺到品牌的映射
    brand_to_shops = {}  # 新增：品牌到店铺的映射
    
    file_path = '店铺-账号信息.csv'
    print(f"尝试读取运营人员映射文件: {os.path.abspath(file_path)}") # 打印绝对路径
    if not os.path.exists(file_path):
        print(f"错误: {file_path} 文件不存在，无法进行运营人员筛选。")
        return {}, [], {}, {}, []

    try:
        encodings = ['gbk', 'utf-8', 'gb18030', 'gb2312', 'cp936']
        df = None
        for encoding in encodings:
            try:
                # 重要：指定 dtype=str 防止数字被错误解析, keep_default_na=False 防止空字符串被视为NaN
                df = pd.read_csv(file_path, encoding=encoding, dtype=str, keep_default_na=False)
                print(f"成功使用 {encoding} 编码读取CSV文件: {file_path}")
                break
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"使用 {encoding} 编码读取CSV文件时发生错误: {str(e)}")
                continue
        
        if df is None:
             print(f"错误: 尝试了所有编码但无法读取文件: {file_path}")
             return {}, [], {}, {}, []

        # 尝试通过列名查找，如果失败则回退到索引
        shop_col_name = None
        operator_col_name = None
        brand_col_name = None  # 新增：品牌列名
        possible_shop_names = ['店铺名称', '店铺名']
        possible_operator_names = ['运营', '运营人员']
        possible_brand_names = ['品牌', '品牌名称']  # a12新增：可能的品牌列名

        for name in possible_shop_names:
            if name in df.columns:
                shop_col_name = name
                print(f"找到店铺列名: {shop_col_name}")
                break

        for name in possible_operator_names:
            if name in df.columns:
                operator_col_name = name
                print(f"找到运营列名: {operator_col_name}")
                break
                
        # 新增：查找品牌列名
        for name in possible_brand_names:
            if name in df.columns:
                brand_col_name = name
                print(f"找到品牌列名: {brand_col_name}")
                break
                
        # 如果没有找到店铺列名，尝试使用索引0（A列）
        if not shop_col_name and df.shape[1] > 0:
            shop_col_name = df.columns[0] # 通常第一列是店铺名称
            print(f"未找到店铺列名，使用第一列: {shop_col_name}")
            
        # 如果没有找到运营列名，尝试使用索引3（D列）    
        if not operator_col_name and df.shape[1] > 3:
            operator_col_name = df.columns[3] # 通常第四列是运营
            print(f"未找到运营列名，使用第四列: {operator_col_name}")
            
        # 新增：如果没有找到品牌列名，尝试使用H列（索引7）
        if not brand_col_name and df.shape[1] > 7:
            brand_col_name = df.columns[7]  # H列，索引7
            print(f"未找到品牌列名，使用H列: {brand_col_name}")
            
        if not shop_col_name:
            print("错误: 无法确定店铺名称列，无法进行运营人员映射。")
            return {}, [], {}, {}, []

        processed_rows = 0
        
        for index, row in df.iterrows():
            try:
                shop_name = row[shop_col_name].strip() if shop_col_name and pd.notna(row[shop_col_name]) else ""
                
                # 获取运营人员，如果列不存在或值为空，则设为"未分配"
                if operator_col_name and pd.notna(row[operator_col_name]):
                    operator = row[operator_col_name].strip()
                    if not operator:
                         operator = "未分配"
                else:
                    operator = "未分配"
                    
                # 新增：获取品牌，如果列不存在或值为空，则设为"未分类"
                if brand_col_name and pd.notna(row[brand_col_name]):
                    brand = row[brand_col_name].strip()
                    if not brand:
                        brand = "未分类"
                else:
                    brand = "未分类"
                
                if not shop_name: # 跳过店铺名为空的行
                    # print(f"跳过行 {index+2}: 店铺名为空")
                    continue

                operators.add(operator)
                brands.add(brand)  # 新增：添加到品牌集合
                
                # 建立店铺-品牌映射关系
                shop_to_brand[shop_name] = brand
                
                # 建立品牌-店铺映射关系
                if brand not in brand_to_shops:
                    brand_to_shops[brand] = []
                if shop_name not in brand_to_shops[brand]:
                    brand_to_shops[brand].append(shop_name)
                
                if operator not in mapping:
                    mapping[operator] = []
                if shop_name not in mapping[operator]:
                    mapping[operator].append(shop_name)
                processed_rows += 1
            except KeyError as ke:
                print(f"处理行 {index+2} 时发生 KeyError: {ke} - 请检查列名或索引是否正确。")
                continue # 跳过此行
            except Exception as row_e:
                 print(f"处理行 {index+2} 时发生错误: {row_e}")
                 continue # 跳过此行
                 
        print(f"处理完成，共处理 {processed_rows} 行有效数据。")
        print(f"运营人员映射创建成功，共 {len(operators)} 个运营， {len(mapping)} 个有效映射。")
        print(f"品牌映射创建成功，共 {len(brands)} 个品牌， {len(brand_to_shops)} 个有效映射。")
        
        return mapping, sorted(list(operators)), shop_to_brand, brand_to_shops, sorted(list(brands))

    except Exception as e:
        print(f"读取或处理 {file_path} 文件时发生严重错误: {e}")
        import traceback
        traceback.print_exc()
        return {}, [], {}, {}, []

# 获取近30天的多店铺销售和推广数据 (添加更多日志)
@app.route('/api/shop-trend-data', methods=['GET'])
def get_shop_trend_data():
    print("--- 开始处理 /api/shop-trend-data 请求 ---")
    try:
        print("正在获取运营人员映射...")
        # 修改为接收额外的品牌相关数据
        operator_shop_map, operator_list, shop_to_brand, brand_to_shops, brand_list = get_operator_shop_mapping()
        print(f"获取到 {len(operator_list)} 个运营人员, {len(operator_shop_map)} 个映射。")
        print(f"获取到 {len(brand_list)} 个品牌, {len(brand_to_shops)} 个品牌映射。")

        days = request.args.get('days', default=30, type=int)
        shop_filter = request.args.get('shop', default='')
        operator_filter = request.args.get('operator', default='all') # 显式设为 'all' 而不是空
        brand_filter = request.args.get('brand', default='all') # 新增：品牌筛选参数
        print(f"请求参数: days={days}, shop_filter='{shop_filter}', operator_filter='{operator_filter}', brand_filter='{brand_filter}'")
        
        # 修改日期范围计算，截止到昨天
        yesterday = datetime.now() - timedelta(days=1)
        date_range = [(yesterday - timedelta(days=i)).strftime("%Y%m%d") for i in range(days - 1, -1, -1)]
        formatted_dates = [(yesterday - timedelta(days=i)).strftime("%m-%d") for i in range(days - 1, -1, -1)]
        print(f"日期范围: {date_range[0]} 到 {date_range[-1]}")

        # 用于同比环比的日期：前天和上周昨天
        day_before_yesterday = get_day_before_yesterday_date()
        week_ago_yesterday = (yesterday - timedelta(days=7)).strftime("%Y%m%d")
        print(f"同比日期(前天): {day_before_yesterday}, 环比日期(上周昨天): {week_ago_yesterday}")

        # 按运营人员筛选店铺
        target_shops_by_operator = set()
        if operator_filter and operator_filter != 'all':
            if operator_filter in operator_shop_map:
                target_shops_by_operator = set(operator_shop_map[operator_filter])
                print(f"按运营 '{operator_filter}' 筛选，目标店铺: {len(target_shops_by_operator)} 个")
            else:
                 print(f"警告: 请求的运营人员 '{operator_filter}' 不在映射中，返回空数据。")
                 return jsonify({
                    "success": True,
                    "data": {
                        "dates": formatted_dates,
                        "shops": [],
                        "operators": operator_list,
                        "brands": brand_list,
                        "salesData": {},
                        "promotionData": {},
                        "refundData": {},  # 添加退款数据字段
                        "shopBrands": {}   # 添加店铺品牌映射
                    }
                })
        else:
            print("未指定运营人员筛选或选择 '全部运营'")
            
        # 新增：按品牌筛选店铺
        target_shops_by_brand = set()
        if brand_filter and brand_filter != 'all':
            if brand_filter in brand_to_shops:
                target_shops_by_brand = set(brand_to_shops[brand_filter])
                print(f"按品牌 '{brand_filter}' 筛选，目标店铺: {len(target_shops_by_brand)} 个")
            else:
                print(f"警告: 请求的品牌 '{brand_filter}' 不在映射中，返回空数据。")
                return jsonify({
                    "success": True,
                    "data": {
                        "dates": formatted_dates,
                        "shops": [],
                        "operators": operator_list,
                        "brands": brand_list,
                        "salesData": {},
                        "promotionData": {},
                        "refundData": {},  # 添加退款数据字段
                        "shopBrands": {}   # 添加店铺品牌映射
                    }
                })
        else:
            print("未指定品牌筛选或选择 '全部品牌'")

        all_filtered_shops = set()
        date_shop_sales = {}
        date_shop_promo = {}
        date_shop_refunds = {}  # 添加退款数据的存储

        # 存储特定日期的数据用于同比环比计算
        yesterday_data = {'sales': {}, 'promo': {}, 'refunds': {}}
        day_before_yesterday_data = {'sales': {}, 'promo': {}, 'refunds': {}}
        week_ago_yesterday_data = {'sales': {}, 'promo': {}, 'refunds': {}}
        
        # 处理销售和推广数据
        temp_dir = os.path.join('static', 'temp')
        processed_sales_files = 0
        if os.path.exists(temp_dir):
            for date_str in date_range:
                sales_file_path = os.path.join(temp_dir, f"{date_str}销售数据.csv")
                if not os.path.exists(sales_file_path):
                    # print(f"  销售数据文件不存在: {sales_file_path}")
                    date_shop_sales[date_str] = {}
                    date_shop_refunds[date_str] = {}
                    continue

                processed_sales_files += 1
                # print(f"  处理 {date_str} 的销售数据...")
                
                # 读取销售数据
                try:
                    df = pd.read_csv(sales_file_path, encoding='gbk')
                except UnicodeDecodeError:
                    try:
                        df = pd.read_csv(sales_file_path, encoding='utf-8')
                    except UnicodeDecodeError:
                        df = pd.read_csv(sales_file_path, encoding='gb18030', errors='replace')
                        
                # 重命名列名为标准名称
                try:
                    if '店铺' in df.columns:
                        df = df.rename(columns={'店铺': '店铺名'})
                    
                    # 检查是否有退款列
                    has_refund_column = False
                    for col in df.columns:
                        if '退款' in col and (('金额' in col) or ('量' in col)):
                            if '金额' in col:
                                df = df.rename(columns={col: '退款金额'})
                                has_refund_column = True
                            break
                    
                    # 如果没有退款列，添加一个全为0的列
                    if not has_refund_column:
                        df['退款金额'] = 0.0
                        
                    # 根据运营人员和品牌筛选
                    if operator_filter and operator_filter != 'all' and target_shops_by_operator:
                        # 仅保留特定运营人员的店铺
                        df = df[df['店铺名'].apply(lambda shop: shop in target_shops_by_operator)]
                        
                    # 新增：根据品牌筛选
                    if brand_filter and brand_filter != 'all' and target_shops_by_brand:
                        # 仅保留特定品牌的店铺
                        df = df[df['店铺名'].apply(lambda shop: shop in target_shops_by_brand)]
                    
                    # 如果df为空，则跳过后续处理
                    if df.empty:
                        # print(f"  筛选后没有满足条件的数据，跳过 {date_str}")
                        date_shop_sales[date_str] = {}
                        date_shop_refunds[date_str] = {}
                        continue
                    
                    # 处理销售数据
                    shop_sales = df.groupby('店铺名')['成交金额'].sum().to_dict()
                    date_shop_sales[date_str] = shop_sales
                    
                    # 处理退款数据
                    if has_refund_column:
                        shop_refunds = df.groupby('店铺名')['退款金额'].sum().to_dict()
                        date_shop_refunds[date_str] = shop_refunds
                    else:
                        # 如果没有退款列，则设为0
                        shop_refunds = {shop: 0.0 for shop in shop_sales.keys()}
                        date_shop_refunds[date_str] = shop_refunds
                    
                    # 更新所有店铺集合
                    all_filtered_shops.update(shop_sales.keys())
                    
                    # 保存特定日期的数据用于同比环比
                    yesterday_str = yesterday.strftime("%Y%m%d")
                    if date_str == yesterday_str:
                        yesterday_data['sales'] = shop_sales.copy()
                        yesterday_data['refunds'] = shop_refunds.copy()
                    elif date_str == day_before_yesterday:
                        day_before_yesterday_data['sales'] = shop_sales.copy()
                        day_before_yesterday_data['refunds'] = shop_refunds.copy()
                    elif date_str == week_ago_yesterday:
                        week_ago_yesterday_data['sales'] = shop_sales.copy()
                        week_ago_yesterday_data['refunds'] = shop_refunds.copy()
                    
                    # 处理推广费：调用 read_promotion_data 获取当天按店铺的推广费
                    try:
                        # print(f"    读取 {date_str} 的推广费数据...")
                        daily_promo_map = read_promotion_data(date_str)
                        # print(f"      获取到 {len(daily_promo_map)} 家店铺的推广费数据。")
                        
                        # 只保留在筛选后店铺列表中的推广费数据
                        shop_promo_for_day = {}
                        for shop in all_filtered_shops: # 使用筛选后的店铺列表
                            shop_promo_for_day[shop] = daily_promo_map.get(shop, 0.0) # 获取对应店铺的推广费，没有则为0

                        date_shop_promo[date_str] = shop_promo_for_day
                        
                        # 保存特定日期的推广费数据
                        if date_str == yesterday_str:
                            yesterday_data['promo'] = shop_promo_for_day.copy()
                        elif date_str == day_before_yesterday:
                            day_before_yesterday_data['promo'] = shop_promo_for_day.copy()
                        elif date_str == week_ago_yesterday:
                            week_ago_yesterday_data['promo'] = shop_promo_for_day.copy()
                        
                        # print(f"      为 {len(shop_promo_for_day)} 家筛选后店铺记录了推广费。")
                    except Exception as promo_e:
                        print(f"    读取或处理 {date_str} 推广费数据时出错: {promo_e}")
                        # 即使推广费读取失败，也为当天创建一个空的推广费记录，防止后面出错
                        date_shop_promo[date_str] = {shop: 0.0 for shop in all_filtered_shops}
                        
                        # 为特定日期初始化空的推广费数据
                        if date_str == yesterday_str:
                            yesterday_data['promo'] = {shop: 0.0 for shop in shop_sales.keys()}
                        elif date_str == day_before_yesterday:
                            day_before_yesterday_data['promo'] = {shop: 0.0 for shop in shop_sales.keys()}
                        elif date_str == week_ago_yesterday:
                            week_ago_yesterday_data['promo'] = {shop: 0.0 for shop in shop_sales.keys()}
                
                except Exception as sales_e:
                    print(f"    处理 {date_str} 销售数据时出错: {sales_e}")
                    # 即使出错，也创建空记录，以防后面处理出错
                    date_shop_sales[date_str] = {}
                    date_shop_refunds[date_str] = {}
                    date_shop_promo[date_str] = {}

        else:
             print(f"警告: 临时数据目录 {temp_dir} 不存在。")

        print(f"文件处理完成，有效处理了 {processed_sales_files} 个销售文件。")
        print(f"筛选后得到的店铺列表: {len(all_filtered_shops)} 个")
        
        # 整合数据 (这部分逻辑不变)
        final_shops_list = sorted(list(all_filtered_shops))
        sales_data = {shop: [0.0] * days for shop in final_shops_list} # 使用浮点数以保持一致
        promo_data = {shop: [0.0] * days for shop in final_shops_list}
        refund_data = {shop: [0.0] * days for shop in final_shops_list} # 添加退款数据

        for i, date_str in enumerate(date_range):
            day_sales = date_shop_sales.get(date_str, {})
            day_promo = date_shop_promo.get(date_str, {}) # 现在 day_promo 直接是按店铺的字典
            day_refunds = date_shop_refunds.get(date_str, {}) # 添加退款数据
            
            for shop in final_shops_list:
                sales_data[shop][i] = day_sales.get(shop, 0.0) # 确保浮点数
                # 直接从 day_promo 获取对应店铺的值
                promo_data[shop][i] = day_promo.get(shop, 0.0) # 确保浮点数
                refund_data[shop][i] = day_refunds.get(shop, 0.0) # 添加退款数据
        
        # 计算同比环比数据
        comparison_data = calculate_shop_comparison(
            final_shops_list, 
            yesterday_data, 
            day_before_yesterday_data, 
            week_ago_yesterday_data
        )

        print(f"数据整合完成，为 {len(final_shops_list)} 个店铺生成了 {days} 天的数据。")
        
        # 创建店铺到品牌的映射，仅包含最终筛选出的店铺
        filtered_shop_to_brand = {}
        for shop in final_shops_list:
            filtered_shop_to_brand[shop] = shop_to_brand.get(shop, "未分类")

        # 返回数据
        response_data = {
            "success": True,
            "data": {
                "dates": formatted_dates,
                "shops": final_shops_list,
                "operators": operator_list,
                "brands": brand_list,
                "salesData": sales_data,
                "promotionData": promo_data,
                "refundData": refund_data,  # 添加退款数据
                "comparisonData": comparison_data,  # 添加同比环比数据
                "shopBrands": filtered_shop_to_brand  # 添加店铺到品牌的映射
            }
        }
        print("--- /api/shop-trend-data 请求处理完成 ---")
        return jsonify(response_data)
        
    except Exception as e:
        print(f"获取店铺趋势数据失败 (在主 try-except 块): {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            "success": False,
            "message": "获取店铺趋势数据失败，请查看服务器日志了解详情"
        }), 500

# 计算店铺同比环比数据（昨天vs前天，昨天vs上周昨天）
def calculate_shop_comparison(shops_list, yesterday_data, day_before_yesterday_data, week_ago_yesterday_data):
    """
    计算各店铺的同比环比数据
    同比：昨天与前天对比
    环比：昨天与上周的昨天对比
    """
    comparison_data = {}
    
    for shop in shops_list:
        # 初始化数据结构
        comparison_data[shop] = {
            "sales": {"yoy": 0, "mom": 0, "current": 0, "yoyRef": 0, "momRef": 0},
            "promotion": {"yoy": 0, "mom": 0, "current": 0, "yoyRef": 0, "momRef": 0},
            "roi": {"yoy": 0, "mom": 0, "current": 0, "yoyRef": 0, "momRef": 0}
        }
        
        # 获取昨天的数据（当前值）
        yesterday_sales = yesterday_data['sales'].get(shop, 0)
        yesterday_refunds = yesterday_data['refunds'].get(shop, 0)
        yesterday_promo = yesterday_data['promo'].get(shop, 0)
        yesterday_net_sales = max(0, yesterday_sales - yesterday_refunds)  # 净销售额
        
        # 获取前天的数据（同比参考值）
        dby_sales = day_before_yesterday_data['sales'].get(shop, 0)
        dby_refunds = day_before_yesterday_data['refunds'].get(shop, 0)
        dby_promo = day_before_yesterday_data['promo'].get(shop, 0)
        dby_net_sales = max(0, dby_sales - dby_refunds)  # 净销售额
        
        # 获取上周昨天的数据（环比参考值）
        way_sales = week_ago_yesterday_data['sales'].get(shop, 0)
        way_refunds = week_ago_yesterday_data['refunds'].get(shop, 0)
        way_promo = week_ago_yesterday_data['promo'].get(shop, 0)
        way_net_sales = max(0, way_sales - way_refunds)  # 净销售额
        
        # 保存当前值
        comparison_data[shop]['sales']['current'] = yesterday_net_sales
        comparison_data[shop]['promotion']['current'] = yesterday_promo
        comparison_data[shop]['roi']['current'] = yesterday_net_sales / yesterday_promo if yesterday_promo > 0 else 0
        
        # 保存参考值
        comparison_data[shop]['sales']['yoyRef'] = dby_net_sales
        comparison_data[shop]['promotion']['yoyRef'] = dby_promo
        comparison_data[shop]['roi']['yoyRef'] = dby_net_sales / dby_promo if dby_promo > 0 else 0
        
        comparison_data[shop]['sales']['momRef'] = way_net_sales
        comparison_data[shop]['promotion']['momRef'] = way_promo
        comparison_data[shop]['roi']['momRef'] = way_net_sales / way_promo if way_promo > 0 else 0
        
        # 计算同比变化率 (昨天 vs 前天)
        if dby_net_sales > 0:
            comparison_data[shop]['sales']['yoy'] = ((yesterday_net_sales - dby_net_sales) / dby_net_sales) * 100
        else:
            comparison_data[shop]['sales']['yoy'] = 100 if yesterday_net_sales > 0 else 0
        
        if dby_promo > 0:
            comparison_data[shop]['promotion']['yoy'] = ((yesterday_promo - dby_promo) / dby_promo) * 100
        else:
            comparison_data[shop]['promotion']['yoy'] = 100 if yesterday_promo > 0 else 0
        
        current_roi = comparison_data[shop]['roi']['current']
        yoy_ref_roi = comparison_data[shop]['roi']['yoyRef']
        
        if yoy_ref_roi > 0:
            comparison_data[shop]['roi']['yoy'] = ((current_roi - yoy_ref_roi) / yoy_ref_roi) * 100
        else:
            comparison_data[shop]['roi']['yoy'] = 100 if current_roi > 0 else 0
        
        # 计算环比变化率 (昨天 vs 上周昨天)
        if way_net_sales > 0:
            comparison_data[shop]['sales']['mom'] = ((yesterday_net_sales - way_net_sales) / way_net_sales) * 100
        else:
            comparison_data[shop]['sales']['mom'] = 100 if yesterday_net_sales > 0 else 0
        
        if way_promo > 0:
            comparison_data[shop]['promotion']['mom'] = ((yesterday_promo - way_promo) / way_promo) * 100
        else:
            comparison_data[shop]['promotion']['mom'] = 100 if yesterday_promo > 0 else 0
        
        mom_ref_roi = comparison_data[shop]['roi']['momRef']
        
        if mom_ref_roi > 0:
            comparison_data[shop]['roi']['mom'] = ((current_roi - mom_ref_roi) / mom_ref_roi) * 100
        else:
            comparison_data[shop]['roi']['mom'] = 100 if current_roi > 0 else 0
    
    return comparison_data

# 获取链接列表接口
@app.route('/api/links', methods=['GET'])
def get_links():
    try:
        # 获取查询参数
        search_query = request.args.get('search', '').lower()
        start_date = request.args.get('startDate', '')
        end_date = request.args.get('endDate', '')
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))

        # 如果没有指定日期范围，默认使用最新的数据文件
        if not start_date and not end_date:
            # 获取最新的数据文件
            goods_data_dir = os.path.join('static', 'goods_data')
            if not os.path.exists(goods_data_dir):
                return jsonify({"success": False, "message": "商品数据目录不存在"}), 404
                
            excel_files = [f for f in os.listdir(goods_data_dir) if f.endswith('goods_data.xlsx')]
            if not excel_files:
                return jsonify({"success": False, "message": "未找到商品数据文件"}), 404
                
            # 按文件名排序（日期格式YYYYMMDD会自然排序）
            excel_files.sort(reverse=True)
            latest_file = excel_files[0]
            file_path = os.path.join(goods_data_dir, latest_file)
            
            # 从文件名中提取日期
            date_str = latest_file.split('goods_data.xlsx')[0]
            end_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
            start_date = end_date
        else:
            # 使用指定的日期范围
            # 将日期转换为文件名格式
            start_date_file = start_date.replace('-', '')
            end_date_file = end_date.replace('-', '')
            
            # 查找日期范围内的所有文件
            goods_data_dir = os.path.join('static', 'goods_data')
            excel_files = []
            for date_str in [start_date_file, end_date_file]:
                file_name = f"{date_str}goods_data.xlsx"
                file_path = os.path.join(goods_data_dir, file_name)
                if os.path.exists(file_path):
                    excel_files.append(file_path)
            
            if not excel_files:
                return jsonify({"success": False, "message": "未找到指定日期范围内的数据文件"}), 404
                
            # 使用最近的文件
            file_path = excel_files[0]

        # 使用pandas读取Excel文件
        df = pd.read_excel(file_path)
        
        # 应用搜索过滤
        if search_query:
            # 在商品名称和店铺名称中搜索
            df = df[df['goodsName'].str.lower().str.contains(search_query) | 
                   df['shop_name'].str.lower().str.contains(search_query)]
            
        # 计算总数
        total_count = len(df)
        
        # 准备聚合数据
        aggregated_data = {
            "totalSales": float(df['payOrdrAmtPpr'].sum()),
            "totalQuantity": int(df['payOrdrGoodsQtyPpr'].sum()),
            "totalProfit": float(df['payOrdrAmtPpr'].sum() * 0.4),  # 估算利润为销售额的40%
            "avgProfitRate": 40.0,  # 固定利润率
            "totalAdCost": float(df['payOrdrAmtPpr'].sum() * 0.15),  # 估算广告成本为销售额的15%
            "avgROI": 150.0  # 固定ROI
        }
        
        # 分页
        start_idx = (page - 1) * page_size
        end_idx = min(start_idx + page_size, total_count)
        df_page = df.iloc[start_idx:end_idx]
        
        # 计算总页数
        total_pages = (total_count + page_size - 1) // page_size
        
        # 转换数据为链接列表
        links = []
        for _, row in df_page.iterrows():
            # 计算一些派生值
            sales = float(row['payOrdrAmtPpr']) if row['payOrdrAmtPpr'] >= 0 else 0
            quantity = int(row['payOrdrGoodsQtyPpr']) if row['payOrdrGoodsQtyPpr'] >= 0 else 0
            profit_rate = 40.0  # 假设利润率为40%
            profit = sales * (profit_rate / 100)
            ad_cost = sales * 0.15  # 假设广告成本为销售额的15%
            break_even_roi = 100  # 默认保本ROI为100%
            actual_roi = (sales / ad_cost) * 100 if ad_cost > 0 else 0
            refund_rate = 5.0  # 假设退款率为5%
            
            # 获取价格和成本
            price = sales / quantity if quantity > 0 else 0
            cost = sales * (1 - profit_rate / 100)
            
            # 生成7天销售趋势数据（从Excel数据可用指标生成）
            # 用goodsUvPpr、goodsPvPpr等相关指标的变化率构造趋势
            sales_trend = [
                max(0, int(sales * (1 + row['goodsUvPpr'] * 0.01))),
                max(0, int(sales * (1 + row['goodsPvPpr'] * 0.01))),
                max(0, int(sales * (1 - row['cfmOrdrRtoPpr'] * 0.1))),
                max(0, int(sales * (1 + row['goodsFavCntPpr'] * 0.02))),
                max(0, int(sales * (1 - row['goodsVcrPpr'] * 0.05))),
                max(0, int(sales * (1 + row['payOrdrUsrCntPpr'] * 0.02))),
                max(0, int(sales))
            ]
            
            # 创建链接项
            link = {
                "id": str(row['goodsId']),
                "productName": row['goodsName'],
                "imageUrl": row['hdThumbUrl'] if pd.notna(row['hdThumbUrl']) else f"https://picsum.photos/id/{random.randint(1, 1000)}/60/60",
                "url": row['url'] if pd.notna(row['url']) else f"https://mobile.yangkeduo.com/goods.html?goods_id={row['goodsId']}",
                "price": price,
                "cost": cost,
                "adCost": ad_cost,
                "sales": sales,
                "quantity": quantity,
                "profit": profit,
                "profitRate": profit_rate,
                "refundRate": refund_rate,
                "breakEvenROI": break_even_roi,
                "actualROI": actual_roi,
                "salesTrend": sales_trend,
                "status": row['payOrdrCntPpr'] > 0,  # 如果有订单则为活跃状态
                "operator": "未分配",  # 默认运营人员
                "shop_name": row['shop_name']
            }
            
            links.append(link)
        
        # 生成响应
        response = {
            "success": True,
            "links": links,
            "totalCount": total_count,
            "pagination": {
                "currentPage": page,
                "pageSize": page_size,
                "totalPages": total_pages
            },
            "aggregatedData": aggregated_data,
            "dateRange": {
                "startDate": start_date,
                "endDate": end_date
            }
        }
        
        return jsonify(response)
    except Exception as e:
        print(f"获取链接数据失败: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"success": False, "message": f"系统错误，请稍后重试: {str(e)}"}), 500

# 添加链接总结数据接口
@app.route('/api/links/summary', methods=['GET'])
def get_links_summary():
    try:
        # 获取查询参数
        start_date = request.args.get('startDate', '')
        end_date = request.args.get('endDate', '')
        
        # 如果没有指定日期范围，默认使用最新的数据文件
        if not start_date and not end_date:
            # 获取最新的数据文件
            goods_data_dir = os.path.join('static', 'goods_data')
            if not os.path.exists(goods_data_dir):
                return jsonify({"success": False, "message": "商品数据目录不存在"}), 404
                
            excel_files = [f for f in os.listdir(goods_data_dir) if f.endswith('goods_data.xlsx')]
            if not excel_files:
                return jsonify({"success": False, "message": "未找到商品数据文件"}), 404
                
            # 按文件名排序（日期格式YYYYMMDD会自然排序）
            excel_files.sort(reverse=True)
            latest_file = excel_files[0]
            file_path = os.path.join(goods_data_dir, latest_file)
        else:
            # 使用指定的日期范围
            # 将日期转换为文件名格式
            start_date_file = start_date.replace('-', '')
            end_date_file = end_date.replace('-', '')
            
            # 查找日期范围内的所有文件
            goods_data_dir = os.path.join('static', 'goods_data')
            excel_files = []
            for date_str in [start_date_file, end_date_file]:
                file_name = f"{date_str}goods_data.xlsx"
                file_path = os.path.join(goods_data_dir, file_name)
                if os.path.exists(file_path):
                    excel_files.append(file_path)
            
            if not excel_files:
                return jsonify({"success": False, "message": "未找到指定日期范围内的数据文件"}), 404
                
            # 使用最近的文件
            file_path = excel_files[0]
            
        # 使用pandas读取Excel文件
        import pandas as pd
        df = pd.read_excel(file_path)
        
        # 准备摘要数据
        # 1. 爆款热销 - 基于订单数排名
        hot_selling_df = df.nlargest(10, 'payOrdrCntPpr')
        hot_selling_count = len(df[df['payOrdrCntPpr'] > 0])
        hot_selling_growth = float(df['payOrdrCntPpr'].mean()) * 100
        hot_selling_top = hot_selling_df.iloc[0]['goodsName'] if not hot_selling_df.empty else "无数据"
        
        # 2. 快速增长 - 基于增长率
        fast_growing_df = df.nlargest(10, 'goodsUvPpr')
        fast_growing_count = len(df[df['goodsUvPpr'] > 1.0])
        fast_growing_rate = float(df['goodsUvPpr'].mean()) * 100
        fast_growing_top = fast_growing_df.iloc[0]['goodsName'] if not fast_growing_df.empty else "无数据"
        
        # 3. 利润王牌 - 假设利润为销售额的40%
        df['profit'] = df['payOrdrAmtPpr'] * 0.4
        profit_king_df = df.nlargest(10, 'profit')
        profit_king_amount = float(df['profit'].sum())
        profit_king_growth = 24.3  # 默认值
        profit_king_top = profit_king_df.iloc[0]['goodsName'] if not profit_king_df.empty else "无数据"
        
        # 4. 亏损黑洞 - 假设亏损为负利润
        loss_df = df[df['payOrdrAmtPpr'] < 0].copy()
        loss_df['loss'] = loss_df['payOrdrAmtPpr'].abs() * 0.4
        loss_amount = float(loss_df['loss'].sum()) if not loss_df.empty else 0
        loss_rate = -15.2  # 默认值
        loss_top = loss_df.iloc[0]['goodsName'] if not loss_df.empty else "无数据"
        
        # 构建摘要响应
        summary = {
            "hot_selling": {
                "count": hot_selling_count,
                "growth": hot_selling_growth,
                "top_item": hot_selling_top
            },
            "fast_growing": {
                "count": fast_growing_count,
                "growth": fast_growing_rate,
                "top_item": fast_growing_top
            },
            "profit_king": {
                "amount": profit_king_amount,
                "growth": profit_king_growth,
                "top_item": profit_king_top
            },
            "loss_black_hole": {
                "amount": loss_amount,
                "rate": loss_rate,
                "top_item": loss_top
            }
        }
        
        return jsonify({"success": True, "summary": summary})
    except Exception as e:
        print(f"获取链接摘要数据失败: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"success": False, "message": f"系统错误，请稍后重试: {str(e)}"}), 500

# 切换链接状态接口
@app.route('/api/links/<link_id>/toggle', methods=['POST'])
def toggle_link_status(link_id):
    try:
        # 在实际应用中，这里应该从数据库中读取并更新链接状态
        # 这里仅返回成功响应作为示例
        return jsonify({"success": True})
    except Exception as e:
        print(f"切换链接状态失败: {e}")
        return jsonify({"success": False, "message": "系统错误，请稍后重试"}), 500

# 获取链接趋势数据接口
@app.route('/api/links/trend', methods=['GET'])
def get_links_trend():
    """获取全局链接趋势数据"""
    try:
        start_date = request.args.get('startDate', '')
        end_date = request.args.get('endDate', '')
        
        # 如果没有指定日期范围，默认使用近30天
        if not start_date or not end_date:
            end_date_obj = datetime.now() - timedelta(days=1)
            start_date_obj = end_date_obj - timedelta(days=29)
            start_date = start_date_obj.strftime('%Y-%m-%d')
            end_date = end_date_obj.strftime('%Y-%m-%d')
        
        # 生成趋势数据
        trend_data = generate_links_trend_data(start_date, end_date)
        
        return jsonify({
            "success": True,
            "data": trend_data
        })
    except Exception as e:
        print(f"获取链接趋势数据失败: {e}")
        return jsonify({"success": False, "message": "系统错误，请稍后重试"}), 500

# 获取运营人员链接趋势数据接口
@app.route('/api/links/trend/operator', methods=['GET'])
def get_operator_links_trend():
    """获取特定运营人员的链接趋势数据"""
    try:
        operator = request.args.get('operator', '')
        start_date = request.args.get('startDate', '')
        end_date = request.args.get('endDate', '')
        
        if not operator:
            return jsonify({"success": False, "message": "请指定运营人员"}), 400
        
        # 如果没有指定日期范围，默认使用近30天
        if not start_date or not end_date:
            end_date_obj = datetime.now() - timedelta(days=1)
            start_date_obj = end_date_obj - timedelta(days=29)
            start_date = start_date_obj.strftime('%Y-%m-%d')
            end_date = end_date_obj.strftime('%Y-%m-%d')
        
        # 生成运营人员的趋势数据
        trend_data = generate_operator_links_trend_data(operator, start_date, end_date)
        
        return jsonify({
            "success": True,
            "data": trend_data
        })
    except Exception as e:
        print(f"获取运营人员链接趋势数据失败: {e}")
        return jsonify({"success": False, "message": "系统错误，请稍后重试"}), 500

# 获取店铺链接趋势数据接口
@app.route('/api/links/trend/shop', methods=['GET'])
def get_shop_links_trend():
    """获取特定店铺的链接趋势数据"""
    try:
        shop = request.args.get('shop', '')
        start_date = request.args.get('startDate', '')
        end_date = request.args.get('endDate', '')
        
        if not shop:
            return jsonify({"success": False, "message": "请指定店铺名称"}), 400
        
        # 如果没有指定日期范围，默认使用近30天
        if not start_date or not end_date:
            end_date_obj = datetime.now() - timedelta(days=1)
            start_date_obj = end_date_obj - timedelta(days=29)
            start_date = start_date_obj.strftime('%Y-%m-%d')
            end_date = end_date_obj.strftime('%Y-%m-%d')
        
        # 生成店铺的趋势数据
        trend_data = generate_shop_links_trend_data(shop, start_date, end_date)
        
        return jsonify({
            "success": True,
            "data": trend_data
        })
    except Exception as e:
        print(f"获取店铺链接趋势数据失败: {e}")
        return jsonify({"success": False, "message": "系统错误，请稍后重试"}), 500

# 生成链接趋势数据的辅助函数
def generate_links_trend_data(start_date, end_date):
    """生成全局链接趋势数据"""
    try:
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        trend_data = []
        current_dt = start_dt
        
        while current_dt <= end_dt:
            date_str = current_dt.strftime('%Y%m%d')
            
            # 尝试读取当日新上链接数据
            csv_filename = f"{date_str}新上链接.csv"
            csv_path = os.path.join('static', 'temp', csv_filename)
            
            count = 0
            if os.path.exists(csv_path):
                encodings = ['gbk', 'utf-8', 'gb18030', 'gb2312', 'cp936']
                for encoding in encodings:
                    try:
                        with open(csv_path, 'r', encoding=encoding) as f:
                            reader = csv.reader(f)
                            next(reader, None)  # 跳过表头
                            count = sum(1 for row in reader if row)
                        break
                    except UnicodeDecodeError:
                        continue
                    except Exception:
                        continue
            
            trend_data.append({
                'date': current_dt.strftime('%Y-%m-%d'),
                'count': count
            })
            
            current_dt += timedelta(days=1)
        
        return trend_data
    except Exception as e:
        print(f"生成链接趋势数据失败: {e}")
        return []

def generate_operator_links_trend_data(operator, start_date, end_date):
    """生成特定运营人员的链接趋势数据"""
    try:
        # 获取运营人员对应的店铺
        operator_shop_map, _, _, _, _ = get_operator_shop_mapping()
        operator_shops = operator_shop_map.get(operator, [])
        
        if not operator_shops:
            return []
        
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        trend_data = []
        current_dt = start_dt
        
        while current_dt <= end_dt:
            date_str = current_dt.strftime('%Y%m%d')
            
            # 尝试读取当日新上链接数据
            csv_filename = f"{date_str}新上链接.csv"
            csv_path = os.path.join('static', 'temp', csv_filename)
            
            count = 0
            if os.path.exists(csv_path):
                encodings = ['gbk', 'utf-8', 'gb18030', 'gb2312', 'cp936']
                for encoding in encodings:
                    try:
                        with open(csv_path, 'r', encoding=encoding) as f:
                            reader = csv.reader(f)
                            next(reader, None)  # 跳过表头
                            for row in reader:
                                if row and len(row) > 0 and row[0].strip() in operator_shops:
                                    count += 1
                        break
                    except UnicodeDecodeError:
                        continue
                    except Exception:
                        continue
            
            trend_data.append({
                'date': current_dt.strftime('%Y-%m-%d'),
                'count': count
            })
            
            current_dt += timedelta(days=1)
        
        return trend_data
    except Exception as e:
        print(f"生成运营人员链接趋势数据失败: {e}")
        return []

def generate_shop_links_trend_data(shop_name, start_date, end_date):
    """生成特定店铺的链接趋势数据"""
    try:
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        trend_data = []
        current_dt = start_dt
        
        while current_dt <= end_dt:
            date_str = current_dt.strftime('%Y%m%d')
            
            # 尝试读取当日新上链接数据
            csv_filename = f"{date_str}新上链接.csv"
            csv_path = os.path.join('static', 'temp', csv_filename)
            
            count = 0
            if os.path.exists(csv_path):
                encodings = ['gbk', 'utf-8', 'gb18030', 'gb2312', 'cp936']
                for encoding in encodings:
                    try:
                        with open(csv_path, 'r', encoding=encoding) as f:
                            reader = csv.reader(f)
                            next(reader, None)  # 跳过表头
                            for row in reader:
                                if row and len(row) > 0 and row[0].strip() == shop_name:
                                    count += 1
                        break
                    except UnicodeDecodeError:
                        continue
                    except Exception:
                        continue
            
            trend_data.append({
                'date': current_dt.strftime('%Y-%m-%d'),
                'count': count
            })
            
            current_dt += timedelta(days=1)
        
        return trend_data
    except Exception as e:
        print(f"生成店铺链接趋势数据失败: {e}")
        return []

# 获取财务概览数据接口
@app.route('/api/finance/overview', methods=['GET'])
def get_finance_overview():
    try:
        # 生成模拟数据
        current_month_income = 0
        last_month_income = 0
        current_month_expense = 0
        last_month_expense = 0

        income_trend = round((current_month_income - last_month_income) / last_month_income * 100, 1)
        expense_trend = round((current_month_expense - last_month_expense) / last_month_expense * 100, 1)
        profit_trend = round(((current_month_income - current_month_expense) -
                             (last_month_income - last_month_expense)) /
                            (last_month_income - last_month_expense) * 100, 1)

        overview = {
            "income": {
                "value": current_month_income,
                "trend": income_trend
            },
            "expense": {
                "value": current_month_expense,
                "trend": expense_trend
            },
            "profit": {
                "value": current_month_income - current_month_expense,
                "trend": profit_trend
            }
        }

        return jsonify({"success": True, "data": overview})
    except Exception as e:
        print(f"获取财务概览数据失败: {e}")
        return jsonify({"success": False, "message": "系统错误，请稍后重试"}), 500

# 获取财务明细数据接口
@app.route('/api/finance/details', methods=['GET'])
def get_finance_details():
    try:
        start_date = request.args.get('startDate')
        end_date = request.args.get('endDate')
        report_type = request.args.get('reportType')
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))

        # 生成模拟数据
        types = ['收入', '支出']
        payment_methods = ['支付宝', '微信', '银行转账']
        income_notes = ['服装销售', '数码产品销售', '家居用品销售']
        expense_notes = ['进货支出', '运营费用', '人工费用']

        total_count = 100  # 总记录数
        total_pages = (total_count + page_size - 1) // page_size
        start_index = (page - 1) * page_size

        details = []
        for _ in range(page_size):
            type_value = random.choice(types)
            amount = random.randint(1000, 6000) if type_value == '收入' else -random.randint(500, 3500)

            details.append({
                "date": (datetime.now() - timedelta(days=random.randint(0, 30))).strftime("%Y-%m-%d"),
                "type": type_value,
                "amount": amount,
                "paymentMethod": random.choice(payment_methods),
                "note": random.choice(income_notes if type_value == '收入' else expense_notes)
            })

        current_time = datetime.now().strftime("%Y-%m-%d %H:%M")

        return jsonify({
            "success": True,
            "data": {
                "items": details,
                "pagination": {
                    "currentPage": page,
                    "pageSize": page_size,
                    "totalPages": total_pages,
                    "totalCount": total_count
                },
                "updateTime": current_time
            }
        })
    except Exception as e:
        print(f"获取财务明细数据失败: {e}")
        return jsonify({"success": False, "message": "系统错误，请稍后重试"}), 500

# 获取仪表盘数据接口
@app.route('/api/dashboard', methods=['GET'])
def get_dashboard():
    try:
        # 获取最新的销售数据文件
        yesterday = get_yesterday_date()
        df = read_sales_data(f'static/temp/{yesterday}销售数据.csv')
        if df is None:
            raise Exception("无法读取销售数据文件")

        # 计算仪表盘数据
        dashboard_data = calculate_dashboard_data(df)
        if dashboard_data is None:
            raise Exception("无法计算仪表盘数据")

        # 添加更新时间
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
        dashboard_data['updateTime'] = current_time

        return jsonify({
            'success': True,
            'data': dashboard_data
        })
    except Exception as e:
        print(f"获取仪表盘数据失败: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

# 获取仪表盘数据带趋势接口
@app.route('/api/dashboard/trend', methods=['GET'])
def get_dashboard_trend():
    print("获取仪表盘数据带趋势接口")
    # 添加响应头禁用缓存
    response = jsonify({
        'success': False,
        'message': '初始化响应'
    })
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    try:
        # 获取最新的销售数据文件（昨天）
        yesterday = get_yesterday_date()
        yesterday_df = read_sales_data(f'static/temp/{yesterday}销售数据.csv')
        if yesterday_df is None:
            raise Exception("无法读取昨天销售数据文件")

        # 获取前天的销售数据文件
        day_before_yesterday = get_day_before_yesterday_date()
        before_yesterday_df = read_sales_data(f'static/temp/{day_before_yesterday}销售数据.csv')

        # 计算仪表盘数据
        yesterday_data = calculate_dashboard_data(yesterday_df)
        if yesterday_data is None:
            raise Exception("无法计算昨天仪表盘数据")

        # 如果前天数据可用，计算趋势
        if before_yesterday_df is not None:
            before_yesterday_data = calculate_dashboard_data(before_yesterday_df)
            if before_yesterday_data:
                # 计算涨跌幅
                yesterday_data = calculate_trends(yesterday_data, before_yesterday_data)

        # 获取多天的销售趋势数据
        days = request.args.get('days', default=7, type=int)
        trend_data = get_multi_day_sales_data(days)

        # 更新趋势数据
        if 'sales' in trend_data:
            yesterday_data['salesTrend'] = trend_data['sales']
        if 'promotion' in trend_data:
            yesterday_data['promotionTrend'] = trend_data['promotion']

        return jsonify({
            'success': True,
            'data': yesterday_data
        })
    
    except Exception as e:
        print(f"获取仪表盘趋势数据失败: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

# 获取账户余额数据
@app.route('/api/finance/account-balance', methods=['GET'])
def get_account_balance():
    try:
        # 获取当前日期并格式化为YYYYMMDD格式
        today = datetime.now().strftime("%Y%m%d")

        # 构建CSV文件路径
        csv_filename = f"{today}账户余额.csv"
        temp_path = os.path.join('static', 'temp', csv_filename)

        # 如果当天文件不存在，尝试查找最近的文件
        if not os.path.exists(temp_path):
            # 获取temp目录下所有文件
            temp_dir = os.path.join('static', 'temp')
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)

            files = [f for f in os.listdir(temp_dir) if f.endswith('账户余额.csv')]

            if not files:
                return jsonify({"success": False, "message": f"未找到账户余额数据文件，请确保 {csv_filename} 文件存在于 static/temp 目录下"}), 404

            # 按文件名排序（日期格式YYYYMMDD会自然排序）
            files.sort(reverse=True)
            temp_path = os.path.join(temp_dir, files[0])
            print(f"使用最近的账户余额文件: {files[0]}")

        # 解析CSV文件
        account_data = []
        encodings = ['gbk', 'utf-8', 'gb18030', 'gb2312', 'cp936']
        success = False

        for encoding in encodings:
            try:
                with open(temp_path, 'r', encoding=encoding) as f:
                    reader = csv.reader(f)
                    for row in reader:
                        if row and len(row) >= 4:  # 确保行不为空且至少有4列
                            account_data.append({
                                "shop": row[0],
                                "dataType": row[1],
                                "dataName": row[2],
                                "dataValue": row[3]
                            })
                success = True
                print(f"成功使用 {encoding} 编码读取CSV文件: {temp_path}")
                break
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"使用 {encoding} 编码读取CSV文件时发生错误: {str(e)}")
                continue

        if not success:
            return jsonify({"success": False, "message": "无法解析CSV文件，请确保文件格式正确且使用GBK或UTF-8编码"}), 500

        if not account_data:
            return jsonify({"success": False, "message": "CSV文件中没有有效数据"}), 400

        # 按店铺名称分组数据
        grouped_data = {}
        for item in account_data:
            shop = item["shop"]
            if shop not in grouped_data:
                grouped_data[shop] = []
            grouped_data[shop].append(item)

        return jsonify({"success": True, "data": grouped_data})
    except Exception as e:
        print(f"获取账户余额数据失败: {e}")
        return jsonify({"success": False, "message": f"系统错误，请稍后重试: {str(e)}"}), 500

# 获取团队管理数据接口
@app.route('/api/team-management', methods=['GET'])
def get_team_management():
    try:
        # 获取当前的数据状态
        current_date = request.args.get('date', get_yesterday_date())

        # 如果存在团队管理数据的JSON文件，则从文件中读取
        team_data_path = os.path.join('static', 'data', 'team_management.json')

        if os.path.exists(team_data_path):
            try:
                with open(team_data_path, 'r', encoding='utf-8') as f:
                    team_data = json.load(f)
                    return jsonify({"success": True, "data": team_data})
            except Exception as e:
                print(f"读取团队管理数据文件失败: {e}")
                # 如果读取失败，继续使用默认数据
        # 确保数据目录存在
        data_dir = os.path.join('static', 'data')
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        # 保存默认数据到JSON文件中，以便将来修改
        try:
            with open(team_data_path, 'w', encoding='utf-8') as f:
                json.dump(team_data, f, ensure_ascii=False, indent=4)
                print(f"团队管理数据已保存到: {team_data_path}")
        except Exception as e:
            print(f"保存团队管理数据失败: {e}")

        return jsonify({"success": True, "data": team_data})
    except Exception as e:
        print(f"获取团队管理数据失败: {e}")
        return jsonify({"success": False, "message": f"系统错误，请稍后重试: {str(e)}"}), 500

# 读取SPU日期数据文件
def read_spu_json_data(date_str=None, period='daily', limit=10):
    """
    从static/spu_day_date目录读取SPU JSON数据

    参数:
        date_str: 日期字符串，格式YYYY-MM-DD，如果为None则读取最近日期
        period: 'daily'为单日数据, 'weekly'为7天数据, 'monthly'为按月数据, 'yearly'为全年数据
        limit: 返回的SPU数量限制
    """
    spu_data_dir = 'static/spu_day_date'

    # 获取所有可用的日期文件
    all_json_files = glob.glob(f"{spu_data_dir}/*.json")

    if not all_json_files:
        print("未找到SPU数据文件")
        return None, []

    # 从文件名提取日期并排序
    date_files = []
    for file_path in all_json_files:
        file_name = os.path.basename(file_path)
        file_date = file_name.split('.')[0]  # 获取不带扩展名的文件名，即日期
        try:
            file_datetime = datetime.strptime(file_date, '%Y-%m-%d')
            date_files.append((file_datetime, file_path))
        except ValueError:
            continue

    # 按日期排序文件（降序）
    date_files.sort(reverse=True)

    if not date_files:
        return None, []

    # 确定要读取的文件
    target_files = []

    if date_str:
        # 如果提供了特定日期，查找该日期的文件
        target_date = datetime.strptime(date_str, '%Y-%m-%d')
        found = False
        for file_date, file_path in date_files:
            if file_date.date() == target_date.date():
                target_files.append((file_date, file_path))
                found = True
                break

        if not found:
            print(f"未找到日期为 {date_str} 的数据文件")
            return None, []

    # 根据不同时间段选择文件
    if period == 'weekly':
        # 获取最近7天的文件
        latest_date = date_files[0][0] if not date_str else datetime.strptime(date_str, '%Y-%m-%d')
        for file_date, file_path in date_files:
            date_diff = (latest_date - file_date).days
            if 0 <= date_diff < 7:
                target_files.append((file_date, file_path))
        target_files.sort(key=lambda x: x[0])  # 按日期升序排序

    elif period == 'monthly':
        # 获取同一月的文件
        target_month = date_files[0][0].month if not date_str else datetime.strptime(date_str, '%Y-%m-%d').month
        target_year = date_files[0][0].year if not date_str else datetime.strptime(date_str, '%Y-%m-%d').year
        for file_date, file_path in date_files:
            if file_date.month == target_month and file_date.year == target_year:
                target_files.append((file_date, file_path))
        target_files.sort(key=lambda x: x[0])  # 按日期升序排序

    elif period == 'yearly' and date_str:
        # 获取同一年的文件
        target_year = datetime.strptime(date_str, '%Y-%m-%d').year
        for file_date, file_path in date_files:
            if file_date.year == target_year:
                target_files.append((file_date, file_path))
        target_files.sort(key=lambda x: x[0])  # 按日期升序排序

    elif not target_files:
        # 默认使用最新的一天
        target_files = [date_files[0]]

    # 读取SPU数据
    all_dates = []
    all_spus = {}

    for file_date, file_path in target_files:
        date_str = file_date.strftime('%Y-%m-%d')
        all_dates.append(date_str)

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)

                # 检查数据结构
                if 'data' not in json_data:
                    continue

                # 处理SPU数据
                for spu in json_data['data']:
                    spu_id = spu.get('spu_id', '')
                    if not spu_id:
                        continue

                    # 初始化SPU记录
                    if spu_id not in all_spus:
                        all_spus[spu_id] = {
                            'id': spu_id,
                            'name': spu.get('spu_name', f'SPU {spu_id}'),
                            'pic': spu.get('spu_pic', ''),
                            'dates': {},
                            'sales': [],
                            'units': [],
                            'profit': [],
                            'totalSales': 0,
                            'totalUnits': 0,
                            'totalProfit': 0,
                            'growth': 0
                        }

                    # 添加日期数据
                    all_spus[spu_id]['dates'][date_str] = {
                        'sales': spu.get('order_item_amt', 0),
                        'units': spu.get('order_item_qty', 0),
                        'profit': spu.get('net_order_item_amt', 0)
                    }
        except Exception as e:
            print(f"读取SPU数据文件 {file_path} 出错: {e}")

    # 将收集的数据转换为列表格式
    spu_list = []

    for spu_id, spu_data in all_spus.items():
        # 计算总量和填充数组
        total_sales = 0
        total_units = 0
        total_profit = 0
        sales_array = []
        units_array = []
        profit_array = []

        for date in all_dates:
            if date in spu_data['dates']:
                date_data = spu_data['dates'][date]
                sales = date_data['sales']
                units = date_data['units']
                profit = date_data['profit']

                sales_array.append(sales)
                units_array.append(units)
                profit_array.append(profit)

                total_sales += sales
                total_units += units
                total_profit += profit
            else:
                # 如果某天没有数据，填充0
                sales_array.append(0)
                units_array.append(0)
                profit_array.append(0)

        # 计算环比增长率（如果有足够的数据）
        growth = 0
        if len(sales_array) >= 2 and sales_array[-2] > 0:
            growth = round(((sales_array[-1] - sales_array[-2]) / sales_array[-2]) * 100, 2)

        # 更新SPU数据
        spu_data['sales'] = sales_array
        spu_data['units'] = units_array
        spu_data['profit'] = profit_array
        spu_data['totalSales'] = total_sales
        spu_data['totalUnits'] = total_units
        spu_data['totalProfit'] = total_profit
        spu_data['growth'] = growth

        spu_list.append(spu_data)

    # 排序并限制数量
    spu_list.sort(key=lambda x: x['totalSales'], reverse=True)
    spu_list = spu_list[:limit]

    return all_dates, spu_list

# SPU全年销售趋势数据接口
@app.route('/api/spu-trend', methods=['GET'])
def get_spu_trend():
    try:
        # 获取查询参数
        year = request.args.get('year', '2023')
        search = request.args.get('search', '')
        period = request.args.get('period', 'yearly')  # yearly, weekly
        granularity = request.args.get('granularity', 'monthly')  # daily, monthly
        limit = int(request.args.get('limit', '10'))

        # 处理2025年数据特殊情况
        current_year = datetime.now().year
        is_current_year = int(year) == current_year

        # 根据year构造日期字符串
        if period == 'yearly':
            if is_current_year:
                # 如果是当前年份，使用昨天的日期
                date_str = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
            else:
                date_str = f"{year}-12-31"  # 使用年末作为锚定日期
        else:
            date_str = None  # 使用最新数据

        # 定义日期格式转换函数
        def format_date(date_str, format_type='monthly'):
            if format_type == 'monthly':
                # 年月格式
                dt = datetime.strptime(date_str, '%Y-%m-%d')
                return dt.strftime('%m月')
            else:
                # 月日格式
                dt = datetime.strptime(date_str, '%Y-%m-%d')
                return dt.strftime('%m-%d')

        # 读取真实SPU数据
        api_period = 'weekly' if period == 'weekly' else 'yearly'
        dates, spu_list = read_spu_json_data(date_str, api_period, limit)

        if not dates or not spu_list:
            # 如果没有读取到数据，使用模拟数据（现有的逻辑）
            print("没有找到SPU数据文件，使用模拟数据")

        else:
            # 使用真实数据

            # 对于2025年的特殊处理
            if int(year) == 2025:
                current_date = datetime.now()

                # 如果当前年份是2025，需要过滤数据，只显示到昨天
                if current_date.year == 2025:
                    yesterday = (current_date - timedelta(days=1)).strftime('%Y-%m-%d')

                    # 过滤日期数据，只保留到昨天的
                    filtered_dates = []
                    for date in dates:
                        date_obj = datetime.strptime(date, '%Y-%m-%d')
                        if date_obj.strftime('%Y-%m-%d') <= yesterday:
                            filtered_dates.append(date)

                    # 如果没有有效数据，返回空结果
                    if not filtered_dates:
                        return jsonify({
                            'success': True,
                            'data': {
                                'year': year,
                                'period': period,
                                'granularity': granularity,
                                'dates': [],
                                'spuList': []
                            }
                        })

                    # 更新日期列表
                    dates = filtered_dates

                    # 为每个SPU过滤数据
                    for spu in spu_list:
                        cut_length = len(dates)
                        spu['sales'] = spu['sales'][:cut_length]
                        spu['units'] = spu['units'][:cut_length]
                        spu['profit'] = spu['profit'][:cut_length]

            # 根据granularity调整日期格式
            if period == 'yearly' and granularity == 'monthly':
                # 转换日期格式为月份
                formatted_dates = [format_date(date, 'monthly') for date in dates]

                # 汇总月度数据
                months_map = {}
                for date in formatted_dates:
                    month_idx = int(datetime.strptime(date, '%m月').strftime('%m')) - 1
                    if month_idx not in months_map:
                        months_map[month_idx] = []

                for spu in spu_list:
                    monthly_sales = [0] * 12
                    monthly_units = [0] * 12
                    monthly_profit = [0] * 12

                    for i, date in enumerate(formatted_dates):
                        if i < len(spu['sales']):  # 确保索引有效
                            month_idx = int(datetime.strptime(date, '%m月').strftime('%m')) - 1
                            if 0 <= month_idx < 12:  # 确保月份索引有效
                                monthly_sales[month_idx] += spu['sales'][i]
                                monthly_units[month_idx] += spu['units'][i]
                                monthly_profit[month_idx] += spu['profit'][i]
                                # 记录这个月有数据
                                if i not in months_map[month_idx]:
                                    months_map[month_idx].append(i)

                    # 只保留有数据的月份
                    valid_months = sorted(months_map.keys())
                    spu['sales'] = [monthly_sales[idx] for idx in valid_months]
                    spu['units'] = [monthly_units[idx] for idx in valid_months]
                    spu['profit'] = [monthly_profit[idx] for idx in valid_months]

                # 使用有数据的月份作为日期列表
                dates = [(idx + 1) for idx in valid_months]
                dates = [f"{m}月" for m in dates]
            elif period == 'weekly' or (period == 'yearly' and granularity == 'daily'):
                # 对于每日数据，转换为MM-DD格式
                dates = [format_date(date, 'daily') for date in dates]

            # 设置是否为日数据的标志
            for spu in spu_list:
                spu['daily'] = period == 'weekly' or granularity == 'daily'

        # 过滤搜索
        if search and spu_list:
            filtered_spus = []
            for spu in spu_list:
                if search.lower() in spu['id'].lower() or search.lower() in spu['name'].lower():
                    filtered_spus.append(spu)
            spu_list = filtered_spus if filtered_spus else spu_list

        # 对SPU数据按总销售额排序
        spu_list.sort(key=lambda x: x['totalSales'], reverse=True)

        # 返回响应
        return jsonify({
            'success': True,
            'data': {
                'year': year,
                'period': period,
                'granularity': granularity,
                'dates': dates,
                'spuList': spu_list
            }
        })
    except Exception as e:
        print(f"获取SPU销售趋势数据失败: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'message': str(e)}), 500

# 获取昨日日期字符串 (YYYYMMDD格式)
def get_yesterday_date():
    yesterday = datetime.now() - timedelta(days=1)
    return yesterday.strftime("%Y%m%d")

# 获取前天日期字符串 (YYYYMMDD格式)
def get_day_before_yesterday_date():
    day_before_yesterday = datetime.now() - timedelta(days=2)
    return day_before_yesterday.strftime("%Y%m%d")

# 获取店铺趋势数据接口
@app.route('/api/shop-trend', methods=['GET'])
def get_shop_trend():
    try:
        # 获取查询参数
        shop_name = request.args.get('shop')
        days = int(request.args.get('days', 7))  # 默认获取7天的数据

        if not shop_name:
            return jsonify({"success": False, "message": "请指定店铺名称"}), 400

        # 限制天数范围
        if days < 1:
            days = 7
        elif days > 90:
            days = 90

        # 获取日期范围
        today = datetime.now()
        date_list = []
        for i in range(days):
            date = today - timedelta(days=i+1)  # 从昨天开始往前推
            date_str = date.strftime('%Y%m%d')
            date_list.append(date_str)

        # 存储趋势数据的列表
        trend_data = []

        # 遍历日期获取数据
        for date_str in date_list:
            # 读取销售数据
            sales_data = {}
            sales_file_path = os.path.join('static', 'temp', f"{date_str}销售数据.csv")
            if os.path.exists(sales_file_path):
                encodings = ['gbk', 'utf-8', 'gb18030', 'gb2312', 'cp936']
                for encoding in encodings:
                    try:
                        with open(sales_file_path, 'r', encoding=encoding) as f:
                            reader = csv.reader(f)
                            next(reader)  # 跳过表头
                            for row in reader:
                                if row and len(row) >= 4 and row[0].strip() == shop_name:
                                    sales_amount = float(row[2]) if row[2] and row[2] != '' else 0.0
                                    orders_count = int(row[3]) if row[3] and row[3] != '' else 0
                                    buyers_count = int(row[4]) if row[4] and len(row) > 4 and row[4] != '' else 0
                                    conversion_rate = float(row[6]) * 100 if row[6] and len(row) > 6 and row[6] != '' else 0.0
                                    refund_amount = float(row[9]) if row[9] and len(row) > 9 and row[9] != '' else 0.0
                                    refund_orders = int(row[10]) if row[10] and len(row) > 10 and row[10] != '' else 0

                                    sales_data = {
                                        'salesAmount': sales_amount,
                                        'ordersCount': orders_count,
                                        'buyersCount': buyers_count,
                                        'conversionRate': conversion_rate,
                                        'refundAmount': refund_amount,
                                        'refundOrders': refund_orders
                                    }
                                    break
                        if sales_data:
                            break
                    except UnicodeDecodeError:
                        continue
                    except Exception as e:
                        print(f"使用 {encoding} 编码读取销售数据CSV文件时发生错误: {str(e)}")
                        continue

            # 读取推广金额数据
            promo_amount = 0.0
            promo_data = read_promotion_data(date_str)
            if shop_name in promo_data:
                promo_amount = promo_data[shop_name]
            else:
                # 如果没有推广数据，基于销售额设置一个默认推广费
                sales_amount = sales_data.get('salesAmount', 0.0)
                promo_amount = 0

            # 如果有销售数据，添加到趋势数据中
            if sales_data:
                # 使用当前日期的数据
                trend_data.append({
                    'date': date_str,
                    'salesAmount': sales_data.get('salesAmount', 0.0),
                    'ordersCount': sales_data.get('ordersCount', 0),
                    'buyersCount': sales_data.get('buyersCount', 0),
                    'conversionRate': sales_data.get('conversionRate', 0.0),
                    'refundAmount': sales_data.get('refundAmount', 0.0),
                    'refundOrders': sales_data.get('refundOrders', 0),
                    'promoAmount': promo_amount
                })

        # 按日期排序，确保数据是按照时间先后排序的
        trend_data.sort(key=lambda x: x['date'])

        return jsonify({"success": True, "data": trend_data})
    except Exception as e:
        print(f"获取店铺趋势数据失败: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"success": False, "message": f"系统错误，请稍后重试: {str(e)}"}), 500

# 获取留言板数据的API
@app.route('/api/messages', methods=['GET'])
def get_messages():
    """获取留言板数据"""
    try:
        # 读取留言数据
        messages_path = os.path.join('static', 'data', 'messages.json')
        if not os.path.exists(messages_path):
            return jsonify({'success': False, 'error': '留言数据文件不存在'}), 404

        with open(messages_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 获取过滤参数
        status_filter = request.args.get('status', 'all')
        type_filter = request.args.get('type', 'all')

        # 应用过滤
        filtered_messages = data['messages']
        if status_filter != 'all':
            filtered_messages = [m for m in filtered_messages if m['status'] == status_filter]
        if type_filter != 'all':
            filtered_messages = [m for m in filtered_messages if m['type'] == type_filter]

        # 分页参数
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))

        # 计算分页
        total = len(filtered_messages)
        total_pages = (total + per_page - 1) // per_page
        start = (page - 1) * per_page
        end = min(start + per_page, total)

        # 返回分页结果
        return jsonify({
            'success': True,
            'messages': filtered_messages[start:end],
            'pagination': {
                'total': total,
                'per_page': per_page,
                'current_page': page,
                'total_pages': total_pages
            }
        })

    except Exception as e:
        print(f"获取留言数据失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# 添加新留言的API
@app.route('/api/messages', methods=['POST'])
def add_message():
    """添加新留言"""
    try:
        # 获取请求数据
        request_data = request.get_json()
        if not request_data:
            return jsonify({'success': False, 'error': '无效的请求数据'}), 400

        # 验证请求数据
        required_fields = ['type', 'title', 'details', 'priority']
        for field in required_fields:
            if field not in request_data:
                return jsonify({'success': False, 'error': f'缺少必填字段: {field}'}), 400

        # 读取现有留言数据
        messages_path = os.path.join('static', 'data', 'messages.json')
        if not os.path.exists(messages_path):
            # 如果文件不存在，创建它
            data_dir = os.path.dirname(messages_path)
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
            data = {"messages": [], "next_id": 1}
        else:
            try:
                with open(messages_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 确保基本结构存在
                    if "messages" not in data or not isinstance(data["messages"], list):
                         data = {"messages": [], "next_id": 1}
                    if "next_id" not in data:
                         data["next_id"] = max([m.get("id", 0) for m in data["messages"]], default=0) + 1
                         
            except (json.JSONDecodeError, FileNotFoundError):
                 data = {"messages": [], "next_id": 1}


        # 创建新留言
        user = request.cookies.get('user', '未知用户') # 从 cookie 获取用户名
        today = datetime.now().strftime('%Y-%m-%d')

        new_message = {
            "id": data.get('next_id', 1),
            "type": request_data['type'],
            "title": request_data['title'],
            "details": request_data['details'],
            "priority": request_data['priority'],
            "status": "pending", # 初始状态为待处理
            "date": today,
            "user": user,
            "admin_reply": None # 管理员回复初始为空
        }

        # 添加到数据中
        data['messages'].insert(0, new_message)  # 添加到列表顶部
        data['next_id'] = data.get('next_id', 1) + 1

        # 保存到文件
        with open(messages_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"用户 '{user}' 添加了新留言: {new_message['title']}")
        return jsonify({'success': True, 'message': new_message}), 201 # 返回 201 Created

    except Exception as e:
        print(f"添加留言失败: {e}")
        import traceback
        traceback.print_exc() # 打印详细错误信息
        return jsonify({'success': False, 'error': '服务器内部错误'}), 500

@app.route('/api/messages/<int:message_id>', methods=['PATCH'])
@admin_required # 只有管理员能更新
def update_message(message_id):
    """更新留言状态或回复 (管理员)"""
    try:
        # 获取请求数据
        request_data = request.get_json()
        if not request_data:
            return jsonify({'success': False, 'error': '无效的请求数据'}), 400
        
        # 检查是否有有效的更新字段
        valid_update_fields = ['status', 'admin_reply']
        if not any(field in request_data for field in valid_update_fields):
             return jsonify({'success': False, 'error': '没有提供有效的更新字段 (status 或 admin_reply)'}), 400

        # 读取现有留言数据
        messages_path = os.path.join('static', 'data', 'messages.json')
        if not os.path.exists(messages_path):
            return jsonify({'success': False, 'error': '留言数据文件不存在'}), 404

        with open(messages_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 查找指定留言
        message_to_update = None
        for message in data['messages']:
            if message.get('id') == message_id:
                message_to_update = message
                break

        if not message_to_update:
            return jsonify({'success': False, 'error': f'未找到ID为{message_id}的留言'}), 404

        # 更新状态
        updated_fields = []
        if 'status' in request_data:
            # 可以添加对 status 值的验证，例如只能是 'pending', 'processing', 'resolved' 等
            valid_statuses = ['pending', 'processing', 'resolved', 'closed']
            if request_data['status'] not in valid_statuses:
                 return jsonify({'success': False, 'error': f'无效的状态值，可选值: {", ".join(valid_statuses)}'}), 400
            message_to_update['status'] = request_data['status']
            updated_fields.append('status')
            
        # 更新管理员回复
        if 'admin_reply' in request_data:
            message_to_update['admin_reply'] = request_data['admin_reply']
            updated_fields.append('admin_reply')

        # 保存到文件
        with open(messages_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        admin_user = request.cookies.get('user', '管理员')
        print(f"管理员 '{admin_user}' 更新了留言 ID {message_id} 的以下字段: {', '.join(updated_fields)}")
        return jsonify({'success': True, 'message': '留言更新成功', 'updated_message': message_to_update})

    except Exception as e:
        print(f"更新留言失败: {e}")
        return jsonify({'success': False, 'error': '服务器内部错误'}), 500

@app.route('/api/messages/<int:message_id>', methods=['DELETE'])
@admin_required # 只有管理员能删除
def delete_message(message_id):
    """删除留言 (管理员)"""
    try:
        # 构建文件路径
        messages_path = Path('static') / 'data' / 'messages.json'

        # 检查文件是否存在
        if not messages_path.exists():
            return jsonify({'success': False, 'error': '留言数据文件不存在'}), 404

        # 读取现有留言数据
        try:
            with open(messages_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except json.JSONDecodeError:
            return jsonify({'success': False, 'error': '留言数据文件格式错误'}), 500

        # 检查数据结构
        if 'messages' not in data or not isinstance(data['messages'], list):
            return jsonify({'success': False, 'error': '留言数据格式不正确'}), 500

        # 过滤掉指定ID的留言
        original_count = len(data['messages'])
        data['messages'] = [msg for msg in data['messages'] if msg.get('id') != message_id]

        # 验证是否真的找到了要删除的留言
        if len(data['messages']) == original_count:
            return jsonify({'success': False, 'error': f'未找到ID为{message_id}的留言'}), 404

        # 保存到文件
        try:
            with open(messages_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except IOError as e:
            return jsonify({'success': False, 'error': f'文件保存失败: {str(e)}'}), 500

        admin_user = request.cookies.get('user', '管理员')
        print(f"管理员 '{admin_user}' 删除了留言 ID: {message_id}")
        return jsonify({
            'success': True,
            'message': '留言删除成功',
            'deleted_id': message_id,
            'remaining_count': len(data['messages'])
        })

    except Exception as e:
        print(f"删除留言失败: {e}")
        return jsonify({'success': False, 'error': '服务器内部错误'}), 500

# 添加静态文件服务
@app.route('/', defaults={'path': 'shop.html'})
@app.route('/<path:path>')
def serve_static(path):
    return app.send_static_file(path)

# FRP配置说明
def setup_frp_info():
    """
    打印FRP使用说明

    FRP是一个快速反向代理，用于将NAT或防火墙后面的本地服务器暴露到互联网上
    """
    print("\n" + "="*80)
    print("FRP内网穿透配置说明".center(80))
    print("="*80)
    print("当前应用运行在: http://localhost:5000")
    print("="*80 + "\n")

    return None

# 获取昨日运营SKU数据API
@app.route('/api/daily-operation-data', methods=['GET'])
def get_daily_operation_data():
    try:
        # 获取请求中的date参数
        date_param = request.args.get('date', '')
        start_date_param = request.args.get('startDate', '')
        end_date_param = request.args.get('endDate', '')
        
        # 默认使用昨天的日期
        target_dates = [get_yesterday_date()]
        file_date_label = '昨日'
        
        # 如果有日期范围参数
        if start_date_param and end_date_param:
            file_date_label = f"{start_date_param} 至 {end_date_param}"
            
            # 转换日期格式并生成日期范围
            start = datetime.strptime(start_date_param, '%Y-%m-%d')
            end = datetime.strptime(end_date_param, '%Y-%m-%d')
            date_range = []
            current = start
            while current <= end:
                date_range.append(current.strftime('%Y%m%d'))
                current += timedelta(days=1)
            target_dates = date_range
        # 如果有单个日期参数
        elif date_param:
            # 检查是否为日期范围格式（兼容旧逻辑）
            if ',' in date_param:  
                start_date, end_date = date_param.split(',')
                file_date_label = f"{start_date} 至 {end_date}"
                
                # 转换日期格式并生成日期范围
                start = datetime.strptime(start_date, '%Y-%m-%d')
                end = datetime.strptime(end_date, '%Y-%m-%d')
                date_range = []
                current = start
                while current <= end:
                    date_range.append(current.strftime('%Y%m%d'))
                    current += timedelta(days=1)
                target_dates = date_range
            else:  # 单日格式
                # 转换日期格式 (如 2023-05-01 -> 20230501)
                formatted_date = date_param.replace('-', '')
                target_dates = [formatted_date]
                file_date_label = date_param

        # 合并数据存储
        merged_sku_data = {}
        found_files = 0
        
        # 遍历每个日期获取数据
        for target_date in target_dates:
            # 构建JSON文件路径
            sku_file = os.path.join('static', 'SKU', f'{target_date}SKU列表_商品SKU映射_上架商品ID数量.json')
            
            # 如果指定日期的文件不存在，尝试基于日期的其他可能格式
            if not os.path.exists(sku_file):
                # 尝试日期前加"截止"的格式
                alt_sku_file = os.path.join('static', 'SKU', f'截止{target_date}SKU列表_商品SKU映射_上架商品ID数量.json')
                if os.path.exists(alt_sku_file):
                    sku_file = alt_sku_file
                else:
                    # 尝试查找含有当天日期的文件
                    possible_files = glob.glob(os.path.join('static', 'SKU', f'*{target_date}*SKU列表_商品SKU映射_上架商品ID数量.json'))
                    if possible_files:
                        sku_file = possible_files[0]
                    else:
                        # 日志记录未找到特定日期的文件
                        print(f'未找到日期 {target_date} 的SKU数据文件')
                        continue
            
            # 读取JSON文件
            try:
                with open(sku_file, 'r', encoding='utf-8') as f:
                    daily_sku_data = json.load(f)
                    found_files += 1  # 计数找到的文件
                    
                    # 合并数据
                    for sku, shops in daily_sku_data.items():
                        if sku not in merged_sku_data:
                            merged_sku_data[sku] = {}
                            
                        for shop, shop_data in shops.items():
                            if shop not in merged_sku_data[sku]:
                                merged_sku_data[sku][shop] = {
                                    "商品数量": 0,
                                    "商品ID列表": []
                                }
                            
                            # 累加商品数量
                            merged_sku_data[sku][shop]["商品数量"] += shop_data.get("商品数量", 0)
                            
                            # 合并商品ID列表（去重）
                            new_ids = shop_data.get("商品ID列表", [])
                            existing_ids = set(merged_sku_data[sku][shop]["商品ID列表"])
                            merged_sku_data[sku][shop]["商品ID列表"] = list(existing_ids.union(new_ids))
            except Exception as e:
                print(f"读取文件 {sku_file} 失败: {e}")
                continue

        # 如果在日期范围内未找到任何文件，尝试使用最新的可用文件
        if found_files == 0:
            # 搜索所有匹配的文件
            sku_files = glob.glob(os.path.join('static', 'SKU', '*SKU列表_商品SKU映射_上架商品ID数量.json'))
            if sku_files:
                # 按文件名排序以找到最新的日期
                sku_files.sort(reverse=True)
                sku_file = sku_files[0]
                # 从文件名中提取日期
                file_date = os.path.basename(sku_file).split('SKU列表')[0]
                if file_date == '截止28':
                    file_date_label = '示例数据'
                else:
                    file_date_label = f'{file_date} (最新可用数据)'
                
                try:
                    with open(sku_file, 'r', encoding='utf-8') as f:
                        merged_sku_data = json.load(f)
                except Exception as e:
                    print(f"读取最新文件 {sku_file} 失败: {e}")
            else:
                # 如果没有找到任何匹配的文件，使用示例文件
                sku_file = os.path.join('static', 'SKU', '截止28SKU列表_商品SKU映射_上架商品ID数量.json')
                file_date_label = '示例数据'
                
                if os.path.exists(sku_file):
                    try:
                        with open(sku_file, 'r', encoding='utf-8') as f:
                            merged_sku_data = json.load(f)
                    except Exception as e:
                        print(f"读取示例文件失败: {e}")

        if not merged_sku_data:
            return jsonify({'success': False, 'message': '未找到任何有效的SKU数据'}), 404

        # 添加统计信息到响应
        response_data = {
            'success': True,
            'data': merged_sku_data,
            'date': file_date_label,
            'file_count': found_files
        }

        return jsonify(response_data)
    except Exception as e:
        print(f"获取运营SKU数据失败: {e}")
        return jsonify({'success': False, 'message': f'系统错误，请稍后重试: {str(e)}'}), 500

@app.route('/user/<username>')
def user_profile(username):
    return render_template('userprofile.html', username=username)

# 商品管理页面
@app.route('/product')
def product_management():
    return render_template('product.html')

# 美工任务存储路径
ART_TASKS_FILE = 'data/art_tasks.json'
UPLOAD_FOLDER = 'static/uploads/art_tasks'
ART_TASKS_OVERVIEW_FILE = 'data/art_tasks_overview.json'  # 新增：任务概览存储

# 确保目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(os.path.dirname(ART_TASKS_FILE), exist_ok=True)

# 读取美工任务数据
def read_art_tasks():
    try:
        if os.path.exists(ART_TASKS_FILE):
            with open(ART_TASKS_FILE, 'r', encoding='utf-8') as file:
                return json.load(file)
        return []
    except Exception as e:
        print(f"读取美工任务数据失败: {e}")
        return []

# 保存美工任务数据
def write_art_tasks(tasks):
    try:
        with open(ART_TASKS_FILE, 'w', encoding='utf-8') as file:
            json.dump(tasks, file, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存美工任务数据失败: {e}")
        return False

# 读取任务概览数据
def read_art_tasks_overview():
    try:
        if os.path.exists(ART_TASKS_OVERVIEW_FILE):
            with open(ART_TASKS_OVERVIEW_FILE, 'r', encoding='utf-8') as file:
                return json.load(file)
        # 默认结构
        return {
            "last_reset": datetime.now().strftime('%Y-%m-%d'),
            "monthly_stats": {},
            "yearly_stats": {}
        }
    except Exception as e:
        print(f"读取美工任务概览数据失败: {e}")
        return {
            "last_reset": datetime.now().strftime('%Y-%m-%d'),
            "monthly_stats": {},
            "yearly_stats": {}
        }

# 保存任务概览数据
def write_art_tasks_overview(overview_data):
    try:
        with open(ART_TASKS_OVERVIEW_FILE, 'w', encoding='utf-8') as file:
            json.dump(overview_data, file, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存美工任务概览数据失败: {e}")
        return False

# 更新任务概览统计
def update_art_tasks_overview():
    tasks = read_art_tasks()
    overview = read_art_tasks_overview()
    current_month = datetime.now().strftime('%Y-%m')
    current_year = datetime.now().strftime('%Y')
    
    # 初始化当月数据结构
    if current_month not in overview["monthly_stats"]:
        overview["monthly_stats"][current_month] = {
            "total": 0,
            "completed": 0,
            "by_category": {},
            "by_designer": {},
            "by_status": {}
        }
    
    # 初始化当年数据结构
    if current_year not in overview["yearly_stats"]:
        overview["yearly_stats"][current_year] = {
            "total": 0,
            "completed": 0,
            "by_month": {},
            "by_category": {},
            "by_designer": {}
        }
    
    # 重置当月统计
    overview["monthly_stats"][current_month] = {
        "total": 0,
        "completed": 0,
        "by_category": {},
        "by_designer": {},
        "by_status": {}
    }
    
    # 重置当年统计
    overview["yearly_stats"][current_year] = {
        "total": 0,
        "completed": 0,
        "by_month": {},
        "by_category": {},
        "by_designer": {}
    }
    
    # 统计任务数据
    for task in tasks:
        task_date = task.get('createdAt', '')
        if not task_date:
            continue
        
        task_month = task_date[:7]  # YYYY-MM
        task_year = task_date[:4]   # YYYY
        
        # 统计当月数据
        if task_month == current_month:
            # 总数
            overview["monthly_stats"][current_month]["total"] += 1
            
            # 完成数
            if task.get('statusId') == 4:  # 已完成状态
                overview["monthly_stats"][current_month]["completed"] += 1
            
            # 按类别统计
            category_id = str(task.get('categoryId', ''))
            if category_id:
                if category_id not in overview["monthly_stats"][current_month]["by_category"]:
                    overview["monthly_stats"][current_month]["by_category"][category_id] = 0
                overview["monthly_stats"][current_month]["by_category"][category_id] += 1
            
            # 按设计师统计
            designer = task.get('assignedTo', '')
            if designer:
                if designer not in overview["monthly_stats"][current_month]["by_designer"]:
                    overview["monthly_stats"][current_month]["by_designer"][designer] = 0
                overview["monthly_stats"][current_month]["by_designer"][designer] += 1
            
            # 按状态统计
            status_id = str(task.get('statusId', ''))
            if status_id:
                if status_id not in overview["monthly_stats"][current_month]["by_status"]:
                    overview["monthly_stats"][current_month]["by_status"][status_id] = 0
                overview["monthly_stats"][current_month]["by_status"][status_id] += 1
        
        # 更新年度统计
        if task_year == current_year:
            # 总数
            overview["yearly_stats"][current_year]["total"] += 1
            
            # 完成数
            if task.get('statusId') == 4:  # 已完成状态
                overview["yearly_stats"][current_year]["completed"] += 1
            
            # 按月统计
            if task_month not in overview["yearly_stats"][current_year]["by_month"]:
                overview["yearly_stats"][current_year]["by_month"][task_month] = 0
            overview["yearly_stats"][current_year]["by_month"][task_month] += 1
            
            # 按类别统计
            category_id = str(task.get('categoryId', ''))
            if category_id:
                if category_id not in overview["yearly_stats"][current_year]["by_category"]:
                    overview["yearly_stats"][current_year]["by_category"][category_id] = 0
                overview["yearly_stats"][current_year]["by_category"][category_id] += 1
            
            # 按设计师统计
            designer = task.get('assignedTo', '')
            if designer:
                if designer not in overview["yearly_stats"][current_year]["by_designer"]:
                    overview["yearly_stats"][current_year]["by_designer"][designer] = 0
                overview["yearly_stats"][current_year]["by_designer"][designer] += 1
    
    # 更新最后重置时间
    overview["last_reset"] = datetime.now().strftime('%Y-%m-%d')
    
    # 保存概览数据
    write_art_tasks_overview(overview)
    return overview

# 检查是否需要月度重置
def check_and_reset_monthly_overview():
    overview = read_art_tasks_overview()
    last_reset = overview.get("last_reset", "")
    
    if not last_reset:
        return update_art_tasks_overview()
    
    last_reset_date = datetime.strptime(last_reset, '%Y-%m-%d')
    current_date = datetime.now()
    
    # 检查是否跨月
    if last_reset_date.month != current_date.month or last_reset_date.year != current_date.year:
        return update_art_tasks_overview()
    
    return overview

# 获取任务概览
@app.route('/api/art-tasks/overview', methods=['GET'])
def get_art_tasks_overview():
    # 检查是否需要重置
    overview = check_and_reset_monthly_overview()
    return jsonify(overview)

# 手动重置任务概览
@app.route('/api/art-tasks/overview/reset', methods=['POST'])
def reset_art_tasks_overview():
    overview = update_art_tasks_overview()
    return jsonify({"success": True, "message": "任务概览已重置", "data": overview})

# 获取所有美工任务
@app.route('/api/art-tasks', methods=['GET'])
def get_art_tasks():
    # 每次获取任务时检查是否需要重置概览
    check_and_reset_monthly_overview()
    
    tasks = read_art_tasks()
    
    # 过滤查询参数
    assignee = request.args.get('assignedTo')
    creator = request.args.get('createdBy')
    status = request.args.get('statusId')
    category = request.args.get('categoryId')
    
    if assignee:
        tasks = [task for task in tasks if task.get('assignedTo') == assignee]
    if creator:
        tasks = [task for task in tasks if task.get('createdBy') == creator]
    if status:
        tasks = [task for task in tasks if str(task.get('statusId')) == status]
    if category:
        tasks = [task for task in tasks if str(task.get('categoryId')) == category]
    
    return jsonify(tasks)

# 获取单个美工任务
@app.route('/api/art-tasks/<task_id>', methods=['GET'])
def get_art_task(task_id):
    tasks = read_art_tasks()
    task = next((task for task in tasks if task.get('id') == task_id), None)
    
    if task:
        return jsonify(task)
    else:
        return jsonify({"error": "任务不存在"}), 404

# 创建美工任务
@app.route('/api/art-tasks', methods=['POST'])
def create_art_task():
    tasks = read_art_tasks()
    
    try:
        task_data = request.json
        
        # 确保必填字段存在
        required_fields = ['title', 'description', 'categoryId', 'priorityId', 'createdBy', 'deadline']
        for field in required_fields:
            if field not in task_data:
                return jsonify({"error": f"缺少必要字段: {field}"}), 400
        
        # 为任务分配唯一ID
        task_data['id'] = task_data.get('id') or f"task_{int(datetime.now().timestamp())}"
        
        # 设置创建时间
        if 'createdAt' not in task_data:
            task_data['createdAt'] = datetime.now().strftime('%Y-%m-%d')
        
        # 初始化评论和附件列表
        task_data['comments'] = task_data.get('comments', [])
        task_data['attachments'] = task_data.get('attachments', [])
        task_data['references'] = task_data.get('references', [])
        
        # 添加到任务列表
        tasks.append(task_data)
        
        # 保存数据
        if write_art_tasks(tasks):
            # 创建任务附件目录
            task_upload_dir = os.path.join(UPLOAD_FOLDER, task_data['id'])
            os.makedirs(task_upload_dir, exist_ok=True)
            
            # 更新任务概览数据
            update_art_tasks_overview()
            
            return jsonify(task_data), 201
        else:
            return jsonify({"error": "保存任务失败"}), 500
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# 更新美工任务
@app.route('/api/art-tasks/<task_id>', methods=['PUT'])
def update_art_task(task_id):
    tasks = read_art_tasks()
    
    try:
        task_data = request.json
        task_index = next((i for i, task in enumerate(tasks) if task.get('id') == task_id), None)
        
        if task_index is None:
            return jsonify({"error": "任务不存在"}), 404
        
        # 更新任务数据
        updated_task = {**tasks[task_index], **task_data}
        updated_task['id'] = task_id  # 确保ID不变
        
        tasks[task_index] = updated_task
        
        # 保存数据
        if write_art_tasks(tasks):
            # 更新任务概览数据
            update_art_tasks_overview()
            
            return jsonify(updated_task)
        else:
            return jsonify({"error": "保存任务失败"}), 500
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# 更新任务状态
@app.route('/api/art-tasks/<task_id>/status', methods=['PATCH'])
def update_task_status(task_id):
    tasks = read_art_tasks()
    
    try:
        status_data = request.json
        
        if 'statusId' not in status_data:
            return jsonify({"error": "缺少状态ID"}), 400
            
        task_index = next((i for i, task in enumerate(tasks) if task.get('id') == task_id), None)
        
        if task_index is None:
            return jsonify({"error": "任务不存在"}), 404
            
        # 更新状态
        tasks[task_index]['statusId'] = int(status_data['statusId'])
        
        # 保存数据
        if write_art_tasks(tasks):
            # 更新任务概览数据
            update_art_tasks_overview()
            
            return jsonify(tasks[task_index])
        else:
            return jsonify({"error": "保存任务状态失败"}), 500
            
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# 删除任务
@app.route('/api/art-tasks/<task_id>', methods=['DELETE'])
def delete_art_task(task_id):
    tasks = read_art_tasks()
    
    task_index = next((i for i, task in enumerate(tasks) if task.get('id') == task_id), None)
    
    if task_index is None:
        return jsonify({"error": "任务不存在"}), 404
        
    # 删除任务
    deleted_task = tasks.pop(task_index)
    
    # 保存数据
    if write_art_tasks(tasks):
        # 更新任务概览数据
        update_art_tasks_overview()
        
        return jsonify({"success": True, "message": "任务已删除"})
    else:
        # 恢复删除的任务
        tasks.insert(task_index, deleted_task)
        return jsonify({"error": "删除任务失败"}), 500

# 添加评论
@app.route('/api/art-tasks/<task_id>/comments', methods=['POST'])
def add_task_comment(task_id):
    tasks = read_art_tasks()
    
    try:
        comment_data = request.json
        
        if not all(key in comment_data for key in ['user', 'text']):
            return jsonify({"error": "缺少必要字段"}), 400
            
        task_index = next((i for i, task in enumerate(tasks) if task.get('id') == task_id), None)
        
        if task_index is None:
            return jsonify({"error": "任务不存在"}), 404
            
        # 格式化时间
        now = datetime.now()
        time_str = now.strftime('%Y-%m-%d %H:%M')
        
        # 创建评论
        comment = {
            "user": comment_data['user'],
            "text": comment_data['text'],
            "time": comment_data.get('time', time_str)
        }
        
        # 添加评论
        if 'comments' not in tasks[task_index]:
            tasks[task_index]['comments'] = []
            
        tasks[task_index]['comments'].append(comment)
        
        # 保存数据
        if write_art_tasks(tasks):
            # 更新任务概览数据
            update_art_tasks_overview()
            
            return jsonify(comment)
        else:
            return jsonify({"error": "保存评论失败"}), 500
            
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# 上传附件
@app.route('/api/art-tasks/<task_id>/attachments', methods=['POST'])
def upload_task_attachments(task_id):
    tasks = read_art_tasks()
    
    task_index = next((i for i, task in enumerate(tasks) if task.get('id') == task_id), None)
    
    if task_index is None:
        return jsonify({"error": "任务不存在"}), 404
        
    # 确保任务有附件列表
    if 'attachments' not in tasks[task_index]:
        tasks[task_index]['attachments'] = []
        
    uploaded_files = []
    
    try:
        # 创建任务附件目录
        task_upload_dir = os.path.join(UPLOAD_FOLDER, task_id)
        os.makedirs(task_upload_dir, exist_ok=True)
        
        for file in request.files.getlist('files'):
            if file and file.filename:
                filename = secure_filename(file.filename)
                filepath = os.path.join(task_upload_dir, filename)
                
                # 保存文件
                file.save(filepath)
                
                # 记录附件
                if filename not in tasks[task_index]['attachments']:
                    tasks[task_index]['attachments'].append(filename)
                    
                uploaded_files.append(filename)
                
        # 保存数据
        if write_art_tasks(tasks):
            # 更新任务概览数据
            update_art_tasks_overview()
            
            return jsonify({
                "success": True,
                "files": uploaded_files,
                "attachments": tasks[task_index]['attachments']
            })
        else:
            return jsonify({"error": "保存附件信息失败"}), 500
            
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# 下载附件
@app.route('/api/art-tasks/<task_id>/attachments/<filename>', methods=['GET'])
def download_task_attachment(task_id, filename):
    tasks = read_art_tasks()
    
    task = next((task for task in tasks if task.get('id') == task_id), None)
    
    if task is None:
        return jsonify({"error": "任务不存在"}), 404
        
    if 'attachments' not in task or filename not in task['attachments']:
        return jsonify({"error": "附件不存在"}), 404
        
    # 构建文件路径
    file_path = os.path.join(UPLOAD_FOLDER, task_id, filename)
    
    if not os.path.exists(file_path):
        return jsonify({"error": "文件不存在"}), 404
        
    return send_file(file_path, as_attachment=True, download_name=filename)

# 获取美工用户列表
@app.route('/api/users/filtered', methods=['GET'])
def get_users_filtered():
    accounts = read_accounts()
    
    # 获取查询参数
    role = request.args.get('role')
    
    if role:
        # 过滤指定角色的用户
        filtered_users = [user for user in accounts.get('zhanghu', []) if user.get('role') == role]
    else:
        # 返回所有用户
        filtered_users = accounts.get('zhanghu', [])
    
    return jsonify(filtered_users)

def secure_filename(filename):
    """
    安全地处理文件名，同时保留原始文件名
    """
    # 获取原始文件名和扩展名
    base_name, ext = os.path.splitext(filename)
    # 替换非法字符为下划线
    safe_base = re.sub(r'[^\w\.-]', '_', base_name)
    # 返回安全的完整文件名
    return safe_base + ext

# 新的文件上传API端点
@app.route('/api/tasks/upload', methods=['POST'])
def upload_task_files():
    try:
        task_id = request.form.get('taskId')
        if not task_id:
            return jsonify({"success": False, "message": "缺少任务ID"}), 400
            
        tasks = read_art_tasks()
        task_index = next((i for i, task in enumerate(tasks) if task.get('id') == task_id), None)
        
        if task_index is None:
            return jsonify({"success": False, "message": "任务不存在"}), 404
            
        # 确保任务有附件列表
        if 'attachments' not in tasks[task_index]:
            tasks[task_index]['attachments'] = []
            
        uploaded_files = []
        
        # 创建任务附件目录
        task_upload_dir = os.path.join(UPLOAD_FOLDER, task_id)
        os.makedirs(task_upload_dir, exist_ok=True)
        
        for file in request.files.getlist('files'):
            if file and file.filename:
                # 保存完整的文件名
                original_filename = file.filename
                safe_filename = secure_filename(original_filename)
                filepath = os.path.join(task_upload_dir, safe_filename)
                
                # 保存文件
                file.save(filepath)
                
                # 记录附件
                if safe_filename not in tasks[task_index]['attachments']:
                    tasks[task_index]['attachments'].append(safe_filename)
                    
                uploaded_files.append(safe_filename)
                
        # 保存数据
        if write_art_tasks(tasks):
            # 更新任务概览数据
            update_art_tasks_overview()
            
            return jsonify({
                "success": True,
                "files": uploaded_files,
                "attachments": tasks[task_index]['attachments']
            })
        else:
            return jsonify({"success": False, "message": "保存附件信息失败"}), 500
            
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

# 新的文件下载API端点
@app.route('/api/tasks/download', methods=['GET'])
def download_task_file():
    try:
        task_id = request.args.get('taskId')
        filename = request.args.get('filename')
        
        if not task_id or not filename:
            return jsonify({"error": "缺少任务ID或文件名"}), 400
        
        tasks = read_art_tasks()
        task = next((task for task in tasks if task.get('id') == task_id), None)
        
        if task is None:
            return jsonify({"error": "任务不存在"}), 404
            
        if 'attachments' not in task or filename not in task['attachments']:
            return jsonify({"error": "附件不存在"}), 404
            
        # 构建文件路径
        file_path = os.path.join(UPLOAD_FOLDER, task_id, filename)
        
        if not os.path.exists(file_path):
            return jsonify({"error": "文件不存在"}), 404
            
        # 确保文件以正确的名称下载
        return send_file(file_path, as_attachment=True, download_name=filename)
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# 添加更新用户的API
@app.route('/api/users/<username>', methods=['PUT'])
@admin_required
def update_user(username):
    """更新用户信息 (管理员)"""
    try:
        data = request.json
        new_username = data.get('username', username)
        name = data.get('name', '')
        password = data.get('password')
        role = data.get('role')
        permissions = data.get('permissions', [])

        if not role:
            return jsonify({"success": False, "message": "缺少必要信息 (role)"}), 400

        accounts_data = read_accounts()
        accounts = accounts_data.get('zhanghu', [])

        # 查找要更新的用户
        user_index = None
        for i, acc in enumerate(accounts):
            if acc.get('user') == username:
                user_index = i
                break

        if user_index is None:
            return jsonify({"success": False, "message": f"用户 '{username}' 不存在"}), 404

        # 如果更改了用户名，确保新用户名不与其他用户冲突
        if new_username != username and any(acc.get('user') == new_username for i, acc in enumerate(accounts) if i != user_index):
            return jsonify({"success": False, "message": f"用户名 '{new_username}' 已存在"}), 409

        # 更新用户信息
        user = accounts[user_index]
        user['user'] = new_username
        user['name'] = name
        if password:
            user['password'] = password
        user['role'] = role
        user['permissions'] = permissions

        accounts_data['zhanghu'] = accounts

        if write_accounts(accounts_data):
            print(f"管理员更新了用户: {username} -> {new_username}")
            # 不返回密码
            user_copy = user.copy()
            if 'password' in user_copy:
                del user_copy['password']
            return jsonify({"success": True, "message": "用户信息更新成功", "user": user_copy})
        else:
            return jsonify({"success": False, "message": "保存用户信息失败"}), 500

    except Exception as e:
        print(f"更新用户失败: {e}")
        return jsonify({"success": False, "message": f"更新用户失败: {str(e)}"}), 500

@app.route('/api/violation-data', methods=['GET'])
def get_violation_data():
    """获取商品违规整改数据（支持分页）"""
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 20))
        search_keyword = request.args.get('keyword', '')
        shop_filter = request.args.get('shop', '')
        operator_filter = request.args.get('operator', '') # 添加运营人员筛选参数
        status_filter = request.args.get('status', '')  # 添加状态筛选参数
        punish_type_filter = request.args.get('punishType', '')  # 添加处罚类型筛选参数
        start_date = request.args.get('startDate', '')
        end_date = request.args.get('endDate', '')
        sort_field = request.args.get('sortField', 'start_time')
        sort_order = request.args.get('sortOrder', 'desc')
        
        # 自动识别最新的CSV文件
        weigui_dir = 'static/WEIGUIFAKUAN'
        csv_files = [f for f in os.listdir(weigui_dir) if f.endswith('.csv') and '违规申诉和整改' in f]
        
        if not csv_files:
                return jsonify({
                    "success": False,
                    "message": "违规数据文件不存在"
                }), 404
            
        # 按文件名排序（通常包含日期），选择最新的文件
        csv_files.sort(reverse=True)
        latest_csv = csv_files[0]
        csv_file_path = os.path.join(weigui_dir, latest_csv)
        print(f"使用最新的违规数据文件: {csv_file_path}")
        
        # 保存文件名到文件，用于后续检查是否更新
        last_file_path = os.path.join(os.path.dirname(VIOLATION_DB_PATH), 'last_csv_file.txt')
        needs_reload = True
        
        # 检查是否需要重新加载数据库
        if os.path.exists(last_file_path):
            try:
                with open(last_file_path, 'r') as f:
                    last_csv = f.read().strip()
                    # 如果上次加载的CSV文件和当前最新的不同，需要重新加载
                    if last_csv != latest_csv:
                        print(f"检测到新的CSV文件: {latest_csv}，将重新加载数据库")
                        # 安全删除旧数据库
                        safe_remove_database(VIOLATION_DB_PATH)
                    else:
                        needs_reload = False
            except Exception as e:
                print(f"读取上次CSV文件记录出错: {e}")
                # 出错时保险起见仍然重新加载
                safe_remove_database(VIOLATION_DB_PATH)
        
        # 如果数据库不存在，重新导入CSV
        if not os.path.exists(VIOLATION_DB_PATH) or needs_reload:
            # 确保数据库目录存在
            os.makedirs(os.path.dirname(VIOLATION_DB_PATH), exist_ok=True)
            
            # 导入CSV到数据库
            if not import_csv_to_db(csv_file_path):
                return jsonify({
                    "success": False,
                    "message": "导入数据到数据库失败"
                }), 500
        
            # 保存当前CSV文件名，用于后续检查
            try:
                with open(last_file_path, 'w') as f:
                    f.write(latest_csv)
                print(f"已记录当前CSV文件: {latest_csv}")
            except Exception as e:
                print(f"记录当前CSV文件出错: {e}")
        
        # 连接数据库
        try:
            conn = sqlite3.connect(VIOLATION_DB_PATH)
            conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
            cursor = conn.cursor()
        except sqlite3.Error as e:
            print(f"数据库连接错误: {e}")
            return jsonify({
                "success": False,
                "message": f"数据库连接错误: {str(e)}"
            }), 500
        
        try:
            # 构建查询条件
            conditions = []
            params = []
            
            if search_keyword:
                # 搜索商品ID、产品标题、店铺名称或处罚原因
                conditions.append("(goods_id LIKE ? OR product_title LIKE ? OR shop_name LIKE ? OR punish_reason LIKE ?)")
                params.extend([f"%{search_keyword}%", f"%{search_keyword}%", f"%{search_keyword}%", f"%{search_keyword}%"])
            
            if shop_filter and shop_filter != 'all':
                conditions.append("shop_name = ?")
                params.append(shop_filter)
                
            # 运营人员筛选条件
            if operator_filter and operator_filter != 'all':
                conditions.append("operator = ?")
                params.append(operator_filter)
            
            # 新增状态筛选条件 - 修复"处理中"状态筛选
            if status_filter and status_filter != 'all':
                # 处理前端传来的"处理中"状态，映射到数据库中的"处罚中"
                if status_filter == "处理中":
                    conditions.append("status_display = ?")
                    params.append("处罚中")
                else:
                    conditions.append("status_display = ?")
                    params.append(status_filter)
            
            # 新增处罚类型筛选条件
            if punish_type_filter and punish_type_filter != 'all':
                conditions.append("punish_measure LIKE ?")
                params.append(f"%{punish_type_filter}%")
            
            # 修复日期筛选功能
            if start_date:
                # 确保使用正确的日期格式比较
                conditions.append("(start_time_format >= ? OR start_time >= ?)")
                params.extend([start_date, start_date])
            
            if end_date:
                # 确保使用正确的日期格式比较，并包含当天
                end_date_with_time = end_date + " 23:59:59"
                conditions.append("(start_time_format <= ? OR start_time <= ?)")
                params.extend([end_date_with_time, end_date_with_time])
            
            # 组合查询条件
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            # 获取总记录数
            count_query = f"SELECT COUNT(*) as total FROM violations WHERE {where_clause}"
            cursor.execute(count_query, params)
            total_records = cursor.fetchone()['total']
            
            # 计算分页
            offset = (page - 1) * page_size
            
            # 排序规则
            order_by_clause = f"{sort_field} {sort_order.upper()}"
            
            # 执行分页查询
            data_query = f"""
            SELECT * FROM violations 
            WHERE {where_clause}
            ORDER BY {order_by_clause}
            LIMIT ? OFFSET ?
            """
            cursor.execute(data_query, params + [page_size, offset])
            
            # 提取数据
            violation_data = []
            for row in cursor.fetchall():
                # 将行转换为字典
                item = dict(row)
                
                # 处理商品ID，确保不显示小数点
                if 'goods_id' in item and item['goods_id']:
                    try:
                        # 尝试转换为整数
                        goods_id_value = str(item['goods_id'])
                        if '.' in goods_id_value:
                            item['goods_id'] = goods_id_value.split('.')[0]
                    except:
                        pass  # 如果转换失败，保持原样
                
                # 转换字段名为驼峰式以匹配前端期望
                clean_item = {}
                field_mapping = {
                    'id': 'id',
                    'record_id': 'recordId',
                    'goods_id': 'goodsId',
                    'product_title': 'productTitle',
                    'target_type_id': 'targetTypeId',
                    'punish_measure': 'punishMeasure',
                    'punish_reason': 'punishReason',
                    'start_time': 'startTime',
                    'real_end_time': 'realEndTime',
                    'end_time': 'endTime',
                    'punish_status': 'punishStatus',
                    'appeal_status': 'appealStatus',
                    'appeal_id': 'appealId',
                    'rectification_measure': 'rectificationMeasure',
                    'operation_type_list': 'operationTypeList',
                    'shop_name': 'shopName',
                    'shop_display_name': 'shopDisplayName',
                    'operator': 'operator',
                    'start_time_format': 'startTimeFormat',
                    'end_time_format': 'endTimeFormat',
                    'real_end_time_format': 'realEndTimeFormat',
                    'status_display': 'statusDisplay',
                    'target_type': 'targetType'
                }
                
                for db_field, frontend_field in field_mapping.items():
                    if db_field in item:
                        clean_item[frontend_field] = item[db_field]
                
                # 处理状态显示 - 将"处罚中"显示为"处理中"以匹配前端
                if clean_item.get('statusDisplay') == '处罚中':
                    clean_item['statusDisplay'] = '处理中'
                
                violation_data.append(clean_item)
            
            # 获取统计数据
            try:
                # 时间范围计算
                today = datetime.now().strftime('%Y-%m-%d')
                yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
                one_week_ago = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
                one_month_ago = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
                
                # 打印调试信息
                print(f"DEBUG: 统计时间范围 - 今天: {today}, 昨天: {yesterday}, 一周前: {one_week_ago}, 一月前: {one_month_ago}")
                
                # 1. 今日违规数量 - 改进搜索方式确保匹配当天所有记录
                # 先检查数据库中的日期格式
                cursor.execute("SELECT start_time, start_time_format FROM violations LIMIT 5")
                date_samples = cursor.fetchall()
                print(f"DEBUG: 数据库中的日期样本: {[dict(sample) for sample in date_samples]}")
                
                # 使用更灵活的方式查询今日违规
                today_date_obj = datetime.now().date()
                
                # 尝试多种方式获取今日违规数
                # 方式1: 使用模糊匹配
                cursor.execute(
                    "SELECT COUNT(*) as count FROM violations WHERE start_time LIKE ? OR start_time_format LIKE ?", 
                    [f"{today}%", f"{today}%"]
                )
                today_count_method1 = cursor.fetchone()['count']
                
                # 方式2: 检查格式化日期字段 (如果存在)
                cursor.execute(
                    "SELECT COUNT(*) as count FROM violations WHERE DATE(start_time_format) = ? OR start_time_format LIKE ?", 
                    [today, f"{today}%"]
                )
                today_count_method2 = cursor.fetchone()['count']
                
                # 方式3: 使用模拟数据（临时解决方案）
                # 为了演示，我们可以假设今日有一定数量的违规
                # 实际生产环境应删除此方法，仅依靠真实数据
                random_today_count = 0
                
                # 选择最合适的方法获取今日违规
                today_count = max(today_count_method1, today_count_method2)
                
                # 如果仍然为0且测试环境需要显示数据，则使用模拟数据
                if today_count == 0:
                    print("DEBUG: 使用模拟数据作为今日违规数")
                    today_count = random_today_count
                
                print(f"DEBUG: 今日违规数 - 方法1: {today_count_method1}, 方法2: {today_count_method2}, 最终值: {today_count}")
                
                # 2. 昨日违规数量（用于计算环比增长）
                cursor.execute(
                    "SELECT COUNT(*) as count FROM violations WHERE start_time LIKE ? OR start_time_format LIKE ?", 
                    [f"{yesterday}%", f"{yesterday}%"]
                )
                yesterday_count = cursor.fetchone()['count']
                
                # 3. 一周内违规数量
                cursor.execute(
                    "SELECT COUNT(*) as count FROM violations WHERE (start_time >= ? OR start_time_format >= ?)", 
                    [one_week_ago, one_week_ago]
                )
                week_count = cursor.fetchone()['count']
                
                # 4. 上周同期违规数量（用于计算周同比）
                two_weeks_ago = (datetime.now() - timedelta(days=14)).strftime('%Y-%m-%d')
                cursor.execute(
                    "SELECT COUNT(*) as count FROM violations WHERE (start_time >= ? AND start_time < ?) OR (start_time_format >= ? AND start_time_format < ?)", 
                    [two_weeks_ago, one_week_ago, two_weeks_ago, one_week_ago]
                )
                last_week_count = cursor.fetchone()['count']
                
                # 5. 一月内违规数量
                cursor.execute(
                    "SELECT COUNT(*) as count FROM violations WHERE (start_time >= ? OR start_time_format >= ?)", 
                    [one_month_ago, one_month_ago]
                )
                month_count = cursor.fetchone()['count']
                
                # 6. 上月同期违规数量（用于计算月同比）
                two_months_ago = (datetime.now() - timedelta(days=60)).strftime('%Y-%m-%d')
                cursor.execute(
                    "SELECT COUNT(*) as count FROM violations WHERE (start_time >= ? AND start_time < ?) OR (start_time_format >= ? AND start_time_format < ?)", 
                    [two_months_ago, one_month_ago, two_months_ago, one_month_ago]
                )
                last_month_count = cursor.fetchone()['count']
                
                # 7. 近一个月涉及的店铺数量
                cursor.execute(
                    "SELECT COUNT(DISTINCT shop_name) as count FROM violations WHERE start_time >= ? OR start_time_format >= ?",
                    [one_month_ago, one_month_ago]
                )
                shop_count = cursor.fetchone()['count']
                
                # 8. 上月同期涉及的店铺数量（用于计算店铺增长率）
                cursor.execute(
                    "SELECT COUNT(DISTINCT shop_name) as count FROM violations WHERE (start_time >= ? AND start_time < ?) OR (start_time_format >= ? AND start_time_format < ?)",
                    [two_months_ago, one_month_ago, two_months_ago, one_month_ago]
                )
                last_month_shop_count = cursor.fetchone()['count']
                
                # 计算增长率
                daily_growth = calculate_growth_rate(today_count, yesterday_count)
                weekly_growth = calculate_growth_rate(week_count, last_week_count)
                monthly_growth = calculate_growth_rate(month_count, last_month_count)
                shop_growth = calculate_growth_rate(shop_count, last_month_shop_count)
                
                # 构建统计数据
                statistics = {
                    "today": today_count,
                    "weekly": week_count,
                    "monthly": month_count,
                    "dailyGrowth": daily_growth,
                    "weeklyGrowth": weekly_growth,
                    "monthlyGrowth": monthly_growth,
                    "shopCount": shop_count,
                    "shopGrowth": shop_growth
                }
                
            except Exception as e:
                print(f"获取统计数据出错: {e}")
                statistics = {
                    "today": 0,
                    "weekly": 0,
                    "monthly": 0,
                    "dailyGrowth": 0,
                    "weeklyGrowth": 0,
                    "monthlyGrowth": 0,
                    "shopCount": 0,
                    "shopGrowth": 0
                }
            
            # 获取所有可用的运营人员列表
            cursor.execute("SELECT DISTINCT operator FROM violations ORDER BY operator")
            operators = [row['operator'] for row in cursor.fetchall()]
            
            # 构建响应数据
            response_data = {
                "success": True,
                "total": total_records,
                "page": page,
                "pageSize": page_size,
                "data": violation_data,
                "statistics": statistics,
                "operators": operators
            }
            
            return jsonify(response_data)
        
        except sqlite3.Error as e:
            if conn:
                conn.close()
            print(f"SQLite查询错误: {e}")
            return jsonify({
                "success": False,
                "message": f"数据库查询错误: {str(e)}"
            }), 500
            
    except Exception as e:
        print(f"获取违规数据出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            "success": False,
            "message": f"服务器内部错误: {str(e)}"
        }), 500

# 辅助函数：计算增长率
def calculate_growth_rate(current, previous):
    """计算增长率，以百分比形式返回"""
    if previous == 0:
        return 100 if current > 0 else 0
    return round(((current - previous) / previous) * 100, 2)

@app.route('/api/violation-data/<violation_id>', methods=['GET'])
def get_violation_detail(violation_id):
    """获取特定违规整改数据详情"""
    try:
        # 自动识别最新的CSV文件
        weigui_dir = 'static/WEIGUIFAKUAN'
        csv_files = [f for f in os.listdir(weigui_dir) if f.endswith('.csv') and '违规申诉和整改' in f]
        
        if not csv_files:
            return jsonify({
                "success": False,
                "message": "违规数据文件不存在"
            }), 404
            
        # 按文件名排序（通常包含日期），选择最新的文件
        csv_files.sort(reverse=True)
        latest_csv = csv_files[0]
        csv_file_path = os.path.join(weigui_dir, latest_csv)
        
        # 找到特定ID的记录
        violation = None
        
        if 'pd' in globals():
            try:
                df = pd.read_csv(csv_file_path, encoding='utf-8')
                # 先尝试使用记录ID查找
                filtered_df = df[df['记录ID'] == violation_id]
                if len(filtered_df) == 0:
                    # 如果没找到，可能ID是单独生成的
                    # 但在这个实现中无法处理这种情况，因为数据不是持久存储的
                    return jsonify({
                        "success": False,
                        "message": "未找到指定的违规记录"
                    }), 404
                
                violation = filtered_df.iloc[0].to_dict()
            except Exception as e:
                print(f"查找违规记录出错: {e}")
                return jsonify({
                    "success": False,
                    "message": f"获取违规记录出错: {str(e)}"
                }), 500
        else:
            with open(csv_file_path, 'r', encoding='utf-8') as file:
                csv_reader = csv.DictReader(file)
                for row in csv_reader:
                    if str(row.get('记录ID', '')) == violation_id:
                        violation = row
                        break
        
        if not violation:
            return jsonify({
                "success": False,
                "message": "未找到指定的违规记录"
            }), 404
        
        return jsonify({
            "success": True,
            "data": violation
        })
    
    except Exception as e:
        print(f"获取违规详情出错: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"获取违规详情出错: {str(e)}"
        }), 500

@app.route('/api/violation-data/punish-types', methods=['GET'])
def get_violation_punish_types():
    """获取违规数据中所有的处罚类型"""
    try:
        # 连接数据库
        db_path = 'static/db/violation_data.db'
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 查询所有不为空的处罚措施
        sql = """
        SELECT DISTINCT punish_measure 
        FROM violations 
        WHERE punish_measure IS NOT NULL 
        AND punish_measure != '' 
        AND punish_measure != 'null'
        ORDER BY punish_measure
        """
        
        cursor.execute(sql)
        results = cursor.fetchall()
        
        # 处理处罚措施数据
        punish_types = set()
        
        for row in results:
            punish_measure = row['punish_measure']
            if punish_measure:
                try:
                    # 尝试解析JSON格式的处罚措施
                    if punish_measure.startswith('[') and punish_measure.endswith(']'):
                        # 处理JSON数组格式
                        measures = json.loads(punish_measure.replace("'", '"'))
                        for measure in measures:
                            if measure and measure.strip():
                                punish_types.add(measure.strip())
                    else:
                        # 处理单个字符串
                        punish_types.add(punish_measure.strip())
                except (json.JSONDecodeError, ValueError):
                    # 如果JSON解析失败，作为普通字符串处理
                    if punish_measure.strip():
                        punish_types.add(punish_measure.strip())
        
        # 转换为排序的列表
        punish_types_list = sorted(list(punish_types))
        
        conn.close()
        
        return jsonify({
            "success": True,
            "data": punish_types_list,
            "count": len(punish_types_list)
        })
        
    except Exception as e:
        print(f"获取处罚类型数据错误: {e}")
        return jsonify({
            "success": False,
            "message": f"获取处罚类型数据失败: {str(e)}"
        }), 500

@app.route('/api/violation-data/trend', methods=['GET'])
def get_violation_trend():
    """获取违规数据趋势统计"""
    try:
        # 获取查询参数
        start_date = request.args.get('startDate', '')
        end_date = request.args.get('endDate', '')
        days = int(request.args.get('days', 30))
        
        # 连接数据库
        db_path = 'static/db/violation_data.db'
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 计算日期范围
        if start_date and end_date:
            # 使用自定义日期范围
            try:
                chart_start_date = datetime.strptime(start_date, '%Y-%m-%d')
                chart_end_date = datetime.strptime(end_date, '%Y-%m-%d')
                days = (chart_end_date - chart_start_date).days + 1
            except ValueError:
                # 如果日期格式错误，使用默认范围
                chart_end_date = datetime.now()
                chart_start_date = chart_end_date - timedelta(days=days-1)
        else:
            # 使用默认范围（最近30天）
            chart_end_date = datetime.now()
            chart_start_date = chart_end_date - timedelta(days=days-1)
        
        # 生成日期列表
        date_list = []
        current_date = chart_start_date
        while current_date <= chart_end_date:
            date_list.append(current_date.strftime('%Y-%m-%d'))
            current_date += timedelta(days=1)
        
        # 查询每日违规数量
        daily_counts = {}
        
        # 初始化所有日期的计数为0
        for date_str in date_list:
            daily_counts[date_str] = 0
        
        # 构建查询SQL
        sql = """
        SELECT start_time_format as violation_date, 
               COUNT(*) as count
        FROM violations 
        WHERE start_time_format BETWEEN ? AND ?
        GROUP BY start_time_format
        ORDER BY violation_date
        """
        
        cursor.execute(sql, (chart_start_date.strftime('%Y-%m-%d'), chart_end_date.strftime('%Y-%m-%d')))
        results = cursor.fetchall()
        
        # 更新实际的计数
        for row in results:
            violation_date = row['violation_date']
            if violation_date in daily_counts:
                daily_counts[violation_date] = row['count']
        
        # 计算统计信息
        total_violations = sum(daily_counts.values())
        max_daily = max(daily_counts.values()) if daily_counts else 0
        min_daily = min(daily_counts.values()) if daily_counts else 0
        avg_daily = total_violations / len(date_list) if date_list else 0
        
        # 计算趋势（与前一个周期对比）
        prev_start_date = chart_start_date - timedelta(days=days)
        prev_end_date = chart_start_date - timedelta(days=1)
        
        prev_sql = """
        SELECT COUNT(*) as prev_count
        FROM violations 
        WHERE start_time_format BETWEEN ? AND ?
        """
        
        cursor.execute(prev_sql, (prev_start_date.strftime('%Y-%m-%d'), prev_end_date.strftime('%Y-%m-%d')))
        prev_result = cursor.fetchone()
        prev_total = prev_result['prev_count'] if prev_result else 0
        
        # 计算增长率
        growth_rate = 0
        if prev_total > 0:
            growth_rate = ((total_violations - prev_total) / prev_total) * 100
        elif total_violations > 0:
            growth_rate = 100  # 从0增长视为100%增长
        
        conn.close()
        
        # 返回结果
        return jsonify({
            "success": True,
            "data": {
                "labels": date_list,
                "values": [daily_counts[date] for date in date_list],
                "statistics": {
                    "total": total_violations,
                    "max_daily": max_daily,
                    "min_daily": min_daily,
                    "avg_daily": round(avg_daily, 2),
                    "growth_rate": round(growth_rate, 2),
                    "days": days,
                    "start_date": chart_start_date.strftime('%Y-%m-%d'),
                    "end_date": chart_end_date.strftime('%Y-%m-%d')
                }
            }
        })
        
    except Exception as e:
        print(f"获取违规趋势数据错误: {e}")
        return jsonify({
            "success": False,
            "message": f"获取违规趋势数据失败: {str(e)}"
        }), 500

@app.route('/api/violation-data/export', methods=['POST'])
def export_violation_data():
    """导出违规整改数据"""
    try:
        # 获取请求参数
        data = request.get_json()
        selected_fields = data.get('fields', [])
        shop_filter = data.get('shop', '')
        operator_filter = data.get('operator', '') # 获取运营人员筛选参数
        punish_type_filter = data.get('punishType', '') # 获取处罚类型筛选参数
        status_filter = data.get('status', '') # 获取状态筛选参数
        start_date = data.get('startDate', '')
        end_date = data.get('endDate', '')
        
        # 如果没有选择任何字段，使用默认字段
        if not selected_fields:
            selected_fields = [
                'recordId', 'goodsId', 'productTitle', 'shopName', 
                'targetTypeId', 'punishMeasure', 'punishReason', 
                'startTimeFormat', 'endTimeFormat', 'statusDisplay'
            ]
        
        # 自动识别最新的CSV文件
        weigui_dir = 'static/WEIGUIFAKUAN'
        csv_files = [f for f in os.listdir(weigui_dir) if f.endswith('.csv') and '违规申诉和整改' in f]
            
        if not csv_files:
                return jsonify({
                    "success": False,
                    "message": "违规数据文件不存在"
                }), 404
            
        # 按文件名排序（通常包含日期），选择最新的文件
        csv_files.sort(reverse=True)
        latest_csv = csv_files[0]
        csv_file_path = os.path.join(weigui_dir, latest_csv)
        
        # 保存文件名到文件，用于后续检查是否更新
        last_file_path = os.path.join(os.path.dirname(VIOLATION_DB_PATH), 'last_csv_file.txt')
        needs_reload = True
        
        # 检查是否需要重新加载数据库
        if os.path.exists(last_file_path):
            try:
                with open(last_file_path, 'r') as f:
                    last_csv = f.read().strip()
                    # 如果上次加载的CSV文件和当前最新的不同，需要重新加载
                    if last_csv != latest_csv:
                        print(f"检测到新的CSV文件: {latest_csv}，将重新加载数据库")
                        # 安全删除旧数据库
                        safe_remove_database(VIOLATION_DB_PATH)
                    else:
                        needs_reload = False
            except Exception as e:
                print(f"读取上次CSV文件记录出错: {e}")
                # 出错时保险起见仍然重新加载
                safe_remove_database(VIOLATION_DB_PATH)
        
        # 如果数据库不存在，重新导入CSV
        if not os.path.exists(VIOLATION_DB_PATH) or needs_reload:
            # 确保数据库目录存在
            os.makedirs(os.path.dirname(VIOLATION_DB_PATH), exist_ok=True)
            
            # 导入CSV到数据库
            if not import_csv_to_db(csv_file_path):
                return jsonify({
                    "success": False,
                    "message": "导入数据到数据库失败"
                }), 500
                
            # 保存当前CSV文件名，用于后续检查
            try:
                with open(last_file_path, 'w') as f:
                    f.write(latest_csv)
                print(f"已记录当前CSV文件: {latest_csv}")
            except Exception as e:
                print(f"记录当前CSV文件出错: {e}")
        
        # 连接数据库
        conn = sqlite3.connect(VIOLATION_DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 构建查询条件
        conditions = []
        params = []
        
        if shop_filter and shop_filter != 'all':
            conditions.append("shop_name = ?")
            params.append(shop_filter)
        
        # 运营人员筛选条件
        if operator_filter and operator_filter != 'all':
            conditions.append("operator = ?")
            params.append(operator_filter)
        
        # 处罚类型筛选条件
        if punish_type_filter and punish_type_filter != 'all':
            conditions.append("punish_measure LIKE ?")
            params.append(f"%{punish_type_filter}%")
        
        # 状态筛选条件
        if status_filter and status_filter != 'all':
            # 处理前端传来的"处理中"状态，映射到数据库中的"处罚中"
            if status_filter == "处理中":
                conditions.append("status_display = ?")
                params.append("处罚中")
            else:
                conditions.append("status_display = ?")
                params.append(status_filter)
        
        if start_date:
            # 确保使用正确的日期格式比较
            conditions.append("(start_time_format >= ? OR start_time >= ?)")
            params.extend([start_date, start_date])
        
        if end_date:
            # 确保使用正确的日期格式比较，并包含当天
            end_date_with_time = end_date + " 23:59:59"
            conditions.append("(start_time_format <= ? OR start_time <= ?)")
            params.extend([end_date_with_time, end_date_with_time])
        
        # 组合查询条件
        where_clause = " AND ".join(conditions) if conditions else "1=1"
        
        # 创建字段映射（数据库字段名 -> 前端字段名）
        db_to_frontend = {
            'id': 'id',
            'record_id': 'recordId',
            'goods_id': 'goodsId',
            'product_title': 'productTitle',
            'target_type_id': 'targetTypeId',
            'punish_measure': 'punishMeasure',
            'punish_reason': 'punishReason',
            'start_time': 'startTime',
            'real_end_time': 'realEndTime',
            'end_time': 'endTime',
            'punish_status': 'punishStatus',
            'appeal_status': 'appealStatus',
            'appeal_id': 'appealId',
            'rectification_measure': 'rectificationMeasure',
            'operation_type_list': 'operationTypeList',
            'shop_name': 'shopName',
            'start_time_format': 'startTimeFormat',
            'end_time_format': 'endTimeFormat',
            'real_end_time_format': 'realEndTimeFormat',
            'status_display': 'statusDisplay',
            'target_type': 'targetType'
        }
        
        # 创建反向映射（前端字段名 -> 数据库字段名）
        frontend_to_db = {v: k for k, v in db_to_frontend.items()}
        
        # 字段名中文映射
        field_chinese_names = {
            'recordId': '记录ID',
            'goodsId': '商品ID',
            'productTitle': '商品名称',
            'targetTypeId': '违规目标',
            'punishMeasure': '处罚措施',
            'punishReason': '处罚原因',
            'startTime': '开始时间戳',
            'realEndTime': '实际结束时间戳',
            'endTime': '结束时间戳',
            'punishStatus': '处罚状态码',
            'appealStatus': '申诉状态',
            'appealId': '申诉ID',
            'rectificationMeasure': '整改措施',
            'operationTypeList': '操作类型列表',
            'shopName': '店铺名称',
            'startTimeFormat': '开始时间',
            'endTimeFormat': '结束时间',
            'realEndTimeFormat': '实际结束时间',
            'statusDisplay': '状态',
            'targetType': '目标类型'
        }
        
        # 转换前端字段为数据库字段
        db_fields = [frontend_to_db.get(field, field) for field in selected_fields]
        
        # 查询数据
        query = f"SELECT {', '.join(db_fields)} FROM violations WHERE {where_clause} ORDER BY start_time DESC"
        cursor.execute(query, params)
        rows = cursor.fetchall()
        
        # 生成CSV文件
        output = io.StringIO()
        writer = csv.writer(output)
        
        # 写入中文标题行
        chinese_headers = [field_chinese_names.get(field, field) for field in selected_fields]
        writer.writerow(chinese_headers)
        
        # 写入数据行
        for row in rows:
            row_dict = dict(row)
            writer.writerow([row_dict.get(field, '') for field in db_fields])
        
        # 关闭数据库连接
        conn.close()
        
        # 构建响应
        response = make_response(output.getvalue())
        response.headers["Content-Disposition"] = f"attachment; filename=violation_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        response.headers["Content-type"] = "text/csv"
        
        return response
    
    except Exception as e:
        print(f"导出数据出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            "success": False,
            "message": f"导出数据出错: {str(e)}"
        }), 500

# 违规数据库路径
VIOLATION_DB_PATH = 'static/db/violation_data.db'

# 确保数据库目录存在
os.makedirs(os.path.dirname(VIOLATION_DB_PATH), exist_ok=True)

# 初始化违规数据库
def init_violation_db():
    conn = sqlite3.connect(VIOLATION_DB_PATH)
    cursor = conn.cursor()
    
    # 创建违规数据表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS violations (
        id TEXT PRIMARY KEY,
        record_id TEXT,
        goods_id TEXT,
        product_title TEXT,
        target_type_id TEXT,
        punish_measure TEXT,
        punish_reason TEXT,
        start_time TEXT,
        real_end_time TEXT,
        end_time TEXT,
        punish_status TEXT,
        appeal_status TEXT,
        appeal_id TEXT,
        rectification_measure TEXT,
        operation_type_list TEXT,
        shop_name TEXT,
        start_time_format TEXT,
        end_time_format TEXT,
        real_end_time_format TEXT,
        status_display TEXT,
        target_type TEXT,
        operator TEXT,
        shop_display_name TEXT
    )
    ''')
    
    # 添加索引以提高查询性能
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_shop_name ON violations (shop_name)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_product_title ON violations (product_title)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_status ON violations (status_display)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_start_time ON violations (start_time)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_operator ON violations (operator)')
    
    conn.commit()
    conn.close()
    print("违规数据库初始化完成")

# 从CSV导入数据到SQLite
def import_csv_to_db(csv_file_path):
    try:
        # 初始化数据库
        init_violation_db()
        
        # 连接数据库
        conn = sqlite3.connect(VIOLATION_DB_PATH)
        cursor = conn.cursor()
        
        # 清空现有数据
        cursor.execute('DELETE FROM violations')
        
        # 读取CSV文件
        df = None
        try:
            df = pd.read_csv(csv_file_path, encoding='utf-8')
        except UnicodeDecodeError:
            try:
                df = pd.read_csv(csv_file_path, encoding='gbk')
            except:
                df = pd.read_csv(csv_file_path, encoding='gb18030', errors='replace')
        
        if df is None:
            raise Exception("无法读取CSV文件")
        
        # 处理列名映射
        column_mapping = {
            '记录ID': 'record_id',
            'goodsID': 'goods_id',
            '商品名称': 'product_title',
            '目标类型ID': 'target_type_id',
            '处罚措施': 'punish_measure',
            '处罚原因': 'punish_reason',
            'startTime': 'start_time',
            'realEndTime': 'real_end_time',
            'endTime': 'end_time',
            '处罚状态': 'punish_status',
            'appealStatus': 'appeal_status',
            'appealID': 'appeal_id',
            'rectificationMeasure': 'rectification_measure',
            'operationTypeList': 'operation_type_list',
            '店铺名称': 'shop_name',
            '开始时间': 'start_time_format',
            '结束时间': 'end_time_format',
            '实际结束时间': 'real_end_time_format',
            '状态': 'status_display',
            '目标类型': 'target_type'
        }
        
        # 重命名列
        for old_col, new_col in column_mapping.items():
            if old_col in df.columns:
                df[new_col] = df[old_col]
        
        # 确保id列存在
        if 'id' not in df.columns:
            df['id'] = df['record_id'].apply(lambda x: str(x) if pd.notna(x) else str(uuid.uuid4()))
        
        # 处理日期格式，确保可以正确比较
        if 'start_time_format' in df.columns:
            # 尝试标准化日期格式为YYYY-MM-DD格式
            def standardize_date(date_str):
                if not date_str or pd.isna(date_str):
                    return ""
                try:
                    # 尝试解析各种格式的日期
                    formats = ['%Y-%m-%d', '%Y/%m/%d', '%Y.%m.%d', '%Y-%m-%d %H:%M:%S', '%Y/%m/%d %H:%M:%S']
                    for fmt in formats:
                        try:
                            dt = datetime.strptime(str(date_str).strip(), fmt)
                            return dt.strftime('%Y-%m-%d')
                        except ValueError:
                            continue
                    return str(date_str)  # 如果无法解析，保留原始值
                except Exception:
                    return str(date_str)
            
            df['start_time_format'] = df['start_time_format'].apply(standardize_date)
            if 'end_time_format' in df.columns:
                df['end_time_format'] = df['end_time_format'].apply(standardize_date)
            if 'real_end_time_format' in df.columns:
                df['real_end_time_format'] = df['real_end_time_format'].apply(standardize_date)
        
        # 获取店铺-运营人员映射
        operator_shop_map, _, _, _, _ = get_operator_shop_mapping()
        
        # 添加运营人员信息和展示名称
        def get_operator_for_shop(shop_name):
            for operator, shops in operator_shop_map.items():
                if shop_name in shops:
                    return operator
            return "未分配"
            
        df['operator'] = df['shop_name'].apply(get_operator_for_shop)
        df['shop_display_name'] = df.apply(lambda row: f"{row['shop_name']} [{row['operator']}]", axis=1)
        
        # 处理NaN值
        df = df.fillna("")
        
        # 准备插入的数据
        records = []
        for _, row in df.iterrows():
            record = (
                str(row.get('id', uuid.uuid4())),
                str(row.get('record_id', "")),
                str(row.get('goods_id', "")),
                str(row.get('product_title', "")),
                str(row.get('target_type_id', "")),
                str(row.get('punish_measure', "")),
                str(row.get('punish_reason', "")),
                str(row.get('start_time', "")),
                str(row.get('real_end_time', "")),
                str(row.get('end_time', "")),
                str(row.get('punish_status', "")),
                str(row.get('appeal_status', "")),
                str(row.get('appeal_id', "")),
                str(row.get('rectification_measure', "")),
                str(row.get('operation_type_list', "")),
                str(row.get('shop_name', "")),
                str(row.get('start_time_format', "")),
                str(row.get('end_time_format', "")),
                str(row.get('real_end_time_format', "")),
                str(row.get('status_display', "")),
                str(row.get('target_type', "")),
                str(row.get('operator', "")),
                str(row.get('shop_display_name', ""))
            )
            records.append(record)
        
        # 批量插入数据
        cursor.executemany('''
        INSERT INTO violations 
        (id, record_id, goods_id, product_title, target_type_id, punish_measure, 
        punish_reason, start_time, real_end_time, end_time, punish_status, 
        appeal_status, appeal_id, rectification_measure, operation_type_list, 
        shop_name, start_time_format, end_time_format, real_end_time_format, 
        status_display, target_type, operator, shop_display_name)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', records)
        
        conn.commit()
        conn.close()
        
        print(f"成功导入 {len(records)} 条记录到数据库")
        return True
    except Exception as e:
        print(f"导入CSV到数据库失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

# 推广数据管理API
@app.route('/api/promotion-data', methods=['GET'])
def get_promotion_data():
    """获取推广数据（汇总或详细）"""
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 20))
        sort_field = request.args.get('sortField', 'fetch_time')
        sort_order = request.args.get('sortOrder', 'desc')
        data_type = request.args.get('dataType', 'summary')  # 'summary' 或 'detail'
        keyword = request.args.get('keyword', '')
        shop_filter = request.args.get('shop', '')
        start_date = request.args.get('startDate', '')
        end_date = request.args.get('endDate', '')
        hide_zero_gmv = request.args.get('hideZeroGmv', 'false').lower() == 'true'
        hide_paused_ads = request.args.get('hidePausedAds', 'false').lower() == 'true'
        
        print(f"查询参数: dataType={data_type}, shop={shop_filter}, keyword={keyword}")
        print(f"筛选参数: hideZeroGmv={hide_zero_gmv}, hidePausedAds={hide_paused_ads}")
        print(f"排序参数: sortField={sort_field}, sortOrder={sort_order}")
        
        # 导入database_query模块的函数
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from database_query import query_summary_data, query_ad_detail_data, get_shop_list
        
        # 构建日期范围
        date_range = None
        if start_date and end_date:
            date_range = [start_date, end_date]
        elif start_date:
            date_range = [start_date, start_date]
        elif end_date:
            date_range = [end_date, end_date]
        
        # 根据数据类型查询数据
        if data_type == 'summary':
            df = query_summary_data(shop_name=shop_filter if shop_filter else None, date_range=date_range)
        else:
            df = query_ad_detail_data(shop_name=shop_filter if shop_filter else None, date_range=date_range)
        
        # 数据清理函数
        def clean_numeric_column(series):
            """清理数值列，处理异常数据"""
            try:
                # 将字符串转换为数值，无法转换的设为0
                return pd.to_numeric(series, errors='coerce').fillna(0)
            except Exception:
                return pd.Series([0] * len(series))
        
        # 清理数值列
        numeric_columns = ['spend_amount', 'gmv', 'net_gmv', 'order_num', 'net_order_num', 
                          'order_roi', 'net_order_roi', 'click_num', 'impression_num']
        
        if data_type == 'summary':
            numeric_columns.append('total_ad_num')
        
        for col in numeric_columns:
            if col in df.columns:
                df[col] = clean_numeric_column(df[col])
        
        # 如果有关键词筛选
        if keyword:
            if data_type == 'summary':
                df = df[df['shop_name'].str.contains(keyword, case=False, na=False)]
            else:
                df = df[
                    df['shop_name'].str.contains(keyword, case=False, na=False) |
                    df['ad_name'].str.contains(keyword, case=False, na=False) |
                    df['goods_name'].str.contains(keyword, case=False, na=False)
                ]
        
        # 应用筛选条件
        if hide_zero_gmv:
            df = df[df['gmv'] > 0]
            print(f"应用GMV筛选后，剩余 {len(df)} 条记录")
        
        if hide_paused_ads and data_type == 'detail':
            df = df[~df['ad_status'].isin(['暂停', 'paused'])]
            print(f"应用暂停广告筛选后，剩余 {len(df)} 条记录")
        
        # 排序
        if sort_field in df.columns:
            ascending = sort_order == 'asc'
            try:
                df = df.sort_values(by=sort_field, ascending=ascending)
            except Exception as e:
                print(f"排序失败: {e}")
                # 如果排序失败，使用默认排序
                if 'fetch_time' in df.columns:
                    df = df.sort_values(by='fetch_time', ascending=False)
        
        # 计算统计数据
        try:
            total_shops = len(df['shop_name'].unique()) if not df.empty and 'shop_name' in df.columns else 0
            total_ads = int(df['total_ad_num'].sum()) if data_type == 'summary' and 'total_ad_num' in df.columns else len(df)
            total_spend = float(df['spend_amount'].sum()) if 'spend_amount' in df.columns else 0
            total_gmv = float(df['gmv'].sum()) if 'gmv' in df.columns else 0
            
            # 安全计算平均ROI
            avg_roi = 0
            if 'order_roi' in df.columns and not df.empty:
                try:
                    roi_series = df['order_roi']
                    # 过滤掉无效值
                    valid_roi = roi_series[(roi_series > 0) & (roi_series < 1000)]  # 假设ROI不会超过1000
                    if len(valid_roi) > 0:
                        avg_roi = float(valid_roi.mean())
                except Exception as e:
                    print(f"计算平均ROI失败: {e}")
                    avg_roi = 0
            
            stats = {
                'totalShops': total_shops,
                'totalAds': total_ads,
                'totalSpend': total_spend,
                'totalGmv': total_gmv,
                'avgRoi': avg_roi
            }
        except Exception as e:
            print(f"计算统计数据失败: {e}")
            stats = {
                'totalShops': 0,
                'totalAds': 0,
                'totalSpend': 0,
                'totalGmv': 0,
                'avgRoi': 0
            }
        
        # 分页
        total_records = len(df)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_df = df.iloc[start_idx:end_idx]
        
        # 转换为字典列表
        data = paginated_df.to_dict('records')
        
        # 处理NaN值和数据类型
        for item in data:
            for key, value in item.items():
                if pd.isna(value):
                    item[key] = None
                elif key in numeric_columns:
                    # 确保数值字段是数字类型
                    try:
                        item[key] = float(value) if value is not None else 0
                    except (ValueError, TypeError):
                        item[key] = 0
                else:
                    # 字符串字段
                    item[key] = str(value) if value is not None else ''
        
        return jsonify({
            'success': True,
            'data': data,
            'total': total_records,
            'page': page,
            'pageSize': page_size,
            'statistics': stats
        })
        
    except Exception as e:
        print(f"获取推广数据失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'message': f'获取推广数据失败: {str(e)}'
        }), 500

@app.route('/api/promotion-shops', methods=['GET'])
def get_promotion_shops():
    """获取推广数据中的店铺列表"""
    try:
        # 导入database_query模块的函数
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from database_query import get_shop_list
        
        shops = get_shop_list()
        
        return jsonify({
            'success': True,
            'data': shops
        })
        
    except Exception as e:
        print(f"获取店铺列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取店铺列表失败: {str(e)}'
        }), 500

@app.route('/api/promotion-data/export', methods=['POST'])
def export_promotion_data():
    """导出推广数据到Excel"""
    try:
        # 获取请求参数
        data = request.get_json()
        data_type = data.get('dataType', 'summary')
        shop_filter = data.get('shop', '')
        keyword = data.get('keyword', '')
        hide_zero_gmv = data.get('hideZeroGmv', False)
        hide_paused_ads = data.get('hidePausedAds', False)
        sort_field = data.get('sortField', 'fetch_time')
        sort_order = data.get('sortOrder', 'desc')
        
        print(f"导出参数: dataType={data_type}, shop={shop_filter}, keyword={keyword}")
        print(f"筛选参数: hideZeroGmv={hide_zero_gmv}, hidePausedAds={hide_paused_ads}")
        print(f"排序参数: sortField={sort_field}, sortOrder={sort_order}")
        
        # 导入database_query模块的函数
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from database_query import query_summary_data, query_ad_detail_data
        import pandas as pd
        import tempfile
        
        # 使用与查询API相同的逻辑获取数据
        if data_type == 'summary':
            df = query_summary_data(shop_name=shop_filter if shop_filter else None)
            # 字段映射
            field_mapping = {
                'shop_name': '店铺名称',
                'fetch_date': '数据日期',
                'fetch_time': '获取时间',
                'total_ad_num': '推广总数',
                'spend_amount': '消费金额',
                'gmv': 'GMV',
                'net_gmv': '净GMV',
                'order_num': '订单数',
                'net_order_num': '净订单数',
                'order_roi': '订单ROI',
                'net_order_roi': '净订单ROI',
                'click_rate': '点击率',
                'conversion_rate': '转化率'
            }
        else:
            df = query_ad_detail_data(shop_name=shop_filter if shop_filter else None)
            # 字段映射
            field_mapping = {
                'shop_name': '店铺名称',
                'ad_id': '广告ID',
                'ad_name': '广告名称',
                'goods_name': '商品名称',
                'ad_status': '广告状态',
                'max_cost': '最大消费',
                'spend_amount': '消费金额',
                'spend_ratio': '剩余预算占比',
                'gmv': 'GMV',
                'net_gmv': '净GMV',
                'order_num': '订单数',
                'net_order_num': '净订单数',
                'order_roi': '订单ROI',
                'net_order_roi': '净订单ROI',
                'click_num': '点击数',
                'impression_num': '展示数',
                'click_rate': '点击率',
                'conversion_rate': '转化率',
                'fetch_time': '获取时间'
            }
        
        # 数据清理函数
        def clean_numeric_column(series):
            """清理数值列，处理异常数据"""
            try:
                return pd.to_numeric(series, errors='coerce').fillna(0)
            except Exception:
                return pd.Series([0] * len(series))
        
        # 清理数值列
        numeric_columns = ['spend_amount', 'gmv', 'net_gmv', 'order_num', 'net_order_num', 
                          'order_roi', 'net_order_roi', 'click_num', 'impression_num']
        
        if data_type == 'summary':
            numeric_columns.append('total_ad_num')
        
        for col in numeric_columns:
            if col in df.columns:
                df[col] = clean_numeric_column(df[col])
        
        # 应用筛选
        if keyword:
            if data_type == 'summary':
                df = df[df['shop_name'].str.contains(keyword, case=False, na=False)]
            else:
                keyword_filter = (
                    df['shop_name'].str.contains(keyword, case=False, na=False) |
                    df['ad_name'].str.contains(keyword, case=False, na=False) |
                    df['goods_name'].str.contains(keyword, case=False, na=False) |
                    df['ad_id'].astype(str).str.contains(keyword, case=False, na=False)
                )
                df = df[keyword_filter]
        
        if hide_zero_gmv:
            df = df[df['gmv'] > 0]
        
        if hide_paused_ads and data_type == 'detail':
            df = df[~df['ad_status'].isin(['暂停', 'paused'])]
        
        # 应用排序
        if sort_field in df.columns:
            ascending = sort_order == 'asc'
            try:
                df = df.sort_values(by=sort_field, ascending=ascending)
            except Exception as e:
                print(f"排序失败: {e}")
                # 如果排序失败，使用默认排序
                if 'fetch_time' in df.columns:
                    df = df.sort_values(by='fetch_time', ascending=False)
        
        # 选择需要导出的列
        export_columns = [col for col in field_mapping.keys() if col in df.columns]
        export_df = df[export_columns].copy()
        
        # 重命名列
        export_df = export_df.rename(columns=field_mapping)
        
        # 格式化数据
        for col in export_df.columns:
            if '金额' in col or 'GMV' in col or '消费' in col:
                # 格式化金额列
                if col in export_df.columns:
                    export_df[col] = export_df[col].apply(lambda x: f"¥{x:,.2f}" if pd.notna(x) and x != 0 else "¥0.00")
            elif 'ROI' in col:
                # 格式化ROI列
                if col in export_df.columns:
                    export_df[col] = export_df[col].apply(lambda x: f"{x:.2f}" if pd.notna(x) else "0.00")
            elif '占比' in col or '率' in col:
                # 格式化百分比列
                if col in export_df.columns:
                    export_df[col] = export_df[col].apply(lambda x: f"{x:.2f}%" if pd.notna(x) else "0.00%")
            elif '时间' in col:
                # 格式化时间列
                if col in export_df.columns:
                    export_df[col] = pd.to_datetime(export_df[col], errors='coerce').dt.strftime('%Y-%m-%d %H:%M:%S')
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as tmp_file:
            temp_path = tmp_file.name
        
        try:
            # 导出到Excel
            with pd.ExcelWriter(temp_path, engine='openpyxl') as writer:
                export_df.to_excel(writer, sheet_name='推广数据', index=False)
                
                # 获取工作表并设置列宽
                worksheet = writer.sheets['推广数据']
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            # 读取文件内容
            with open(temp_path, 'rb') as f:
                file_content = f.read()
            
            # 删除临时文件
            os.unlink(temp_path)
            
            # 构建文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            shop_name = f"_{shop_filter}" if shop_filter else ""
            keyword_text = f"_{keyword}" if keyword else ""
            filename = f"推广数据_{data_type}_{timestamp}{shop_name}{keyword_text}.xlsx"
            
            # 返回文件
            response = make_response(file_content)
            response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'
            
            print(f"导出成功: {filename}, 共 {len(export_df)} 条记录")
            return response
            
        except Exception as e:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.unlink(temp_path)
            raise e
        
    except Exception as e:
        print(f"导出推广数据失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'message': f'导出推广数据失败: {str(e)}'
        }), 500

@app.route('/api/promotion-data/trend', methods=['GET'])
def get_promotion_trend():
    """获取店铺推广数据趋势"""
    try:
        shop_name = request.args.get('shop', '')
        days = int(request.args.get('days', 30))
        
        if not shop_name:
            return jsonify({
                'success': False,
                'message': '请指定店铺名称'
            }), 400
        
        # 导入database_query模块的函数
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from database_query import get_shop_performance_trend
        
        df = get_shop_performance_trend(shop_name, days)
        
        # 转换为字典列表
        data = df.to_dict('records')
        
        # 处理NaN值
        for item in data:
            for key, value in item.items():
                if pd.isna(value):
                    item[key] = None
        
        return jsonify({
            'success': True,
            'data': data,
            'shop': shop_name,
            'days': days
        })
        
    except Exception as e:
        print(f"获取推广趋势数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取推广趋势数据失败: {str(e)}'
        }), 500

@app.route('/api/promotion-data/top-ads', methods=['GET'])
def get_top_performing_ads():
    """获取表现最好的广告"""
    try:
        shop_name = request.args.get('shop', '')
        metric = request.args.get('metric', 'gmv')
        limit = int(request.args.get('limit', 10))
        
        # 导入database_query模块的函数
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from database_query import get_top_performing_ads
        
        df = get_top_performing_ads(
            shop_name=shop_name if shop_name else None,
            metric=metric,
            limit=limit
        )
        
        # 转换为字典列表
        data = df.to_dict('records')
        
        # 处理NaN值
        for item in data:
            for key, value in item.items():
                if pd.isna(value):
                    item[key] = None
        
        return jsonify({
            'success': True,
            'data': data,
            'metric': metric,
            'limit': limit
        })
        
    except Exception as e:
        print(f"获取热门广告数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取热门广告数据失败: {str(e)}'
        }), 500

# SPU 数据库相关 API
@app.route('/api/spu-data', methods=['GET'])
def get_spu_data():
    """获取 SPU 数据"""
    try:
        # 获取查询参数
        date_start = request.args.get('date_start') or request.args.get('start_date')  # 兼容两种参数名
        date_end = request.args.get('date_end') or request.args.get('end_date')  # 兼容两种参数名
        style_codes = request.args.get('style_codes')  # 逗号分隔的款式编码
        product_codes = request.args.get('product_codes')  # 逗号分隔的商品编码
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 50))
        search_term = request.args.get('search', '')
        sort_by = request.args.get('sort_by', 'net_sales_amount')  # 排序字段
        sort_order = request.args.get('sort_order', 'desc')  # asc 或 desc
        dimension = request.args.get('dimension', 'style_code')  # 新增维度参数，默认为款式编码
        
        db_path = 'static/db/spu_data.db'
        conn = sqlite3.connect(db_path)
        
        # 构建基础查询 - 排除商品名称包含"无效"的数据
        where_conditions = ["(product_name IS NULL OR product_name NOT LIKE '%无效%')"]
        params = []
        
        if date_start:
            where_conditions.append("date >= ?")
            params.append(date_start)
        
        if date_end:
            where_conditions.append("date <= ?")
            params.append(date_end)
        
        if style_codes:
            style_list = [s.strip() for s in style_codes.split(',')]
            placeholders = ','.join(['?' for _ in style_list])
            where_conditions.append(f"style_code IN ({placeholders})")
            params.extend(style_list)
        
        if product_codes:
            product_list = [p.strip() for p in product_codes.split(',')]
            placeholders = ','.join(['?' for _ in product_list])
            where_conditions.append(f"product_code IN ({placeholders})")
            params.extend(product_list)
        
        if search_term:
            where_conditions.append("(style_code = ? OR product_code = ? OR product_name = ?)")
            params.extend([search_term, search_term, search_term])
        
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
        
        # 根据维度确定分组字段
        if dimension == 'product_code':
            group_by_fields = "style_code, product_code, product_name, brand, supplier"
            select_fields = """
                style_code,
                product_code,
                product_name,
                brand,
                supplier"""
        else:  # 款式编码维度 - 按款式编码分组，不显示商品编码
            group_by_fields = "style_code"
            select_fields = """
                style_code,
                '' as product_code,
                GROUP_CONCAT(DISTINCT product_name) as product_name,
                GROUP_CONCAT(DISTINCT brand) as brand,
                GROUP_CONCAT(DISTINCT supplier) as supplier"""
        
        # 汇总查询
        aggregation_query = f"""
            SELECT 
                {select_fields},
                SUM(net_sales_quantity) as total_quantity,
                SUM(net_sales_amount) as total_amount,
                SUM(net_sales_cost) as total_cost,
                SUM(net_sales_profit) as total_profit,
                AVG(CASE WHEN sales_margin_rate LIKE '%' THEN 
                    CAST(REPLACE(sales_margin_rate, '%', '') AS REAL) 
                    ELSE 0 END) as avg_margin_rate,
                COUNT(DISTINCT date) as days_count,
                MIN(date) as first_date,
                MAX(date) as last_date
            FROM spu_sales_data 
            WHERE {where_clause}
            GROUP BY {group_by_fields}
            ORDER BY {sort_by} {sort_order.upper()}
            LIMIT ? OFFSET ?
        """
        
        offset = (page - 1) * page_size
        params.extend([page_size, offset])
        
        cursor = conn.cursor()
        cursor.execute(aggregation_query, params)
        
        columns = [description[0] for description in cursor.description]
        results = []
        
        for row in cursor.fetchall():
            item = dict(zip(columns, row))
            # 计算增长率等指标
            if item['total_amount'] and item['total_cost']:
                item['profit_margin'] = (item['total_profit'] / item['total_amount']) * 100 if item['total_amount'] > 0 else 0
            else:
                item['profit_margin'] = 0
            
            # 计算同比去年数据
            item['last_year_amount'] = 0
            if date_start and date_end:
                try:
                    from datetime import datetime, timedelta
                    import dateutil.parser
                    
                    start_date = dateutil.parser.parse(date_start).date()
                    end_date = dateutil.parser.parse(date_end).date()
                    
                    # 计算去年同期的日期范围
                    last_year_start = start_date.replace(year=start_date.year - 1)
                    last_year_end = end_date.replace(year=end_date.year - 1)
                    
                    # 根据维度构建查询条件
                    if dimension == 'product_code':
                        last_year_query = """
                            SELECT SUM(net_sales_amount) as last_year_amount
                            FROM spu_sales_data 
                            WHERE (product_name IS NULL OR product_name NOT LIKE '%无效%')
                            AND style_code = ? AND product_code = ?
                            AND date >= ? AND date <= ?
                        """
                        last_year_params = [
                            item['style_code'],
                            item['product_code'],
                            last_year_start.strftime('%Y-%m-%d'),
                            last_year_end.strftime('%Y-%m-%d')
                        ]
                    else:  # 款式编码维度 - 只按款式编码查询
                        last_year_query = """
                            SELECT SUM(net_sales_amount) as last_year_amount
                            FROM spu_sales_data 
                            WHERE (product_name IS NULL OR product_name NOT LIKE '%无效%')
                            AND style_code = ?
                            AND date >= ? AND date <= ?
                        """
                        last_year_params = [
                            item['style_code'], 
                            last_year_start.strftime('%Y-%m-%d'),
                            last_year_end.strftime('%Y-%m-%d')
                        ]
                    
                    cursor.execute(last_year_query, last_year_params)
                    
                    last_year_result = cursor.fetchone()
                    if last_year_result and last_year_result[0]:
                        item['last_year_amount'] = float(last_year_result[0])
                except Exception as e:
                    print(f"计算同比数据时出错: {e}")
                    item['last_year_amount'] = 0
                
            results.append(item)
        
        # 获取总数
        if dimension == 'product_code':
            count_query = f"""
                SELECT COUNT(*)
                FROM (
                    SELECT DISTINCT style_code, product_code, product_name, brand, supplier
                    FROM spu_sales_data 
                    WHERE {where_clause}
                )
            """
        else:  # 款式编码维度
            count_query = f"""
                SELECT COUNT(DISTINCT style_code)
                FROM spu_sales_data 
                WHERE {where_clause}
            """
        
        count_params = params[:-2]  # 去掉 LIMIT 和 OFFSET 参数
        cursor.execute(count_query, count_params)
        total_count = cursor.fetchone()[0]
        
        conn.close()
        
        return jsonify({
            'success': True,
            'data': results,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total': total_count,
                'total_pages': (total_count + page_size - 1) // page_size
            }
        })
        
    except Exception as e:
        print(f"获取 SPU 数据时发生错误: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/spu-decline-analysis', methods=['GET'])
def get_spu_decline_analysis():
    """获取SPU同比下降分析数据"""
    try:
        # 获取查询参数
        date_start = request.args.get('date_start')
        date_end = request.args.get('date_end')
        dimension = request.args.get('dimension', 'style_code')  # 数据维度
        min_decline_rate = float(request.args.get('min_decline_rate', 10.0))  # 最小下降幅度百分比
        min_amount = float(request.args.get('min_amount', 1000.0))  # 最小销售额筛选条件
        page = int(request.args.get('page', 1))  # 新增：页码
        page_size = int(request.args.get('page_size', 50))  # 新增：每页数量
        search_term = request.args.get('search', '')  # 搜索条件
        
        db_path = 'static/db/spu_data.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 如果没有设置日期范围，默认使用当月
        if not date_start or not date_end:
            from datetime import datetime, timedelta
            today = datetime.now()
            # 当月1号到昨天
            date_start = today.replace(day=1).strftime('%Y-%m-%d')
            date_end = (today - timedelta(days=1)).strftime('%Y-%m-%d')
        
        # 计算去年同期日期范围
        from datetime import datetime
        import dateutil.parser
        
        start_date = dateutil.parser.parse(date_start).date()
        end_date = dateutil.parser.parse(date_end).date()
        
        last_year_start = start_date.replace(year=start_date.year - 1)
        last_year_end = end_date.replace(year=end_date.year - 1)
        
        # 根据维度确定分组和查询字段
        if dimension == 'product_code':
            group_by_fields = "style_code, product_code"
            select_fields = """
                style_code,
                product_code,
                product_name,
                brand,
                supplier"""
            group_by_clause = "GROUP BY style_code, product_code"
        else:  # 款式编码维度
            group_by_fields = "style_code"
            select_fields = """
                style_code,
                '' as product_code,
                GROUP_CONCAT(DISTINCT product_name) as product_name,
                GROUP_CONCAT(DISTINCT brand) as brand,
                GROUP_CONCAT(DISTINCT supplier) as supplier"""
            group_by_clause = "GROUP BY style_code"
        
        # 构建搜索条件
        search_condition = ""
        search_params = []
        if search_term:
            search_condition = "AND (style_code LIKE ? OR product_code LIKE ? OR product_name LIKE ?)"
            search_params = [f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"]
        
        # 主查询的基础部分 (用于计数和数据获取)
        query_base = f"""
        WITH current_year_data AS (
            SELECT 
                {select_fields},
                SUM(net_sales_amount) as current_amount,
                SUM(net_sales_quantity) as current_quantity,
                COUNT(DISTINCT date) as current_days
            FROM spu_sales_data 
            WHERE (product_name IS NULL OR product_name NOT LIKE '%无效%')
              AND date >= ? AND date <= ?
              {search_condition}
            {group_by_clause}
            HAVING SUM(net_sales_amount) >= ?
        ),
        last_year_data AS (
            SELECT 
                {group_by_fields},
                SUM(net_sales_amount) as last_year_amount,
                SUM(net_sales_quantity) as last_year_quantity,
                COUNT(DISTINCT date) as last_year_days
            FROM spu_sales_data 
            WHERE (product_name IS NULL OR product_name NOT LIKE '%无效%')
              AND date >= ? AND date <= ?
              {search_condition}
            {group_by_clause}
        )
        SELECT 
            c.*,
            COALESCE(l.last_year_amount, 0) as last_year_amount,
            COALESCE(l.last_year_quantity, 0) as last_year_quantity,
            COALESCE(l.last_year_days, 0) as last_year_days,
            CASE 
                WHEN COALESCE(l.last_year_amount, 0) > 0 THEN 
                    ((c.current_amount - COALESCE(l.last_year_amount, 0)) / COALESCE(l.last_year_amount, 0)) * 100
                ELSE 
                    CASE WHEN c.current_amount > 0 THEN 100 ELSE 0 END
            END as growth_rate,
            (c.current_amount - COALESCE(l.last_year_amount, 0)) as amount_diff
        FROM current_year_data c
        LEFT JOIN last_year_data l ON """
        
        # 根据维度添加JOIN条件
        if dimension == 'product_code':
            query_base += "c.style_code = l.style_code AND c.product_code = l.product_code"
        else:
            query_base += "c.style_code = l.style_code"
        
        # 添加筛选条件：只显示下降的SPU
        query_base += """
        WHERE (
            CASE 
                WHEN COALESCE(l.last_year_amount, 0) > 0 THEN 
                    ((c.current_amount - COALESCE(l.last_year_amount, 0)) / COALESCE(l.last_year_amount, 0)) * 100
                ELSE 
                    CASE WHEN c.current_amount > 0 THEN 100 ELSE 0 END
            END
        ) <= ?
        """
        
        # 准备查询参数
        base_params = [
            date_start, date_end,  # 今年数据查询
            *search_params,  # 搜索条件1
            min_amount,  # 最小销售额
            last_year_start.strftime('%Y-%m-%d'), last_year_end.strftime('%Y-%m-%d'),  # 去年数据查询
            *search_params,  # 搜索条件2
            -min_decline_rate,  # 最大增长率（负数表示下降）
        ]
        
        # 获取总记录数
        count_query = f"SELECT COUNT(*) FROM ({query_base})"
        cursor.execute(count_query, base_params)
        total_records = cursor.fetchone()[0]
        
        # 添加排序和分页
        paged_query = query_base + " ORDER BY growth_rate ASC LIMIT ? OFFSET ?"
        offset = (page - 1) * page_size
        paged_params = base_params + [page_size, offset]

        cursor.execute(paged_query, paged_params)
        columns = [description[0] for description in cursor.description]
        results = []
        
        for row in cursor.fetchall():
            item = dict(zip(columns, row))
            
            # 格式化数据
            item['current_amount'] = round(float(item['current_amount'] or 0), 2)
            item['last_year_amount'] = round(float(item['last_year_amount'] or 0), 2)
            item['growth_rate'] = round(float(item['growth_rate'] or 0), 2)
            item['amount_diff'] = round(float(item['amount_diff'] or 0), 2)
            item['decline_amount'] = abs(item['amount_diff']) if item['amount_diff'] < 0 else 0
            
            # 计算下降幅度标签
            if item['growth_rate'] <= -50:
                item['decline_level'] = '严重下降'
                item['decline_color'] = '#dc3545'  # 红色
            elif item['growth_rate'] <= -30:
                item['decline_level'] = '显著下降'
                item['decline_color'] = '#fd7e14'  # 橙色
            elif item['growth_rate'] <= -10:
                item['decline_level'] = '轻微下降'
                item['decline_color'] = '#ffc107'  # 黄色
            else:
                item['decline_level'] = '微降'
                item['decline_color'] = '#6c757d'  # 灰色
            
            results.append(item)
        
        # 获取统计信息
        stats_query = f"""
        WITH current_year_data AS (
            SELECT 
                {group_by_fields},
                SUM(net_sales_amount) as current_amount
            FROM spu_sales_data 
            WHERE (product_name IS NULL OR product_name NOT LIKE '%无效%')
              AND date >= ? AND date <= ?
              {search_condition}
            {group_by_clause}
            HAVING SUM(net_sales_amount) >= ?
        ),
        last_year_data AS (
            SELECT 
                {group_by_fields},
                SUM(net_sales_amount) as last_year_amount
            FROM spu_sales_data 
            WHERE (product_name IS NULL OR product_name NOT LIKE '%无效%')
              AND date >= ? AND date <= ?
              {search_condition}
            {group_by_clause}
        )
        SELECT 
            COUNT(*) as total_compared_spus,
            SUM(CASE WHEN c.current_amount < COALESCE(l.last_year_amount, 0) THEN 1 ELSE 0 END) as declining_count,
            SUM(CASE WHEN c.current_amount >= COALESCE(l.last_year_amount, 0) THEN 1 ELSE 0 END) as growing_count,
            SUM(c.current_amount) as total_current_amount,
            SUM(COALESCE(l.last_year_amount, 0)) as total_last_year_amount
        FROM current_year_data c
        LEFT JOIN last_year_data l ON """
        
        if dimension == 'product_code':
            stats_query += "c.style_code = l.style_code AND c.product_code = l.product_code"
        else:
            stats_query += "c.style_code = l.style_code"
        
        stats_params = [
            date_start, date_end,
            *search_params,
            min_amount,
            last_year_start.strftime('%Y-%m-%d'), last_year_end.strftime('%Y-%m-%d'),
            *search_params
        ]
        
        cursor.execute(stats_query, stats_params)
        stats = cursor.fetchone()
        
        statistics = {
            'total_compared_spus': int(stats[0] or 0),
            'declining_count': int(stats[1] or 0),
            'growing_count': int(stats[2] or 0),
            'total_current_amount': round(float(stats[3] or 0), 2),
            'total_last_year_amount': round(float(stats[4] or 0), 2),
            'decline_rate': round((int(stats[1] or 0) / max(int(stats[0] or 0), 1)) * 100, 2),
            'overall_growth_rate': round(((float(stats[3] or 0) - float(stats[4] or 0)) / max(float(stats[4] or 0), 1)) * 100, 2) if stats[4] and float(stats[4]) > 0 else 0,
            'date_range': {
                'current': {'start': date_start, 'end': date_end},
                'last_year': {'start': last_year_start.strftime('%Y-%m-%d'), 'end': last_year_end.strftime('%Y-%m-%d')}
            }
        }
        
        conn.close()
        
        return jsonify({
            'success': True,
            'data': results,
            'statistics': statistics,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total': total_records,
                'total_pages': (total_records + page_size - 1) // page_size
            },
            'params': {
                'dimension': dimension,
                'min_decline_rate': min_decline_rate,
                'min_amount': min_amount,
                'search_term': search_term
            }
        })
        
    except Exception as e:
        print(f"获取SPU同比下降分析数据时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': str(e)}), 500

def handle_year_compare_data(date_start, date_end, granularity, search, selected_spus, multi_line, dimension, year1, year2):
    """处理年度对比数据"""
    try:
        from datetime import datetime, timedelta
        import dateutil.parser
        
        db_path = 'static/db/spu_data.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 根据粒度调整日期分组
        if granularity == 'monthly':
            date_group = "substr(date, 1, 7)"  # YYYY-MM
        elif granularity == 'weekly':
            date_group = "date(date, 'weekday 0', '-6 days')"  # 周一
        else:
            date_group = "date"
        
        # 计算两年的日期范围
        if date_start and date_end:
            # 解析用户设置的日期范围
            start_date = dateutil.parser.parse(date_start).date()
            end_date = dateutil.parser.parse(date_end).date()
            
            # 第一年的日期范围（使用用户设置的日期范围）
            year1_start = start_date.replace(year=int(year1))
            year1_end = end_date.replace(year=int(year1))
            
            # 第二年的日期范围（保持相同的月日，但年份不同）
            year2_start = start_date.replace(year=int(year2))
            year2_end = end_date.replace(year=int(year2))
        else:
            # 如果没有设置日期范围，使用当前年份1月1日到昨天作为默认
            yesterday = datetime.now() - timedelta(days=1)
            current_year = datetime.now().year
            
            # 默认使用当前年份的1月1日到昨天
            year1_start = datetime(int(year1), 1, 1).date()
            year1_end = datetime(int(year1), yesterday.month, yesterday.day).date()
            
            year2_start = datetime(int(year2), 1, 1).date()
            year2_end = datetime(int(year2), yesterday.month, yesterday.day).date()
        
        print(f"年度对比数据查询: {year1}年 {year1_start} 到 {year1_end}, {year2}年 {year2_start} 到 {year2_end}")
        
        # 基础WHERE条件
        base_where_conditions = ["(product_name IS NULL OR product_name NOT LIKE '%无效%')"]
        
        # 处理搜索条件
        search_conditions = []
        if search and search.strip():
            search_conditions.append("(style_code LIKE ? OR product_code LIKE ?)")
        
        # 解析选择的SPU
        selected_spu_list = []
        spu_conditions = []
        if selected_spus and multi_line == 'true':
            try:
                import json
                selected_spu_list = json.loads(selected_spus)
                if selected_spu_list:
                    for spu in selected_spu_list:
                        if spu.get('style_code') and spu.get('product_code'):
                            spu_conditions.append("(style_code = ? AND product_code = ?)")
                        elif spu.get('style_code'):
                            spu_conditions.append("style_code = ?")
                        elif spu.get('product_code'):
                            spu_conditions.append("product_code = ?")
            except json.JSONDecodeError:
                pass
        
        # 如果是多线模式且有选择的SPU
        if multi_line == 'true' and selected_spu_list:
            # 首先获取日期范围（使用第一年的数据获取日期列表）
            base_where = " AND ".join(base_where_conditions)
            date_query = f"""
                SELECT DISTINCT {date_group} as period
                FROM spu_sales_data 
                WHERE {base_where} AND date >= ? AND date <= ?
                ORDER BY period
            """
            cursor.execute(date_query, [year1_start.strftime('%Y-%m-%d'), year1_end.strftime('%Y-%m-%d')])
            dates = [row[0] for row in cursor.fetchall()]
            
            # 为每个SPU查询两年的数据
            spu_data = []
            
            for spu in selected_spu_list:
                # 构建SPU特定的查询条件
                spu_where_conditions = base_where_conditions.copy()
                spu_params_base = []
                
                # 添加搜索条件
                if search and search.strip():
                    spu_where_conditions.extend(search_conditions)
                    search_param = f"%{search.strip()}%"
                    spu_params_base.extend([search_param, search_param])
                
                # 添加SPU特定条件
                spu_dimension = spu.get('dimension', dimension)
                if spu_dimension == 'style_code':
                    if spu.get('style_code'):
                        spu_where_conditions.append("style_code = ?")
                        spu_params_base.append(spu['style_code'])
                    else:
                        continue
                else:
                    if spu.get('style_code') and spu.get('product_code'):
                        spu_where_conditions.append("style_code = ? AND product_code = ?")
                        spu_params_base.extend([spu['style_code'], spu['product_code']])
                    else:
                        continue
                
                # 查询两年的数据
                year_data = {}
                
                for year, year_start, year_end in [(year1, year1_start, year1_end), (year2, year2_start, year2_end)]:
                    where_clause = " AND ".join(spu_where_conditions + ["date >= ?", "date <= ?"])
                    params = spu_params_base + [year_start.strftime('%Y-%m-%d'), year_end.strftime('%Y-%m-%d')]
                    
                    trend_query = f"""
                        SELECT 
                            {date_group} as period,
                            SUM(net_sales_amount) as total_amount
                        FROM spu_sales_data 
                        WHERE {where_clause}
                        GROUP BY {date_group}
                        ORDER BY period
                    """
                    
                    cursor.execute(trend_query, params)
                    results = cursor.fetchall()
                    
                    # 创建数据字典
                    data_dict = {row[0]: float(row[1] or 0) for row in results}
                    
                    # 构建完整的数据数组，对比模式下第二年需要映射日期
                    if year == year1:
                        key_dates = dates
                    else:
                        key_dates = [d.replace(year1, year2) for d in dates]
                    
                    amounts = [data_dict.get(kd, 0) for kd in key_dates]
                    
                    year_data[year] = {
                        'amounts': amounts
                    }
                
                # 根据维度决定显示名称
                if spu_dimension == 'style_code':
                    display_name = spu.get('style_code', '')
                else:
                    display_name = spu.get('product_name', '')
                
                spu_data.append({
                    'style_code': spu.get('style_code', ''),
                    'product_code': spu.get('product_code', ''),
                    'product_name': display_name,
                    'dimension': spu_dimension,
                    'year_data': year_data
                })
        
        else:
            # 单线模式：聚合所有数据
            # 获取第一年的日期范围
            base_where = " AND ".join(base_where_conditions)
            date_query = f"""
                SELECT DISTINCT {date_group} as period
                FROM spu_sales_data 
                WHERE {base_where} AND date >= ? AND date <= ?
                ORDER BY period
            """
            cursor.execute(date_query, [year1_start.strftime('%Y-%m-%d'), year1_end.strftime('%Y-%m-%d')])
            dates = [row[0] for row in cursor.fetchall()]
            
            # 构建查询条件
            where_conditions = base_where_conditions.copy()
            params_base = []
            
            # 添加搜索条件
            if search and search.strip():
                where_conditions.extend(search_conditions)
                search_param = f"%{search.strip()}%"
                params_base.extend([search_param, search_param])
            
            # 查询两年的聚合数据
            year_data = {}
            
            for year, year_start, year_end in [(year1, year1_start, year1_end), (year2, year2_start, year2_end)]:
                where_clause = " AND ".join(where_conditions + ["date >= ?", "date <= ?"])
                params = params_base + [year_start.strftime('%Y-%m-%d'), year_end.strftime('%Y-%m-%d')]
                
                trend_query = f"""
                    SELECT 
                        {date_group} as period,
                        SUM(net_sales_quantity) as total_quantity,
                        SUM(net_sales_amount) as total_amount,
                        SUM(net_sales_cost) as total_cost,
                        SUM(net_sales_profit) as total_profit
                    FROM spu_sales_data 
                    WHERE {where_clause}
                    GROUP BY {date_group}
                    ORDER BY period
                """
                
                cursor.execute(trend_query, params)
                results = cursor.fetchall()
                
                # 创建数据字典
                data_dict = {}
                for row in results:
                    data_dict[row[0]] = {
                        'amount': float(row[2] or 0),
                        'quantity': int(row[1] or 0),
                        'profit': float(row[4] or 0)
                    }
                
                # 构建完整的数据数组
                amounts = [data_dict.get(date, {}).get('amount', 0) for date in dates]
                quantities = [data_dict.get(date, {}).get('quantity', 0) for date in dates]
                profits = [data_dict.get(date, {}).get('profit', 0) for date in dates]
                
                year_data[year] = {
                    'amounts': amounts,
                    'quantities': quantities,
                    'profits': profits
                }
            
            # 构造年度对比数据
            spu_data = []
            for year in [year1, year2]:
                if year in year_data:
                    spu_data.append({
                        'year': year,
                        'amounts': year_data[year]['amounts'],
                        'quantities': year_data[year]['quantities'],
                        'profits': year_data[year]['profits']
                    })
        
        conn.close()
        
        return jsonify({
            'success': True,
            'dates': dates,
            'data': spu_data
        })
        
    except Exception as e:
        print(f"处理年度对比数据时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/spu-trend-data', methods=['GET'])
def get_spu_trend_data():
    """获取 SPU 趋势数据"""
    try:
        # 获取查询参数
        date_start = request.args.get('date_start')
        date_end = request.args.get('date_end')
        granularity = request.args.get('granularity', 'daily')  # daily, weekly, monthly
        search = request.args.get('search')  # 搜索款式编码
        selected_spus = request.args.get('selected_spus')  # 选择的SPU
        multi_line = request.args.get('multi_line', 'false')  # 是否需要多条线
        dimension = request.args.get('dimension', 'product_code')  # 数据维度
        
        # 年度对比参数
        year_compare = request.args.get('year_compare', 'false')
        year1 = request.args.get('year1')
        year2 = request.args.get('year2')
        
        # 如果是年度对比模式，处理年度对比逻辑
        if year_compare == 'true' and year1 and year2:
            return handle_year_compare_data(date_start, date_end, granularity, search, selected_spus, multi_line, dimension, year1, year2)
        
        db_path = 'static/db/spu_data.db'
        conn = sqlite3.connect(db_path)
        
        # 构建查询条件 - 排除商品名称包含"无效"的数据
        where_conditions = ["(product_name IS NULL OR product_name NOT LIKE '%无效%')"]
        params = []
        
        if date_start:
            where_conditions.append("date >= ?")
            params.append(date_start)
        
        if date_end:
            where_conditions.append("date <= ?")
            params.append(date_end)
        
        # 如果有搜索条件
        if search and search.strip():
            where_conditions.append("(style_code LIKE ? OR product_code LIKE ?)")
            search_param = f"%{search.strip()}%"
            params.extend([search_param, search_param])
        
        # 解析选择的SPU
        selected_spu_list = []
        if selected_spus:
            try:
                import json
                selected_spu_list = json.loads(selected_spus)
                if selected_spu_list:
                    spu_conditions = []
                    for spu in selected_spu_list:
                        if spu.get('style_code') and spu.get('product_code'):
                            spu_conditions.append("(style_code = ? AND product_code = ?)")
                            params.extend([spu['style_code'], spu['product_code']])
                        elif spu.get('style_code'):
                            spu_conditions.append("style_code = ?")
                            params.append(spu['style_code'])
                        elif spu.get('product_code'):
                            spu_conditions.append("product_code = ?")
                            params.append(spu['product_code'])
                    
                    if spu_conditions:
                        where_conditions.append(f"({' OR '.join(spu_conditions)})")
            except json.JSONDecodeError:
                pass
        
        where_clause = " AND ".join(where_conditions)
        
        # 根据粒度调整日期分组
        if granularity == 'monthly':
            date_group = "substr(date, 1, 7)"  # YYYY-MM
        elif granularity == 'weekly':
            date_group = "date(date, 'weekday 0', '-6 days')"  # 周一
        else:
            date_group = "date"

        cursor = conn.cursor()
        
        if multi_line == 'true' and selected_spu_list:
            # 多条线模式：为每个SPU单独查询数据
            spu_data = []
            
            # 首先获取日期范围 - 使用基础的日期过滤条件
            base_where_conditions = ["(product_name IS NULL OR product_name NOT LIKE '%无效%')"]
            base_params = []
            
            if date_start:
                base_where_conditions.append("date >= ?")
                base_params.append(date_start)
            
            if date_end:
                base_where_conditions.append("date <= ?")
                base_params.append(date_end)
            
            base_where_clause = " AND ".join(base_where_conditions)
            
            date_query = f"""
                SELECT DISTINCT {date_group} as period
                FROM spu_sales_data 
                WHERE {base_where_clause}
                ORDER BY period
            """
            cursor.execute(date_query, base_params)
            dates = [row[0] for row in cursor.fetchall()]
            
            # 为每个选择的SPU查询趋势数据
            for spu in selected_spu_list:
                # 构建特定SPU的WHERE条件
                spu_where_conditions = ["(product_name IS NULL OR product_name NOT LIKE '%无效%')"]
                spu_params = []
                
                # 添加日期过滤
                if date_start:
                    spu_where_conditions.append("date >= ?")
                    spu_params.append(date_start)
                if date_end:
                    spu_where_conditions.append("date <= ?")
                    spu_params.append(date_end)
                
                # 添加搜索条件（如果有）
                if search and search.strip():
                    search_param = f"%{search.strip()}%"
                    spu_where_conditions.append("(style_code LIKE ? OR product_code LIKE ?)")
                    spu_params.extend([search_param, search_param])
                
                # 根据维度添加特定SPU条件
                spu_dimension = spu.get('dimension', dimension)
                
                # 检查SPU数据完整性
                spu_valid = False
                if spu_dimension == 'style_code':
                    # 款式编码维度：只需要款式编码
                    if spu.get('style_code'):
                        spu_where_conditions.append("style_code = ?")
                        spu_params.append(spu['style_code'])
                        spu_valid = True
                else:
                    # 商品编码维度：必须匹配款式编码和商品编码
                    if spu.get('style_code') and spu.get('product_code'):
                        spu_where_conditions.append("style_code = ? AND product_code = ?")
                        spu_params.extend([spu['style_code'], spu['product_code']])
                        spu_valid = True
                    else:
                        print(f"跳过不完整的SPU数据: {spu}")
                
                # 只有数据完整才执行查询
                if not spu_valid:
                    # 添加一个空的数据项
                    spu_data.append({
                        'style_code': spu.get('style_code', ''),
                        'product_code': spu.get('product_code', ''),
                        'product_name': spu.get('product_name', ''),
                        'amounts': [0] * len(dates)
                    })
                    continue
                
                spu_where_clause = " AND ".join(spu_where_conditions)
                
                trend_query = f"""
                    SELECT 
                        {date_group} as period,
                        SUM(net_sales_amount) as total_amount
                    FROM spu_sales_data 
                    WHERE {spu_where_clause}
                    GROUP BY {date_group}
                    ORDER BY period
                """
                
                print(f"SPU查询: {trend_query}")
                print(f"查询参数: {spu_params}")
                
                cursor.execute(trend_query, spu_params)
                results = cursor.fetchall()
                
                print(f"查询结果: {results}")
                
                # 创建数据字典
                data_dict = {row[0]: float(row[1] or 0) for row in results}
                
                # 构建完整的数据数组（包含空值）
                amounts = [data_dict.get(date, 0) for date in dates]
                
                # 根据维度决定显示名称
                if spu_dimension == 'style_code':
                    # 款式编码维度：使用款式编码作为显示名称
                    display_name = spu.get('style_code', '')
                else:
                    # 商品编码维度：使用完整信息
                    display_name = spu.get('product_name', '')
                
                spu_data.append({
                    'style_code': spu.get('style_code', ''),
                    'product_code': spu.get('product_code', ''),
                    'product_name': display_name,
                    'amounts': amounts
                })
        else:
            # 单条线模式：聚合数据
            trend_query = f"""
                SELECT 
                    {date_group} as period,
                    SUM(net_sales_quantity) as total_quantity,
                    SUM(net_sales_amount) as total_amount,
                    SUM(net_sales_cost) as total_cost,
                    SUM(net_sales_profit) as total_profit
                FROM spu_sales_data 
                WHERE {where_clause}
                GROUP BY {date_group}
                ORDER BY period
            """
            
            cursor.execute(trend_query, params)
            results = cursor.fetchall()
            
            # 组织数据为前端需要的格式
            dates = []
            amounts = []
            quantities = []
            profits = []
            
            for row in results:
                dates.append(row[0])
                amounts.append(float(row[2] or 0))
                quantities.append(int(row[1] or 0))
                profits.append(float(row[4] or 0))
            
            # 构造一个虚拟的SPU数据结构供前端处理
            spu_data = [{
                'style_code': 'ALL',
                'product_code': 'AGGREGATE',
                'amounts': amounts,
                'quantities': quantities,
                'profits': profits
            }] if dates else []
        
        conn.close()
        
        return jsonify({
            'success': True,
            'dates': dates,
            'data': spu_data
        })
        
    except Exception as e:
        print(f"获取 SPU 趋势数据时发生错误: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/sale_trend', methods=['GET'])
def get_sale_trend():
    """获取销售趋势数据"""
    try:
        # 获取参数
        date_start = request.args.get('date_start')
        date_end = request.args.get('date_end')
        granularity = request.args.get('granularity', 'daily')
        selected_sales = request.args.get('selected_sales')
        multi_line = request.args.get('multi_line', 'false').lower() == 'true'
        dimension = request.args.get('dimension', 'shop')
        year_compare = request.args.get('year_compare', 'false').lower() == 'true'
        year1 = request.args.get('year1')
        year2 = request.args.get('year2')
        display_mode = request.args.get('display_mode', 'separate')  # separate 或 total

        db_path = 'static/db/sale_data.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 年度对比模式
        if year_compare and year1 and year2:
            import datetime
            
            # 定义时间粒度的日期分组字段
            if granularity == 'weekly':
                date_group_field = "strftime('%W', date)"  # 年度对比使用周数
                date_label_format = '%W'
            elif granularity == 'monthly':
                date_group_field = "strftime('%m', date)"  # 年度对比使用月份
                date_label_format = '%m'
            else:
                date_group_field = "strftime('%m-%d', date)"  # 年度对比使用月-日
                date_label_format = '%m-%d'
            
            # 如果是多条线模式且有选中的销售数据
            if multi_line and selected_sales:
                import json
                sales_list = json.loads(selected_sales)
                
                # 获取两个年份的日期范围
                if date_start and date_end:
                    # 使用指定的日期范围，但替换年份
                    start_date_obj = datetime.datetime.strptime(date_start, '%Y-%m-%d')
                    end_date_obj = datetime.datetime.strptime(date_end, '%Y-%m-%d')
                    
                    year1_start = start_date_obj.replace(year=int(year1)).strftime('%Y-%m-%d')
                    year1_end = end_date_obj.replace(year=int(year1)).strftime('%Y-%m-%d')
                    year2_start = start_date_obj.replace(year=int(year2)).strftime('%Y-%m-%d')
                    year2_end = end_date_obj.replace(year=int(year2)).strftime('%Y-%m-%d')
                else:
                    # 使用完整年份
                    year1_start = f"{year1}-01-01"
                    year1_end = f"{year1}-12-31"
                    year2_start = f"{year2}-01-01"
                    year2_end = f"{year2}-12-31"
                
                # 获取所有相关日期（按时间粒度格式）
                cursor.execute(f"""
                    SELECT DISTINCT {date_group_field} as period_key
                    FROM sale_data 
                    WHERE (date BETWEEN ? AND ?) OR (date BETWEEN ? AND ?)
                    ORDER BY period_key
                """, (year1_start, year1_end, year2_start, year2_end))
                
                period_keys = [row[0] for row in cursor.fetchall()]
                
                # 根据展示模式处理年度对比数据
                if display_mode == 'total':
                    # 全部店铺汇总模式
                    shop_names = [sale_item.get('shop_name', '') for sale_item in sales_list if sale_item.get('shop_name')]
                    
                    if shop_names:
                        shop_placeholders = ','.join(['?' for _ in shop_names])
                        
                        # 获取两个年份的汇总数据
                        year_data = {}
                        for year, start_date, end_date in [(year1, year1_start, year1_end), (year2, year2_start, year2_end)]:
                            cursor.execute(f"""
                                SELECT {date_group_field} as period_key, SUM(sales_amount) as amount
                                FROM sale_data 
                                WHERE shop_name IN ({shop_placeholders}) AND date BETWEEN ? AND ?
                                GROUP BY {date_group_field}
                                ORDER BY {date_group_field}
                            """, shop_names + [start_date, end_date])
                            
                            results = cursor.fetchall()
                            data_dict = {row[0]: row[1] or 0 for row in results}
                            amounts = [data_dict.get(period_key, 0) for period_key in period_keys]
                            
                            year_data[year] = {
                                'amounts': amounts
                            }
                        
                        data = [{
                            'shop_name': '全部选中店铺',
                            'display_name': '全部选中店铺',
                            'year_data': year_data
                        }]
                    else:
                        data = []
                else:
                    # 分店铺展示模式：为每个选中的销售数据获取年度对比趋势
                    data = []
                    for sale_item in sales_list:
                        if dimension == 'shop':
                            shop_name = sale_item.get('shop_name', '')
                            if shop_name:
                                # 获取两个年份的数据
                                year_data = {}
                                for year, start_date, end_date in [(year1, year1_start, year1_end), (year2, year2_start, year2_end)]:
                                    cursor.execute(f"""
                                        SELECT {date_group_field} as period_key, SUM(sales_amount) as amount
                                        FROM sale_data 
                                        WHERE shop_name = ? AND date BETWEEN ? AND ?
                                        GROUP BY {date_group_field}
                                        ORDER BY {date_group_field}
                                    """, (shop_name, start_date, end_date))
                                    
                                    results = cursor.fetchall()
                                    data_dict = {row[0]: row[1] or 0 for row in results}
                                    amounts = [data_dict.get(period_key, 0) for period_key in period_keys]
                                    
                                    year_data[year] = {
                                        'amounts': amounts
                                    }
                                
                                data.append({
                                    'shop_name': shop_name,
                                    'display_name': shop_name,
                                    'year_data': year_data
                                })
                        else:
                            # 日期维度处理
                            date_filter = sale_item.get('date', '')
                            if date_filter:
                                # 提取月日部分
                                try:
                                    date_obj = datetime.datetime.strptime(date_filter, '%Y-%m-%d')
                                    month_day = date_obj.strftime('%m-%d')
                                    
                                    year_data = {}
                                    for year in [year1, year2]:
                                        target_date = f"{year}-{month_day}"
                                        cursor.execute(f"""
                                            SELECT SUM(sales_amount) as amount
                                            FROM sale_data 
                                            WHERE date = ?
                                        """, (target_date,))
                                        
                                        result = cursor.fetchone()
                                        amount = result[0] if result and result[0] else 0
                                        
                                        year_data[year] = {
                                            'amounts': [amount]
                                        }
                                    
                                    data.append({
                                        'date': date_filter,
                                        'display_name': date_filter,
                                        'year_data': year_data
                                    })
                                except ValueError:
                                    continue
                
                conn.close()
                
                return jsonify({
                    'success': True,
                    'dates': period_keys,
                    'data': data
                })
            
            else:
                # 年度对比单条线模式
                # 获取两个年份的日期范围
                if date_start and date_end:
                    start_date_obj = datetime.datetime.strptime(date_start, '%Y-%m-%d')
                    end_date_obj = datetime.datetime.strptime(date_end, '%Y-%m-%d')
                    
                    year1_start = start_date_obj.replace(year=int(year1)).strftime('%Y-%m-%d')
                    year1_end = end_date_obj.replace(year=int(year1)).strftime('%Y-%m-%d')
                    year2_start = start_date_obj.replace(year=int(year2)).strftime('%Y-%m-%d')
                    year2_end = end_date_obj.replace(year=int(year2)).strftime('%Y-%m-%d')
                else:
                    year1_start = f"{year1}-01-01"
                    year1_end = f"{year1}-12-31"
                    year2_start = f"{year2}-01-01"
                    year2_end = f"{year2}-12-31"
                
                # 获取所有相关日期（按时间粒度格式）
                cursor.execute(f"""
                    SELECT DISTINCT {date_group_field} as period_key
                    FROM sale_data 
                    WHERE (date BETWEEN ? AND ?) OR (date BETWEEN ? AND ?)
                    ORDER BY period_key
                """, (year1_start, year1_end, year2_start, year2_end))
                
                period_keys = [row[0] for row in cursor.fetchall()]
                
                # 获取两个年份的聚合数据
                data = []
                
                # 如果有选中的店铺，只统计选中店铺的数据
                if selected_sales:
                    import json
                    sales_list = json.loads(selected_sales)
                    shop_names = [sale_item.get('shop_name', '') for sale_item in sales_list if sale_item.get('shop_name')]
                    
                    if shop_names:
                        shop_placeholders = ','.join(['?' for _ in shop_names])
                        
                        for year, start_date, end_date in [(year1, year1_start, year1_end), (year2, year2_start, year2_end)]:
                            cursor.execute(f"""
                                SELECT {date_group_field} as period_key, SUM(sales_amount) as amount
                                FROM sale_data 
                                WHERE shop_name IN ({shop_placeholders}) AND date BETWEEN ? AND ?
                                GROUP BY {date_group_field}
                                ORDER BY {date_group_field}
                            """, shop_names + [start_date, end_date])
                            
                            results = cursor.fetchall()
                            data_dict = {row[0]: row[1] or 0 for row in results}
                            amounts = [data_dict.get(period_key, 0) for period_key in period_keys]
                            
                            data.append({
                                'year': year,
                                'amounts': amounts
                            })
                    else:
                        # 没有有效的店铺选择，返回空数据
                        for year in [year1, year2]:
                            data.append({
                                'year': year,
                                'amounts': [0] * len(period_keys)
                            })
                else:
                    # 没有选中店铺，统计所有店铺数据
                    for year, start_date, end_date in [(year1, year1_start, year1_end), (year2, year2_start, year2_end)]:
                        cursor.execute(f"""
                            SELECT {date_group_field} as period_key, SUM(sales_amount) as amount
                            FROM sale_data 
                            WHERE date BETWEEN ? AND ?
                            GROUP BY {date_group_field}
                            ORDER BY {date_group_field}
                        """, (start_date, end_date))
                        
                        results = cursor.fetchall()
                        data_dict = {row[0]: row[1] or 0 for row in results}
                        amounts = [data_dict.get(period_key, 0) for period_key in period_keys]
                        
                        data.append({
                            'year': year,
                            'amounts': amounts
                        })
                
                conn.close()
                
                return jsonify({
                    'success': True,
                    'dates': period_keys,
                    'data': data
                })

        # 如果是多条线模式且有选中的销售数据
        elif multi_line and selected_sales:
            import json
            sales_list = json.loads(selected_sales)

            # 构建基础查询条件
            filter_conditions = []
            params = []

            if date_start:
                filter_conditions.append("date >= ?")
                params.append(date_start)

            if date_end:
                filter_conditions.append("date <= ?")
                params.append(date_end)

            where_clause = ""
            if filter_conditions:
                where_clause = "WHERE " + " AND ".join(filter_conditions)

            # 根据时间粒度获取日期分组
            if granularity == 'weekly':
                # 按周分组
                cursor.execute(f"""
                    SELECT DISTINCT strftime('%Y-%W', date) as week_key, 
                           MIN(date) as start_date,
                           MAX(date) as end_date
                    FROM sale_data {where_clause}
                    GROUP BY strftime('%Y-%W', date)
                    ORDER BY week_key
                """, params)
                week_results = cursor.fetchall()
                dates = [f"{row[1]}~{row[2]}" for row in week_results]  # 使用周范围作为标识
                date_group_field = "strftime('%Y-%W', date)"
            elif granularity == 'monthly':
                # 按月分组
                cursor.execute(f"""
                    SELECT DISTINCT strftime('%Y-%m', date) as month_key
                    FROM sale_data {where_clause}
                    ORDER BY month_key
                """, params)
                month_results = cursor.fetchall()
                dates = [row[0] for row in month_results]
                date_group_field = "strftime('%Y-%m', date)"
            else:
                # 按日分组（默认）
                cursor.execute(f"""
                    SELECT DISTINCT date
                    FROM sale_data {where_clause}
                    ORDER BY date
                """, params)
                dates = [row[0] for row in cursor.fetchall()]
                date_group_field = "date"

            # 根据展示模式处理数据
            if display_mode == 'total':
                # 全部店铺汇总模式：将所有选中店铺的数据汇总为一条线
                shop_names = [sale_item.get('shop_name', '') for sale_item in sales_list if sale_item.get('shop_name')]
                
                if shop_names:
                    # 构建店铺名称的占位符
                    shop_placeholders = ','.join(['?' for _ in shop_names])
                    sale_params = params + shop_names
                    
                    cursor.execute(f"""
                        SELECT {date_group_field} as date_key, SUM(sales_amount) as amount
                        FROM sale_data {where_clause} AND shop_name IN ({shop_placeholders})
                        GROUP BY {date_group_field}
                        ORDER BY {date_group_field}
                    """, sale_params)

                    results = cursor.fetchall()
                    data_dict = {row[0]: row[1] or 0 for row in results}
                    
                    # 根据时间粒度处理数据映射
                    if granularity == 'weekly':
                        amounts = []
                        # 重新查询获取对应的周键
                        cursor.execute(f"""
                            SELECT strftime('%Y-%W', date) as week_key, SUM(sales_amount) as amount
                            FROM sale_data {where_clause} AND shop_name IN ({shop_placeholders})
                            GROUP BY strftime('%Y-%W', date)
                            ORDER BY strftime('%Y-%W', date)
                        """, sale_params)
                        week_results = cursor.fetchall()
                        week_dict = {row[0]: row[1] or 0 for row in week_results}
                        
                        # 按顺序映射周数据
                        for i, date_range in enumerate(dates):
                            if week_results and i < len(week_results):
                                amounts.append(week_results[i][1] or 0)
                            else:
                                amounts.append(0)
                    elif granularity == 'monthly':
                        amounts = [data_dict.get(date, 0) for date in dates]
                    else:
                        amounts = [data_dict.get(date, 0) for date in dates]

                    data = [{
                        'shop_name': '全部选中店铺',
                        'display_name': '全部选中店铺',
                        'amounts': amounts
                    }]
                else:
                    data = []
            else:
                # 分店铺展示模式：为每个选中的销售数据获取趋势
                data = []
                for sale_item in sales_list:
                    if dimension == 'shop':
                        # 店铺维度
                        shop_name = sale_item.get('shop_name', '')
                        if shop_name:
                            sale_params = params + [shop_name]
                            cursor.execute(f"""
                                SELECT {date_group_field} as date_key, SUM(sales_amount) as amount
                                FROM sale_data {where_clause} AND shop_name = ?
                                GROUP BY {date_group_field}
                                ORDER BY {date_group_field}
                            """, sale_params)

                            results = cursor.fetchall()
                            data_dict = {row[0]: row[1] or 0 for row in results}
                            
                            # 根据时间粒度处理数据映射
                            if granularity == 'weekly':
                                amounts = []
                                # 重新查询获取单个店铺的周数据
                                cursor.execute(f"""
                                    SELECT strftime('%Y-%W', date) as week_key, SUM(sales_amount) as amount
                                    FROM sale_data {where_clause} AND shop_name = ?
                                    GROUP BY strftime('%Y-%W', date)
                                    ORDER BY strftime('%Y-%W', date)
                                """, sale_params)
                                week_results = cursor.fetchall()
                                
                                # 按顺序映射周数据
                                for i, date_range in enumerate(dates):
                                    if week_results and i < len(week_results):
                                        amounts.append(week_results[i][1] or 0)
                                    else:
                                        amounts.append(0)
                            elif granularity == 'monthly':
                                amounts = [data_dict.get(date, 0) for date in dates]
                            else:
                                amounts = [data_dict.get(date, 0) for date in dates]

                            data.append({
                                'shop_name': shop_name,
                                'display_name': shop_name,
                                'amounts': amounts
                            })
                    else:
                        # 日期维度 - 按日期聚合所有店铺数据
                        date_filter = sale_item.get('date', '')
                        if date_filter:
                            sale_params = params + [date_filter]
                            cursor.execute(f"""
                                SELECT date, SUM(sales_amount) as amount
                                FROM sale_data {where_clause} AND date = ?
                                GROUP BY date
                                ORDER BY date
                            """, sale_params)

                            results = cursor.fetchall()
                            data_dict = {row[0]: row[1] or 0 for row in results}
                            amounts = [data_dict.get(date, 0) for date in dates]

                            data.append({
                                'date': date_filter,
                                'display_name': date_filter,
                                'amounts': amounts
                            })

            conn.close()

            return jsonify({
                'success': True,
                'dates': dates,
                'data': data
            })

        else:
            # 常规趋势模式
            filter_conditions = []
            params = []

            if date_start:
                filter_conditions.append("date >= ?")
                params.append(date_start)

            if date_end:
                filter_conditions.append("date <= ?")
                params.append(date_end)

            where_clause = ""
            if filter_conditions:
                where_clause = "WHERE " + " AND ".join(filter_conditions)

            # 根据时间粒度设置分组字段
            if granularity == 'weekly':
                date_group_field = "strftime('%Y-%W', date)"
                cursor.execute(f"""
                    SELECT {date_group_field} as date_key, 
                           MIN(date) as start_date,
                           MAX(date) as end_date,
                           SUM(sales_amount) as total_value
                    FROM sale_data {where_clause}
                    GROUP BY {date_group_field}
                    ORDER BY {date_group_field}
                """, params)
                results = cursor.fetchall()
                dates = [f"{row[1]}~{row[2]}" for row in results]  # 使用周范围作为标识
                data = [row[3] or 0 for row in results]
            elif granularity == 'monthly':
                date_group_field = "strftime('%Y-%m', date)"
                cursor.execute(f"""
                    SELECT {date_group_field} as date_key, SUM(sales_amount) as total_value
                    FROM sale_data {where_clause}
                    GROUP BY {date_group_field}
                    ORDER BY {date_group_field}
                """, params)
                results = cursor.fetchall()
                dates = [row[0] for row in results]
                data = [row[1] or 0 for row in results]
            else:
                # 按日分组（默认）
                cursor.execute(f"""
                    SELECT date, SUM(sales_amount) as total_value
                    FROM sale_data {where_clause}
                    GROUP BY date
                    ORDER BY date
                """, params)
                results = cursor.fetchall()
                dates = [row[0] for row in results]
                data = [row[1] or 0 for row in results]

            conn.close()

            return jsonify({
                'success': True,
                'dates': dates,
                'data': data
            })

    except Exception as e:
        print(f"获取销售趋势数据时发生错误: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/sale_data', methods=['GET'])
def get_sale_data():
    """获取销售数据列表 - 按店铺聚合"""
    try:
        # 获取分页参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 50))
        
        # 获取过滤参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        search = request.args.get('search', '').strip()
        sort_by = request.args.get('sort_by', 'sales_amount')
        sort_order = request.args.get('sort_order', 'desc')
        
        db_path = 'static/db/sale_data.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 构建查询条件
        filter_conditions = []
        params = []
        
        if start_date:
            filter_conditions.append("date >= ?")
            params.append(start_date)
        
        if end_date:
            filter_conditions.append("date <= ?")
            params.append(end_date)
        
        if search:
            filter_conditions.append("shop_name LIKE ?")
            params.append(f'%{search}%')
        
        where_clause = ""
        if filter_conditions:
            where_clause = "WHERE " + " AND ".join(filter_conditions)
        
        # 验证排序字段
        valid_sort_fields = ['sales_amount', 'sales_orders', 'sales_quantity', 'shop_name']
        if sort_by not in valid_sort_fields:
            sort_by = 'sales_amount'
        
        if sort_order.lower() not in ['asc', 'desc']:
            sort_order = 'desc'
        
        # 按店铺聚合数据
        aggregated_query = f"""
            SELECT 
                shop_name,
                shop_code,
                SUM(sales_orders) as sales_orders,
                SUM(actual_orders) as actual_orders,
                SUM(sales_quantity) as sales_quantity,
                SUM(sales_amount) as sales_amount
            FROM sale_data {where_clause}
            GROUP BY shop_name, shop_code
            ORDER BY {sort_by} {sort_order.upper()}
        """
        
        cursor.execute(aggregated_query, params)
        all_results = cursor.fetchall()
        
        # 计算分页
        total_records = len(all_results)
        total_pages = (total_records + page_size - 1) // page_size
        offset = (page - 1) * page_size
        
        # 获取当前页数据
        page_results = all_results[offset:offset + page_size]
        
        records = []
        for row in page_results:
            records.append({
                'shop_name': row[0],
                'shop_code': row[1],
                'sales_orders': row[2] or 0,
                'actual_orders': row[3] or 0,
                'sales_quantity': row[4] or 0,
                'sales_amount': row[5] or 0
            })
        
        conn.close()
        
        # 构建分页信息
        pagination = {
            'page': page,
            'total_pages': total_pages,
            'total': total_records,
            'start': offset + 1 if total_records > 0 else 0,
            'end': min(offset + page_size, total_records)
        }

        return jsonify({
            'success': True,
            'data': records,
            'pagination': pagination
        })
        
    except Exception as e:
        print(f"获取销售数据列表时发生错误: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/sale_detail', methods=['GET'])
def get_sale_detail():
    """获取销售详情"""
    try:
        shop_name = request.args.get('shop_name')
        date = request.args.get('date')

        if not shop_name or not date:
            return jsonify({'success': False, 'error': '缺少必要参数'}), 400

        db_path = 'static/db/sale_data.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        cursor.execute("""
            SELECT *
            FROM sale_data
            WHERE shop_name = ? AND date = ?
        """, (shop_name, date))

        result = cursor.fetchone()

        if not result:
            conn.close()
            return jsonify({'success': False, 'error': '未找到数据'}), 404

        # 获取列名
        cursor.execute("PRAGMA table_info(sale_data)")
        columns = [row[1] for row in cursor.fetchall()]

        # 构建详情数据
        detail_data = {}
        for i, column in enumerate(columns):
            detail_data[column] = result[i]

        conn.close()

        return jsonify({
            'success': True,
            'data': detail_data
        })

    except Exception as e:
        print(f"获取销售详情时发生错误: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500


if __name__ == '__main__':
    # 确保静态文件夹和数据文件夹存在
    Path('static/data').mkdir(parents=True, exist_ok=True)
    Path('static/temp').mkdir(parents=True, exist_ok=True)
    Path('static/SKU').mkdir(parents=True, exist_ok=True)
    Path('static/goods_data').mkdir(parents=True, exist_ok=True)
    Path('static/spu_day_date').mkdir(parents=True, exist_ok=True)
    Path('static/db').mkdir(parents=True, exist_ok=True)  # 创建数据库目录
    
    # 创建上传文件夹
    UPLOAD_FOLDER = os.path.join('static', 'uploads')
    Path(UPLOAD_FOLDER).mkdir(parents=True, exist_ok=True)

    # 检查账号文件是否存在，如果不存在则创建
    read_accounts() 
    # 检查留言文件是否存在，如果不存在则创建
    messages_path = Path('static') / 'data' / 'messages.json'
    if not messages_path.exists():
        with open(messages_path, 'w', encoding='utf-8') as f:
            json.dump({"messages": [], "next_id": 1}, f, ensure_ascii=False, indent=2)
        print(f"已创建空的留言文件: {messages_path}")


    # 显示FRP配置信息
    setup_frp_info()

    # 启动Flask应用
    app.run(host='0.0.0.0', port=5000, debug=True)