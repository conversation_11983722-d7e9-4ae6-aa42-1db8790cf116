// 销售数据分析平台 JavaScript
class SaleAnalytics {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 50;
        this.totalPages = 1;
        this.currentFilters = {};
        this.charts = {};
        this.dateRange = null;
        this.resizeHandler = null;
        this.currentChartType = 'line'; // 添加当前图表类型跟踪
        this.currentChartData = null; // 保存当前图表数据

        this.init();
    }

    async init() {
        console.log('初始化销售分析平台...');
        this.initDatePicker();
        this.setDefaultDateRange();
        this.bindEvents();
        await this.loadInitialData();
    }

    setDefaultDateRange() {
        // 设置默认日期范围：2025年1月1日至昨天
        const yesterday = moment().subtract(1, 'day');
        const start = moment('2025-01-01');
        
        $('#dateRange').val(start.format('YYYY-MM-DD') + ' - ' + yesterday.format('YYYY-MM-DD'));
        this.dateRange = {
            start: start.format('YYYY-MM-DD'),
            end: yesterday.format('YYYY-MM-DD')
        };
    }

    initDatePicker() {
        // 初始化日期范围选择器
        $('#dateRange').daterangepicker({
            autoUpdateInput: false,
            locale: {
                cancelLabel: '清除',
                applyLabel: '确定',
                format: 'YYYY-MM-DD',
                customRangeLabel: '自定义',
                daysOfWeek: ['日', '一', '二', '三', '四', '五', '六'],
                monthNames: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                firstDay: 1
            }
        });

        $('#dateRange').on('apply.daterangepicker', (ev, picker) => {
            $(ev.target).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
            this.dateRange = {
                start: picker.startDate.format('YYYY-MM-DD'),
                end: picker.endDate.format('YYYY-MM-DD')
            };
        });

        $('#dateRange').on('cancel.daterangepicker', (ev, picker) => {
            $(ev.target).val('');
            this.dateRange = null;
        });
    }

    bindEvents() {
        // 搜索输入框
        $('#searchInput').on('input', this.debounce(() => {
            this.currentPage = 1;
            this.loadTableData();
        }, 300));

        // 页面大小改变
        $('#pageSize').on('change', () => {
            this.pageSize = parseInt($('#pageSize').val());
            this.currentPage = 1;
            this.loadTableData();
        });

        // 时间粒度改变
        $('#granularity').on('change', () => {
            console.log('时间粒度改变:', $('#granularity').val());
            // 重新渲染当前的图表
            this.refreshTrendChart();
        });

        // 数据展示模式改变
        $('#displayMode').on('change', () => {
            const displayMode = $('#displayMode').val();
            console.log('数据展示模式改变:', displayMode);
            
            if (displayMode === 'total') {
                // 全部店铺汇总模式：直接显示所有店铺汇总数据，不依赖选中状态
                const isYearCompare = $('#yearCompareSwitch').is(':checked');
                if (isYearCompare) {
                    this.loadTrendDataYearCompare();
                } else {
                    this.loadTrendData();
                }
            } else {
                // 分店铺展示模式：依赖选中状态
                this.refreshTrendChart();
            }
        });

        // 排序方式改变
        $('#sortBy').on('change', () => {
            console.log('排序方式改变:', $('#sortBy').val());
            this.currentPage = 1;
            this.loadTableData();
        });

        // 图表类型切换
        $('[data-chart-type]').on('click', (e) => {
            const chartType = $(e.target).closest('button').data('chart-type');
            $('[data-chart-type]').removeClass('active');
            $(e.target).closest('button').addClass('active');
            this.updateChartType(chartType);
        });



        // 趋势图刷新按钮
        $('#refreshTrendBtn').on('click', () => {
            this.refreshTrendChart();
        });

        // 全选功能
        $('#selectAll').on('change', (e) => {
            const isChecked = $(e.target).is(':checked');
            $('.sale-select').prop('checked', isChecked);

            // 如果是全部店铺汇总模式，不需要响应选择状态变化
            const displayMode = $('#displayMode').val();
            if (displayMode === 'total') {
                return;
            }

            // 根据年度对比状态调用相应的方法
            const isYearCompare = $('#yearCompareSwitch').is(':checked');
            if (isYearCompare) {
                this.updateSelectedTrendYearCompare();
            } else {
                this.updateSelectedTrend();
            }
        });

        // 单选功能
        $(document).on('change', '.sale-select', () => {
            // 如果是全部店铺汇总模式，不需要响应选择状态变化
            const displayMode = $('#displayMode').val();
            if (displayMode === 'total') {
                return;
            }
            
            // 根据年度对比状态调用相应的方法
            const isYearCompare = $('#yearCompareSwitch').is(':checked');
            if (isYearCompare) {
                this.updateSelectedTrendYearCompare();
            } else {
                this.updateSelectedTrend();
            }
        });



        // 年度对比功能
        $('#yearCompareSwitch').on('change', (e) => {
            const isEnabled = $(e.target).is(':checked');
            console.log('年度对比开关:', isEnabled);
            
            if (isEnabled) {
                $('#yearCompareControls').fadeIn(300);
                
                // 添加年度对比提示
                this.showYearCompareHint();
                
                // 为图表区域添加年度对比样式
                $('.chart-card').parent().addClass('year-compare-active');
                
                // 启用年度对比时，重新加载趋势数据
                this.refreshTrendChart();
            } else {
                $('#yearCompareControls').fadeOut(300);
                
                // 移除年度对比提示
                this.hideYearCompareHint();
                
                // 移除图表区域的年度对比样式
                $('.chart-card').parent().removeClass('year-compare-active');
                
                // 关闭年度对比时，重新加载常规趋势数据
                this.refreshTrendChart();
            }
        });

        // 年度对比年份选择
        $('#compareYear1, #compareYear2').on('change', () => {
            const year1 = $('#compareYear1').val();
            const year2 = $('#compareYear2').val();
            
            if (year1 === year2) {
                alert('请选择不同的年份进行对比');
                return;
            }
            
            console.log('年度对比年份变更:', year1, 'vs', year2);
            
            // 如果年度对比开关是打开的，更新提示并重新加载数据
            if ($('#yearCompareSwitch').is(':checked')) {
                this.showYearCompareHint(); // 更新提示内容
                this.refreshTrendChart();
            }
        });
    }



    refreshTrendChart() {
        const isYearCompare = $('#yearCompareSwitch').is(':checked');
        
        console.log('刷新趋势图... 年度对比:', isYearCompare);
        
        // 根据当前状态决定刷新逻辑
        const selectedItems = $('.sale-select:checked');
        
        console.log('选中项目数量:', selectedItems.length);
        
        if (isYearCompare) {
            // 年度对比模式
            if (selectedItems.length > 0) {
                console.log('刷新年度对比多条线图');
                this.updateSelectedTrendYearCompare();
            } else {
                console.log('刷新年度对比默认趋势图');
                this.loadTrendDataYearCompare();
            }
        } else {
            // 常规模式
            if (selectedItems.length > 0) {
                console.log('刷新多条线图');
                this.updateSelectedTrend();
            } else {
                console.log('刷新默认趋势图');
                this.loadTrendData();
            }
        }
    }

    async loadInitialData() {
        try {
            console.log('开始加载初始数据...');
            this.showLoading();
            
            // 只加载表格数据和统计数据，不触碰趋势图
            await Promise.all([
                this.loadTableData(),
                this.loadStatistics()
            ]);
            
            // 只有在没有选中店铺的情况下才显示空图表
            const selectedItems = $('.sale-select:checked');
            if (selectedItems.length === 0) {
                this.renderTrendChart([], []);
            }
            
            this.hideLoading();
            console.log('初始数据加载完成');
        } catch (error) {
            console.error('数据加载失败:', error);
            this.showError('数据加载失败，请刷新页面重试');
            this.hideLoading();
        }
    }

    async loadStatistics() {
        try {
            console.log('加载统计数据...');
            const params = new URLSearchParams();

            // 添加日期范围
            if (this.dateRange) {
                params.append('start_date', this.dateRange.start);
                params.append('end_date', this.dateRange.end);
            }

            const response = await fetch(`/api/sale_statistics?${params}`);
            if (!response.ok) throw new Error('统计数据获取失败');

            const data = await response.json();
            console.log('统计数据:', data);

            if (data.success && data.data) {
                const stats = data.data;
                $('#totalShops').text(this.formatNumber(stats.total_shops || 0));
                $('#totalOrders').text(this.formatNumber(stats.total_orders || 0));
                $('#totalSalesAmount').text(this.formatCurrency(stats.total_sales_amount || 0));
                $('#totalProfit').text(this.formatCurrency(stats.total_sales_profit || 0));
            }

        } catch (error) {
            console.error('统计数据加载失败:', error);
            $('#totalShops').text('-');
            $('#totalOrders').text('-');
            $('#totalSalesAmount').text('-');
            $('#totalProfit').text('-');
        }
    }

    async loadTrendData() {
        try {
            console.log('加载趋势数据...');
            const displayMode = $('#displayMode').val() || 'separate';
            const params = new URLSearchParams({
                granularity: $('#granularity').val() || 'daily',
                display_mode: displayMode,
                multi_line: 'false'  // 默认单条线
            });

            if (this.dateRange) {
                params.append('date_start', this.dateRange.start);
                params.append('date_end', this.dateRange.end);
            }

            // 获取趋势数据
            const trendResponse = await fetch(`/api/sale_trend?${params}`);
            if (!trendResponse.ok) throw new Error('趋势数据获取失败');
            const trendData = await trendResponse.json();
            
            console.log('趋势数据:', trendData);
            
            // 处理趋势数据
            if (trendData.success && trendData.dates && trendData.data) {
                // 将简单数组转换为SPU格式
                const formattedData = [{
                    amounts: Array.isArray(trendData.data) ? trendData.data : [],
                    quantities: [],
                    profits: []
                }];
                this.renderTrendChart(trendData.dates, formattedData);
            } else {
                this.renderTrendChart([], []);
            }
            
        } catch (error) {
            console.error('趋势数据加载失败:', error);
            this.renderTrendChart([], []);
        }
    }

    async updateSelectedTrend() {
        const selectedItems = $('.sale-select:checked');
        const displayMode = $('#displayMode').val();
        
        // 如果是全部店铺汇总模式，直接调用loadTrendData显示所有店铺汇总数据
        if (displayMode === 'total') {
            this.loadTrendData();
            return;
        }
        
        if (selectedItems.length === 0) {
            // 如果没有选择，显示空图表
            this.renderTrendChart([], []);
            return;
        }

        try {
            const selectedSales = [];

            selectedItems.each(function() {
                const checkbox = $(this);
                const row = checkbox.closest('tr');

                // 从复选框的data属性中获取数据
                const shopName = checkbox.data('shop-name') || row.find('td:eq(1)').text();

                // 固定使用店铺维度
                selectedSales.push({
                    shop_name: shopName,
                    date: '',
                    display_name: shopName,
                    dimension: 'shop'
                });
            });

            console.log('更新选择的销售趋势 (店铺维度):', selectedSales);
            console.log('数据展示模式:', displayMode);

            const params = new URLSearchParams({
                granularity: $('#granularity').val() || 'daily',
                selected_sales: JSON.stringify(selectedSales),
                multi_line: displayMode === 'separate' ? 'true' : 'false',  // 根据展示模式决定是否多条线
                dimension: 'shop',  // 固定使用店铺维度
                display_mode: displayMode  // 传递展示模式
            });

            if (this.dateRange) {
                params.append('date_start', this.dateRange.start);
                params.append('date_end', this.dateRange.end);
            }

            const response = await fetch(`/api/sale_trend?${params}`);
            if (!response.ok) throw new Error('选择的销售趋势数据获取失败');
            const data = await response.json();

            console.log('API响应数据:', data);
            console.log('图表日期数组:', data.dates);
            console.log('图表数据数组:', data.data);

            if (data.success) {
                if (displayMode === 'separate') {
                    // 分店铺展示：多条线图
                    this.renderMultiLineTrendChart(data.dates || [], data.data || []);
                } else {
                    // 全部店铺汇总：单条线图
                    this.renderTrendChart(data.dates || [], data.data || []);
                }
            } else {
                console.error('API返回失败状态:', data.message || '未知错误');
                this.renderTrendChart([], []);
            }

        } catch (error) {
            console.error('选择的销售趋势数据加载失败:', error);
            this.renderTrendChart([], []);
        }
    }

    async loadTableData() {
        try {
            console.log('加载表格数据...');
            const params = new URLSearchParams({
                page: this.currentPage,
                page_size: this.pageSize,
                sort_by: $('#sortBy').val() || 'sales_amount',
                sort_order: 'desc'
            });

            const searchTerm = $('#searchInput').val().trim();
            if (searchTerm) {
                params.append('search', searchTerm);
            }

            if (this.dateRange) {
                params.append('start_date', this.dateRange.start);
                params.append('end_date', this.dateRange.end);
            }

            // 固定使用店铺维度
            params.append('dimension', 'shop');

            $('#tableLoading').show();
            const response = await fetch(`/api/sale_data?${params}`);
            if (!response.ok) throw new Error('表格数据获取失败');
            
            const data = await response.json();
            console.log('表格数据:', data);
            
            if (data.success) {
                this.renderTable(data.data || []);
                this.renderPagination(data.pagination || {});
            } else {
                this.renderTable([]);
                this.renderPagination({});
            }
            
            return Promise.resolve(); // 明确返回Promise
            
        } catch (error) {
            console.error('表格数据加载失败:', error);
            this.renderTable([]);
            this.renderPagination({});
            return Promise.reject(error); // 错误时返回rejected Promise
        } finally {
            $('#tableLoading').hide();
        }
    }

    renderTrendChart(dates, spuData) {
        console.log('渲染ECharts趋势图表:', dates, spuData);
        
        // 保存图表数据
        this.currentChartData = { dates, data: spuData };
        
        try {
            const chartDom = document.getElementById('trendChart');
            
            // 销毁现有图表实例
            if (this.charts.trend) {
                this.charts.trend.dispose();
            }

            if (!dates || dates.length === 0 || !spuData || spuData.length === 0) {
                // 显示无数据提示
                chartDom.innerHTML = `
                    <div class="d-flex align-items-center justify-content-center h-100">
                        <div class="text-center">
                            <i class="bi bi-graph-up text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-3 mb-1">暂无趋势数据</p>
                            <small class="text-muted">请选择日期范围或调整筛选条件</small>
                        </div>
                    </div>
                `;
                this.currentChartData = null;
                return;
            }

            // 清空容器内容
            chartDom.innerHTML = '';
            
            // 初始化ECharts实例
            this.charts.trend = echarts.init(chartDom);

            // 聚合所有SPU的数据按日期
            const aggregatedData = {};
            
            spuData.forEach(spu => {
                dates.forEach((date, index) => {
                    if (!aggregatedData[date]) {
                        aggregatedData[date] = { amount: 0, quantity: 0, profit: 0 };
                    }
                    aggregatedData[date].amount += spu.amounts[index] || 0;
                    aggregatedData[date].quantity += spu.quantities[index] || 0;
                    aggregatedData[date].profit += spu.profits ? (spu.profits[index] || 0) : 0;
                });
            });

            const salesData = dates.map(date => aggregatedData[date].amount);

            // 格式化日期标签
            const formattedLabels = this.formatDateLabels(dates);

            // 根据当前图表类型配置系列
            const seriesConfig = {
                                    name: '净销售额',
                data: salesData,
                emphasis: {
                    focus: 'series'
                }
            };

            // 根据图表类型设置不同的配置
            if (this.currentChartType === 'bar') {
                seriesConfig.type = 'bar';
                seriesConfig.itemStyle = {
                    color: '#4f46e5'
                };
                seriesConfig.emphasis = {
                    focus: 'series'
                };
            } else if (this.currentChartType === 'area') {
                seriesConfig.type = 'line';
                seriesConfig.smooth = true;
                seriesConfig.symbol = 'circle';
                seriesConfig.symbolSize = 4;
                seriesConfig.lineStyle = {
                    width: 3,
                    color: '#4f46e5'
                };
                seriesConfig.itemStyle = {
                    color: '#4f46e5'
                };
                seriesConfig.areaStyle = {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(79, 70, 229, 0.3)' },
                        { offset: 1, color: 'rgba(79, 70, 229, 0.05)' }
                    ])
                };
            } else { // line - 使用年度对比样式
                seriesConfig.type = 'line';
                seriesConfig.smooth = true;
                seriesConfig.symbol = 'circle';
                seriesConfig.symbolSize = 4;
                seriesConfig.lineStyle = {
                    width: 3,
                    color: '#4f46e5'
                };
                seriesConfig.itemStyle = {
                    color: '#4f46e5'
                };
                seriesConfig.areaStyle = {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(79, 70, 229, 0.3)' },
                        { offset: 1, color: 'rgba(79, 70, 229, 0.05)' }
                    ])
                };
                seriesConfig.emphasis = {
                    focus: 'series'
                };
            }

            // ECharts配置 - 使用年度对比样式
            const option = {
                title: {
                    text: '销售趋势分析',
                    left: 'center',
                    textStyle: {
                        color: '#374151',
                        fontSize: 16,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    borderColor: 'rgba(79, 70, 229, 0.3)',
                    borderWidth: 1,
                    textStyle: {
                        color: '#ffffff'
                    },
                    formatter: function(params) {
                        let result = params[0].name + '<br/>';
                        params.forEach(param => {
                            result += `<span style="color:${param.color}">●</span> ${param.seriesName}: ¥${param.value.toLocaleString()}<br/>`;
                        });
                        return result;
                    }
                },
                legend: {
                    data: ['净销售额'],
                    top: 40,
                    textStyle: {
                        fontSize: 12,
                        color: '#374151'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: formattedLabels,
                    axisLine: {
                        lineStyle: {
                            color: '#e5e7eb'
                        }
                    },
                    axisLabel: {
                        color: '#6b7280',
                        fontSize: 12
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '净销售额 (¥)',
                    nameTextStyle: {
                        color: '#374151',
                        fontSize: 14,
                        fontWeight: 'bold'
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#e5e7eb'
                        }
                    },
                    axisLabel: {
                        color: '#6b7280',
                        fontSize: 12,
                        formatter: function(value) {
                            return '¥' + value.toLocaleString();
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    }
                },
                series: [seriesConfig]
            };

            // 设置配置项并渲染图表
            this.charts.trend.setOption(option);
            
            // 监听窗口大小变化
            this.setupResizeListener();
            
        } catch (error) {
            console.error('ECharts图表渲染失败:', error);
            const chartDom = document.getElementById('trendChart');
            chartDom.innerHTML = `
                <div class="d-flex align-items-center justify-content-center h-100">
                    <div class="text-center">
                        <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3 mb-1">图表加载失败</p>
                        <small class="text-muted">请刷新页面重试</small>
                    </div>
                </div>
            `;
        }
    }

    renderMultiLineTrendChart(dates, spuData) {
        console.log('渲染ECharts多条线趋势图表:', dates, spuData);
        
        // 保存图表数据
        this.currentChartData = { dates, data: spuData };
        
        try {
            const chartDom = document.getElementById('trendChart');
            
            // 销毁现有图表实例
            if (this.charts.trend) {
                this.charts.trend.dispose();
            }

            if (!dates || dates.length === 0 || !spuData || spuData.length === 0) {
                // 显示无数据提示
                chartDom.innerHTML = `
                    <div class="d-flex align-items-center justify-content-center h-100">
                        <div class="text-center">
                            <i class="bi bi-graph-up text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-3 mb-1">请选择SPU查看趋势</p>
                            <small class="text-muted">在下方数据表格中勾选要查看的商品</small>
                        </div>
                    </div>
                `;
                this.currentChartData = null;
                return;
            }

            // 清空容器内容
            chartDom.innerHTML = '';
            
            // 初始化ECharts实例
            this.charts.trend = echarts.init(chartDom);

            // 格式化日期标签
            const formattedLabels = dates.map(date => {
                const momentDate = moment(date);
                return momentDate.format('MM/DD');
            });

            // 颜色配置
            const colors = [
                '#4f46e5', '#06b6d4', '#10b981', '#f59e0b', '#ef4444', 
                '#8b5cf6', '#22c55e', '#f97316', '#6366f1', '#14b8a6'
            ];

            // 为每个店铺创建系列
            const series = spuData.map((spu, index) => {
                const color = colors[index % colors.length];
                
                // 固定使用店铺名称作为显示名称
                const displayName = spu.shop_name || spu.display_name || `店铺${index + 1}`;
                
                const seriesConfig = {
                    name: displayName,
                    data: spu.amounts || [],
                    emphasis: {
                        focus: 'series'
                    }
                };

                // 根据图表类型设置不同的配置 - 使用年度对比样式
                if (this.currentChartType === 'bar') {
                    seriesConfig.type = 'bar';
                    seriesConfig.itemStyle = {
                        color: color
                    };
                    seriesConfig.emphasis = {
                        focus: 'series'
                    };
                } else if (this.currentChartType === 'area') {
                    seriesConfig.type = 'line';
                    seriesConfig.smooth = true;
                    seriesConfig.symbol = 'circle';
                    seriesConfig.symbolSize = 4;
                    seriesConfig.lineStyle = {
                        width: 3,
                        color: color
                    };
                    seriesConfig.itemStyle = {
                        color: color
                    };
                    seriesConfig.areaStyle = {
                        color: color + '30'
                    };
                    seriesConfig.emphasis = {
                        focus: 'series'
                    };
                } else { // line
                    seriesConfig.type = 'line';
                    seriesConfig.smooth = true;
                    seriesConfig.symbol = 'circle';
                    seriesConfig.symbolSize = 4;
                    seriesConfig.lineStyle = {
                        width: 3,
                        color: color
                    };
                    seriesConfig.itemStyle = {
                        color: color
                    };
                    seriesConfig.areaStyle = {
                        color: color + '30'
                    };
                    seriesConfig.emphasis = {
                        focus: 'series'
                    };
                }
                
                return seriesConfig;
            });

            // ECharts配置 - 使用年度对比样式
            const option = {
                title: {
                    text: '多净销售数据趋势对比',
                    left: 'center',
                    textStyle: {
                        color: '#374151',
                        fontSize: 16,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    borderColor: 'rgba(79, 70, 229, 0.3)',
                    borderWidth: 1,
                    textStyle: {
                        color: '#ffffff'
                    },
                    formatter: function(params) {
                        let result = params[0].name + '<br/>';
                        params.forEach(param => {
                            result += `<span style="color:${param.color}">●</span> ${param.seriesName}: ¥${param.value.toLocaleString()}<br/>`;
                        });
                        return result;
                    }
                },
                legend: {
                    type: 'scroll',
                    orient: 'horizontal',
                    left: 'center',
                    top: 40,
                    pageIconColor: '#374151',
                    pageIconInactiveColor: '#cbd5e1',
                    pageTextStyle: {
                        color: '#374151'
                    },
                    textStyle: {
                        fontSize: 11,
                        color: '#374151'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '20%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: formattedLabels,
                    axisLine: {
                        lineStyle: {
                            color: '#e5e7eb'
                        }
                    },
                    axisLabel: {
                        color: '#6b7280',
                        fontSize: 12
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '净销售额 (¥)',
                    nameTextStyle: {
                        color: '#374151',
                        fontSize: 14,
                        fontWeight: 'bold'
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#e5e7eb'
                        }
                    },
                    axisLabel: {
                        color: '#6b7280',
                        fontSize: 12,
                        formatter: function(value) {
                            return '¥' + value.toLocaleString();
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    }
                },
                series: series
            };

            this.charts.trend.setOption(option);
            this.setupResizeListener();
            
        } catch (error) {
            console.error('ECharts多条线图表渲染失败:', error);
            const chartDom = document.getElementById('trendChart');
            chartDom.innerHTML = `
                <div class="d-flex align-items-center justify-content-center h-100">
                    <div class="text-center">
                        <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3 mb-1">图表加载失败</p>
                        <small class="text-muted">请刷新页面重试</small>
                    </div>
                </div>
            `;
        }
    }

    renderTable(data) {
        const tbody = $('#salesTableBody');
        tbody.empty();

        if (!data.length) {
            tbody.append(`
                <tr>
                    <td colspan="7" class="text-center text-muted py-4">
                        <i class="bi bi-inbox fs-1"></i>
                        <p class="mt-2">暂无数据</p>
                    </td>
                </tr>
            `);
            return;
        }

        data.forEach((item, index) => {
            // 计算平均订单价值
            const avgOrderValue = (item.actual_orders && item.actual_orders > 0) ? 
                (item.sales_amount / item.actual_orders) : 0;
            
            const row = `
                <tr>
                    <td class="text-center">
                        <input type="checkbox" class="form-check-input sale-select"
                               data-shop-name="${item.shop_name || ''}"
                               ${index < 5 ? 'checked' : ''}>
                    </td>
                    <td>
                        <div class="fw-bold text-dark">${item.shop_name || '未知店铺'}</div>
                        <small class="text-muted">
                            <i class="bi bi-shop me-1"></i>编码: ${item.shop_code || '-'}
                        </small>
                    </td>
                    <td class="text-end">
                        <span class="badge bg-primary rounded-pill">
                            ${this.formatNumber(item.sales_orders || 0)}
                        </span>
                    </td>
                    <td class="text-end">
                        <span class="badge bg-success rounded-pill">
                            ${this.formatNumber(item.actual_orders || 0)}
                        </span>
                    </td>
                    <td class="text-end">
                        <span class="fw-semibold text-secondary">
                            ${this.formatNumber(item.sales_quantity || 0)}
                        </span>
                    </td>
                    <td class="text-end">
                        <div class="fw-bold text-success fs-6">
                            ${this.formatCurrency(item.sales_amount || 0)}
                        </div>
                    </td>
                    <td class="text-end">
                        <span class="text-info fw-semibold">
                            ${this.formatCurrency(avgOrderValue)}
                        </span>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });

        // 渲染完成后触发默认选择的趋势更新
        setTimeout(() => {
            const selectedItems = $('.sale-select:checked');
            if (selectedItems.length > 0) {
                // 根据年度对比状态调用相应的方法
                const isYearCompare = $('#yearCompareSwitch').is(':checked');
                if (isYearCompare) {
                    this.updateSelectedTrendYearCompare();
                } else {
                    this.updateSelectedTrend();
                }
            }
        }, 100);
    }

    renderPagination(pagination) {
        const paginationEl = $('#pagination');
        paginationEl.empty();

        if (!pagination.total_pages || pagination.total_pages <= 1) {
            return;
        }

        this.totalPages = pagination.total_pages;
        const currentPage = pagination.page || 1;

        // 上一页
        const prevDisabled = currentPage <= 1 ? 'disabled' : '';
        paginationEl.append(`
            <li class="page-item ${prevDisabled}">
                <a class="page-link" href="#" onclick="saleAnalytics.goToPage(${currentPage - 1})">
                    <i class="bi bi-chevron-left"></i>
                </a>
            </li>
        `);

        // 页码
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(this.totalPages, currentPage + 2);

        if (startPage > 1) {
            paginationEl.append(`
                <li class="page-item">
                    <a class="page-link" href="#" onclick="saleAnalytics.goToPage(1)">1</a>
                </li>
            `);
            if (startPage > 2) {
                paginationEl.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const active = i === currentPage ? 'active' : '';
            paginationEl.append(`
                <li class="page-item ${active}">
                    <a class="page-link" href="#" onclick="saleAnalytics.goToPage(${i})">${i}</a>
                </li>
            `);
        }

        if (endPage < this.totalPages) {
            if (endPage < this.totalPages - 1) {
                paginationEl.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
            }
            paginationEl.append(`
                <li class="page-item">
                    <a class="page-link" href="#" onclick="saleAnalytics.goToPage(${this.totalPages})">${this.totalPages}</a>
                </li>
            `);
        }

        // 下一页
        const nextDisabled = currentPage >= this.totalPages ? 'disabled' : '';
        paginationEl.append(`
            <li class="page-item ${nextDisabled}">
                <a class="page-link" href="#" onclick="saleAnalytics.goToPage(${currentPage + 1})">
                    <i class="bi bi-chevron-right"></i>
                </a>
            </li>
        `);
    }

    goToPage(page) {
        if (page < 1 || page > this.totalPages || page === this.currentPage) return;
        this.currentPage = page;
        this.loadTableData();
    }

    updateChartType(chartType) {
        console.log('切换图表类型到:', chartType);
        this.currentChartType = chartType;
        
        // 如果有当前图表数据，重新渲染
        if (this.currentChartData) {
            const isYearCompare = $('#yearCompareSwitch').is(':checked');
            const selectedItems = $('.sale-select:checked');
            
            if (isYearCompare) {
                if (selectedItems.length > 0) {
                    // 年度对比多条线图
                    this.renderMultiLineYearCompareTrendChart(
                        this.currentChartData.dates, 
                        this.currentChartData.data,
                        this.currentChartData.year1,
                        this.currentChartData.year2
                    );
                } else {
                    // 年度对比单图
                    this.renderYearCompareTrendChart(
                        this.currentChartData.dates, 
                        this.currentChartData.data,
                        this.currentChartData.year1,
                        this.currentChartData.year2
                    );
                }
            } else {
                if (selectedItems.length > 0) {
                    // 常规多条线图
                    this.renderMultiLineTrendChart(this.currentChartData.dates, this.currentChartData.data);
                } else {
                    // 常规单图
                    this.renderTrendChart(this.currentChartData.dates, this.currentChartData.data);
                }
            }
        }
    }



    showLoading() {
        // 可以添加全局加载指示器
        console.log('显示加载状态');
    }

    hideLoading() {
        // 隐藏全局加载指示器
        console.log('隐藏加载状态');
    }

    showError(message) {
        console.error(message);
        alert(message);  // 临时使用alert，可以替换为更美观的提示
    }

    formatNumber(num) {
        return new Intl.NumberFormat('zh-CN').format(num || 0);
    }

    formatCurrency(amount) {
        return '¥' + new Intl.NumberFormat('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount || 0);
    }

    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 格式化日期标签（根据时间粒度）
    formatDateLabels(dates) {
        const granularity = $('#granularity').val() || 'daily';
        const isYearCompare = $('#yearCompareSwitch').is(':checked');
        
        return dates.map(date => {
            if (granularity === 'weekly') {
                if (date.includes('~')) {
                    // 常规模式的周范围格式：2025-01-01~2025-01-07
                    const [start, end] = date.split('~');
                    return `${moment(start).format('MM/DD')}-${moment(end).format('MM/DD')}`;
                } else if (isYearCompare) {
                    // 年度对比模式的周格式：01, 02, 03...
                    const weekNum = parseInt(date);
                    return `第${weekNum}周`;
                }
                return date;
            } else if (granularity === 'monthly') {
                if (date.includes('-') && date.length === 7) {
                    // 常规模式的月格式：2024-01
                    return moment(date + '-01').format('YYYY年MM月');
                } else if (isYearCompare) {
                    // 年度对比模式的月格式：01, 02, 03...
                    const monthNum = parseInt(date);
                    return `${monthNum}月`;
                }
                return moment(date).format('YYYY年MM月');
            } else {
                if (date.includes('-') && date.length === 5) {
                    // 年度对比的日格式：01-15（月-日）
                    return date.replace('-', '/');
                } else if (isYearCompare && date.length <= 2) {
                    // 年度对比模式可能返回的简化格式
                    return date;
                }
                const momentDate = moment(date);
                return momentDate.isValid() ? momentDate.format('MM/DD') : date;
            }
        });
    }

    // 年度对比相关方法
    async loadTrendDataYearCompare() {
        try {
            console.log('加载年度对比趋势数据...');
            const year1 = $('#compareYear1').val();
            const year2 = $('#compareYear2').val();
            
            if (year1 === year2) {
                this.renderTrendChart([], []);
                return;
            }

            // 获取筛选面板的日期范围
            let dateStart, dateEnd;
            if (this.dateRange) {
                dateStart = this.dateRange.start;
                dateEnd = this.dateRange.end;
            } else {
                // 年度对比模式下，需要覆盖两个对比年份的完整范围
                const minYear = Math.min(parseInt(year1), parseInt(year2));
                const maxYear = Math.max(parseInt(year1), parseInt(year2));
                
                // 使用较早年份的1月1日作为开始日期
                dateStart = `${minYear}-01-01`;
                
                // 使用较晚年份的12月31日作为结束日期，如果是当前年份则使用昨天
                const currentYear = moment().year();
                if (maxYear >= currentYear) {
                    const yesterday = moment().subtract(1, 'day');
                    dateEnd = yesterday.format('YYYY-MM-DD');
                } else {
                    dateEnd = `${maxYear}-12-31`;
                }
            }

            console.log('年度对比使用日期范围:', dateStart, '到', dateEnd);

            const displayMode = $('#displayMode').val() || 'separate';
            const params = new URLSearchParams({
                granularity: $('#granularity').val() || 'daily',
                year_compare: 'true',
                year1: year1,
                year2: year2,
                date_start: dateStart,
                date_end: dateEnd,
                display_mode: displayMode,
                multi_line: 'false'
            });

            // 修复：使用正确的销售数据API而不是SPU API
            const trendResponse = await fetch(`/api/sale_trend?${params}`);
            if (!trendResponse.ok) throw new Error('年度对比趋势数据获取失败');
            const trendData = await trendResponse.json();
            
            console.log('年度对比趋势数据:', trendData);
            
            // 处理趋势数据
            if (trendData.success) {
                this.renderYearCompareTrendChart(trendData.dates || [], trendData.data || [], year1, year2);
            } else {
                this.renderTrendChart([], []);
            }
            
        } catch (error) {
            console.error('年度对比趋势数据加载失败:', error);
            this.renderTrendChart([], []);
        }
    }

    async updateSelectedTrendYearCompare() {
        const selectedItems = $('.sale-select:checked');
        const displayMode = $('#displayMode').val();
        
        // 如果是全部店铺汇总模式，直接调用年度对比的全部店铺数据
        if (displayMode === 'total') {
            this.loadTrendDataYearCompare();
            return;
        }
        
        if (selectedItems.length === 0) {
            this.renderTrendChart([], []);
            return;
        }

        try {
            const year1 = $('#compareYear1').val();
            const year2 = $('#compareYear2').val();
            
            if (year1 === year2) {
                this.renderTrendChart([], []);
                return;
            }

            const selectedSales = [];
            
            selectedItems.each(function() {
                const checkbox = $(this);
                const row = checkbox.closest('tr');
                
                const shopName = checkbox.data('shop-name') || row.find('td:eq(1)').text();
                
                // 固定使用店铺维度
                selectedSales.push({ 
                    shop_name: shopName, 
                    date: '',
                    display_name: shopName,
                    dimension: 'shop'
                });
            });

            console.log('更新选择的销售年度对比趋势 (店铺维度):', selectedSales);
            console.log('数据展示模式:', displayMode);
            
            // 获取筛选面板的日期范围
            let dateStart, dateEnd;
            if (this.dateRange) {
                dateStart = this.dateRange.start;
                dateEnd = this.dateRange.end;
            } else {
                // 年度对比模式下，需要覆盖两个对比年份的完整范围
                const minYear = Math.min(parseInt(year1), parseInt(year2));
                const maxYear = Math.max(parseInt(year1), parseInt(year2));
                
                // 使用较早年份的1月1日作为开始日期
                dateStart = `${minYear}-01-01`;
                
                // 使用较晚年份的12月31日作为结束日期，如果是当前年份则使用昨天
                const currentYear = moment().year();
                if (maxYear >= currentYear) {
                    const yesterday = moment().subtract(1, 'day');
                    dateEnd = yesterday.format('YYYY-MM-DD');
                } else {
                    dateEnd = `${maxYear}-12-31`;
                }
            }

            console.log('年度对比多选使用日期范围:', dateStart, '到', dateEnd);
            
            const params = new URLSearchParams({
                granularity: $('#granularity').val() || 'daily',
                selected_sales: JSON.stringify(selectedSales),
                multi_line: displayMode === 'separate' ? 'true' : 'false',
                dimension: 'shop',
                year_compare: 'true',
                year1: year1,
                year2: year2,
                date_start: dateStart,
                date_end: dateEnd,
                display_mode: displayMode
            });

            // 修复：使用正确的销售数据API
            const response = await fetch(`/api/sale_trend?${params}`);
            if (!response.ok) throw new Error('选择的销售年度对比趋势数据获取失败');
            const data = await response.json();
            
            console.log('年度对比API响应数据:', data);
            
            if (data.success) {
                if (displayMode === 'separate') {
                    // 分店铺展示：多条线年度对比图
                    this.renderMultiLineYearCompareTrendChart(data.dates || [], data.data || [], year1, year2);
                } else {
                    // 全部店铺汇总：单条线年度对比图
                    this.renderYearCompareTrendChart(data.dates || [], data.data || [], year1, year2);
                }
            } else {
                console.error('年度对比API返回失败状态:', data.message || '未知错误');
                this.renderTrendChart([], []);
            }
            
        } catch (error) {
            console.error('选择的销售年度对比趋势数据加载失败:', error);
            this.renderTrendChart([], []);
        }
    }

    renderYearCompareTrendChart(dates, spuData, year1, year2) {
        console.log('渲染年度对比趋势图表:', dates, spuData, year1, year2);
        
        // 保存图表数据
        this.currentChartData = { dates, data: spuData, year1, year2 };
        
        try {
            const chartDom = document.getElementById('trendChart');
            
            if (this.charts.trend) {
                this.charts.trend.dispose();
            }

            if (!dates || dates.length === 0 || !spuData || spuData.length < 2) {
                chartDom.innerHTML = `
                    <div class="d-flex align-items-center justify-content-center h-100">
                        <div class="text-center">
                            <i class="bi bi-graph-up text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-3 mb-1">暂无年度对比数据</p>
                            <small class="text-muted">请选择不同的年份进行对比</small>
                        </div>
                    </div>
                `;
                return;
            }

            chartDom.innerHTML = '';
            this.charts.trend = echarts.init(chartDom);

            // 格式化日期标签
            const formattedLabels = dates.map(date => {
                const momentDate = moment(date);
                return momentDate.format('MM/DD');
            });

            // 准备数据 - 确定哪个是今年（较晚年份）和去年（较早年份）
            const thisYear = Math.max(parseInt(year1), parseInt(year2));
            const lastYear = Math.min(parseInt(year1), parseInt(year2));
            
            const thisYearData = spuData.find(item => item.year === thisYear.toString());
            const lastYearData = spuData.find(item => item.year === lastYear.toString());

            const thisYearSales = thisYearData ? thisYearData.amounts : [];
            const lastYearSales = lastYearData ? lastYearData.amounts : [];

            // ECharts配置
            const option = {
                title: {
                    text: `${lastYear}年 vs ${thisYear}年 净销售对比`,
                    left: 'center',
                    textStyle: {
                        color: '#374151',
                        fontSize: 16,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    borderColor: 'rgba(79, 70, 229, 0.3)',
                    borderWidth: 1,
                    textStyle: {
                        color: '#ffffff'
                    },
                    formatter: function(params) {
                        let result = params[0].name + '<br/>';
                        params.forEach(param => {
                            result += `<span style="color:${param.color}">●</span> ${param.seriesName}: ¥${param.value.toLocaleString()}<br/>`;
                        });
                        return result;
                    }
                },
                legend: {
                    data: [`${lastYear}年(去年)`, `${thisYear}年(今年)`],
                    top: 40,
                    textStyle: {
                        fontSize: 12,
                        color: '#374151'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: formattedLabels,
                    axisLine: {
                        lineStyle: {
                            color: '#e5e7eb'
                        }
                    },
                    axisLabel: {
                        color: '#6b7280',
                        fontSize: 12
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '净销售额 (¥)',
                    nameTextStyle: {
                        color: '#374151',
                        fontSize: 14,
                        fontWeight: 'bold'
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#e5e7eb'
                        }
                    },
                    axisLabel: {
                        color: '#6b7280',
                        fontSize: 12,
                        formatter: function(value) {
                            return '¥' + value.toLocaleString();
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    }
                },
                series: [
                    {
                        name: `${lastYear}年(去年)`,
                        type: 'line',
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 4,
                        lineStyle: {
                            width: 3,
                            color: '#94a3b8',
                            type: 'dashed' // 去年使用虚线
                        },
                        itemStyle: {
                            color: '#94a3b8'
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: 'rgba(148, 163, 184, 0.2)' },
                                { offset: 1, color: 'rgba(148, 163, 184, 0.02)' }
                            ])
                        },
                        data: lastYearSales,
                        emphasis: {
                            focus: 'series'
                        }
                    },
                    {
                        name: `${thisYear}年(今年)`,
                        type: 'line',
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 4,
                        lineStyle: {
                            width: 3,
                            color: '#4f46e5'
                            // 今年使用实线（默认）
                        },
                        itemStyle: {
                            color: '#4f46e5'
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: 'rgba(79, 70, 229, 0.3)' },
                                { offset: 1, color: 'rgba(79, 70, 229, 0.05)' }
                            ])
                        },
                        data: thisYearSales,
                        emphasis: {
                            focus: 'series'
                        }
                    }
                ]
            };

            this.charts.trend.setOption(option);
            this.setupResizeListener();
            
        } catch (error) {
            console.error('年度对比图表渲染失败:', error);
            const chartDom = document.getElementById('trendChart');
            chartDom.innerHTML = `
                <div class="d-flex align-items-center justify-content-center h-100">
                    <div class="text-center">
                        <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3 mb-1">年度对比图表加载失败</p>
                        <small class="text-muted">请刷新页面重试</small>
                    </div>
                </div>
            `;
        }
    }

    renderMultiLineYearCompareTrendChart(dates, spuData, year1, year2) {
        console.log('渲染多条线年度对比趋势图表:', dates, spuData, year1, year2);
        
        // 保存图表数据
        this.currentChartData = { dates, data: spuData, year1, year2 };
        
        try {
            const chartDom = document.getElementById('trendChart');
            
            if (this.charts.trend) {
                this.charts.trend.dispose();
            }

            if (!dates || dates.length === 0 || !spuData || spuData.length === 0) {
                chartDom.innerHTML = `
                    <div class="d-flex align-items-center justify-content-center h-100">
                        <div class="text-center">
                            <i class="bi bi-graph-up text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-3 mb-1">请选择SPU查看年度对比</p>
                            <small class="text-muted">在下方数据表格中勾选要查看的商品</small>
                        </div>
                    </div>
                `;
                return;
            }

            chartDom.innerHTML = '';
            this.charts.trend = echarts.init(chartDom);

            // 格式化日期标签
            const formattedLabels = dates.map(date => {
                const momentDate = moment(date);
                return momentDate.format('MM/DD');
            });

            // 颜色配置
            const colors = ['#4f46e5', '#06b6d4', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];
            const series = [];
            const legendData = [];

            // 确定哪个是今年（较晚年份）和去年（较早年份）
            const thisYear = Math.max(parseInt(year1), parseInt(year2)).toString();
            const lastYear = Math.min(parseInt(year1), parseInt(year2)).toString();
            
            // 为每个店铺创建两条线（两个年份）
            spuData.forEach((spu, index) => {
                // 固定使用店铺名称作为显示名称
                const displayName = spu.shop_name || spu.display_name || `店铺${index + 1}`;

                const baseColor = colors[index % colors.length];
                
                // 先添加去年数据（虚线）
                if (spu.year_data && spu.year_data[lastYear]) {
                    const seriesName = `${displayName} (${lastYear}年)`;
                    legendData.push(seriesName);
                    series.push({
                        name: seriesName,
                        type: 'line',
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 4,
                        lineStyle: {
                            width: 2,
                            color: baseColor,
                            type: 'dashed' // 去年使用虚线
                        },
                        itemStyle: {
                            color: baseColor
                        },
                        data: spu.year_data[lastYear].amounts || [],
                        emphasis: {
                            focus: 'series'
                        }
                    });
                }

                // 再添加今年数据（实线）
                if (spu.year_data && spu.year_data[thisYear]) {
                    const seriesName = `${displayName} (${thisYear}年)`;
                    legendData.push(seriesName);
                    series.push({
                        name: seriesName,
                        type: 'line',
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 4,
                        lineStyle: {
                            width: 2,
                            color: baseColor
                            // 今年使用实线（默认）
                        },
                        itemStyle: {
                            color: baseColor
                        },
                        data: spu.year_data[thisYear].amounts || [],
                        emphasis: {
                            focus: 'series'
                        }
                    });
                }
            });

            // ECharts配置
            const option = {
                backgroundColor: 'transparent',
                title: {
                    text: `SPU年度对比 (${lastYear}年 vs ${thisYear}年)`,
                    left: 'center',
                    textStyle: {
                        color: '#374151',
                        fontSize: 16,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    borderColor: 'rgba(79, 70, 229, 0.3)',
                    borderWidth: 1,
                    textStyle: {
                        color: '#ffffff'
                    },
                    formatter: function(params) {
                        let result = params[0].name + '<br/>';
                        params.forEach(param => {
                            result += `<span style="color:${param.color}">●</span> ${param.seriesName}: ¥${param.value.toLocaleString()}<br/>`;
                        });
                        return result;
                    }
                },
                legend: {
                    data: legendData,
                    top: 40,
                    type: 'scroll',
                    pageIconColor: '#374151',
                    pageIconInactiveColor: '#cbd5e1',
                    pageTextStyle: {
                        color: '#374151'
                    },
                    textStyle: {
                        fontSize: 11,
                        color: '#374151'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '20%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: formattedLabels,
                    axisLine: {
                        lineStyle: {
                            color: '#e5e7eb'
                        }
                    },
                    axisLabel: {
                        color: '#6b7280',
                        fontSize: 12
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '净销售额 (¥)',
                    nameTextStyle: {
                        color: '#374151',
                        fontSize: 14,
                        fontWeight: 'bold'
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#e5e7eb'
                        }
                    },
                    axisLabel: {
                        color: '#6b7280',
                        fontSize: 12,
                        formatter: function(value) {
                            return '¥' + value.toLocaleString();
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    }
                },
                series: series
            };

            this.charts.trend.setOption(option);
            this.setupResizeListener();
            
        } catch (error) {
            console.error('多条线年度对比图表渲染失败:', error);
            const chartDom = document.getElementById('trendChart');
            chartDom.innerHTML = `
                <div class="d-flex align-items-center justify-content-center h-100">
                    <div class="text-center">
                        <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3 mb-1">年度对比图表加载失败</p>
                        <small class="text-muted">请刷新页面重试</small>
                    </div>
                </div>
            `;
        }
    }

    // 年度对比UI提示方法
    showYearCompareHint() {
        // 如果提示已存在，先移除
        this.hideYearCompareHint();
        
        const year1 = $('#compareYear1').val();
        const year2 = $('#compareYear2').val();
        const thisYear = Math.max(parseInt(year1), parseInt(year2));
        const lastYear = Math.min(parseInt(year1), parseInt(year2));
        
        // 获取当前设置的日期范围
        let dateRangeText = '';
        if (this.dateRange) {
            dateRangeText = `日期范围：${this.dateRange.start} 到 ${this.dateRange.end}`;
        } else {
            dateRangeText = '日期范围：使用筛选面板设置的范围';
        }
        
        const hintHtml = `
            <div class="year-compare-hint" id="yearCompareHint">
                <i class="bi bi-lightbulb"></i>
                <strong>年度对比模式已启用</strong> - 正在对比 <strong>${lastYear}年</strong> 与 <strong>${thisYear}年</strong> 的数据
                <br>
                <small>
                    • <strong>实线</strong>表示${thisYear}年（今年）数据，<strong>虚线</strong>表示${lastYear}年（去年）数据
                    <br>
                    • ${dateRangeText}
                    <br>
                    • 请在下方SPU数据详情表格中勾选商品来查看具体的年度对比趋势
                    <br>
                    • 支持切换不同图表类型：折线图、柱状图、面积图
                </small>
            </div>
        `;
        
        // 在图表卡片内容前插入提示
        $('.chart-card .card-body').prepend(hintHtml);
    }
    
    hideYearCompareHint() {
        $('#yearCompareHint').remove();
    }

    setupResizeListener() {
        // 移除之前的监听器
        if (this.resizeHandler) {
            window.removeEventListener('resize', this.resizeHandler);
        }
        
        // 创建新的监听器
        this.resizeHandler = () => {
            if (this.charts.trend) {
                this.charts.trend.resize();
            }
        };
        
        // 添加监听器
        window.addEventListener('resize', this.resizeHandler);
    }
}

// 全局函数
window.setQuickDate = function(period) {
    const today = moment();
    const yesterday = moment().subtract(1, 'day');
    let start, end;
    
    switch(period) {
        case '2025':
            start = moment('2025-01-01');
            end = yesterday;
            break;
        case '2024':
            start = moment('2024-01-01');
            end = moment('2024-12-31');
            break;
        case '2023':
            start = moment('2023-01-01');
            end = moment('2023-12-31');
            break;
        case 'last30':
            start = moment().subtract(30, 'days');
            end = yesterday;
            break;
        case 'last90':
            start = moment().subtract(90, 'days');
            end = yesterday;
            break;
        default:
            return;
    }
    
    $('#dateRange').val(start.format('YYYY-MM-DD') + ' - ' + end.format('YYYY-MM-DD'));
    window.saleAnalytics.dateRange = {
        start: start.format('YYYY-MM-DD'),
        end: end.format('YYYY-MM-DD')
    };
    
    // 自动应用新的日期范围
    if (window.saleAnalytics) {
        window.saleAnalytics.currentPage = 1;
        // 重新加载数据但保持图表状态
        Promise.all([
            window.saleAnalytics.loadTableData(),
            window.saleAnalytics.loadStatistics()
        ]).then(() => {
            // 如果有选中的销售数据，重新渲染多线图
            const selectedItems = $('.sale-select:checked');
            if (selectedItems.length > 0) {
                // 根据年度对比状态调用相应的方法
                const isYearCompare = $('#yearCompareSwitch').is(':checked');
                if (isYearCompare) {
                    window.saleAnalytics.updateSelectedTrendYearCompare();
                } else {
                    window.saleAnalytics.updateSelectedTrend();
                }
            }
        });
    }
};

window.applyFilters = function() {
    window.saleAnalytics.currentPage = 1;
    // 重新加载表格数据和统计数据，保持现有的图表状态
    Promise.all([
        window.saleAnalytics.loadTableData(),
        window.saleAnalytics.loadStatistics()
    ]).then(() => {
        // 如果有选中的销售数据，重新渲染多线图
        const selectedItems = $('.sale-select:checked');
        if (selectedItems.length > 0) {
            // 根据年度对比状态调用相应的方法
            const isYearCompare = $('#yearCompareSwitch').is(':checked');
            if (isYearCompare) {
                window.saleAnalytics.updateSelectedTrendYearCompare();
            } else {
                window.saleAnalytics.updateSelectedTrend();
            }
        }
    });
};

window.resetFilters = function() {
    $('#dateRange').val('');
    $('#granularity').val('daily');
    $('#displayMode').val('separate');
    $('#sortBy').val('sales_amount');
    $('#searchInput').val('');
    
    window.saleAnalytics.dateRange = null;
    window.saleAnalytics.currentPage = 1;
    window.saleAnalytics.setDefaultDateRange();
    
    // 重新加载数据但保持图表状态
    Promise.all([
        window.saleAnalytics.loadTableData(),
        window.saleAnalytics.loadStatistics()
    ]).then(() => {
        // 清除所有选中状态
        $('.sale-select').prop('checked', false);
        $('#selectAll').prop('checked', false);
        
        // 根据年度对比状态显示相应的空图表
        const isYearCompare = $('#yearCompareSwitch').is(':checked');
        if (isYearCompare) {
            // 年度对比模式下的空图表
            window.saleAnalytics.loadTrendDataYearCompare();
        } else {
            // 常规模式下的空图表
            window.saleAnalytics.renderTrendChart([], []);
        }
    });
};

window.exportData = function() {
    // 导出功能实现
    const params = new URLSearchParams({
        format: 'excel',
        sort_by: $('#sortBy').val() || 'sales_amount',
        sort_order: 'desc'
    });

    const searchTerm = $('#searchInput').val().trim();
    if (searchTerm) {
        params.append('search', searchTerm);
    }

    if (window.saleAnalytics.dateRange) {
        params.append('start_date', window.saleAnalytics.dateRange.start);
        params.append('end_date', window.saleAnalytics.dateRange.end);
    }
    
    window.open(`/api/sale_data?${params}&export=true`, '_blank');
};

// 生成销售数据分析页面的函数
function generateSaleAnalytics() {
    const container = document.querySelector('.container');
    
    // 清空容器
    container.innerHTML = '';
    
    // 创建销售数据分析页面的HTML内容
    const saleAnalyticsHTML = `
        <div class="header">
            <h1>销售数据分析</h1>
        </div>
        
        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <div class="stat-value" id="totalShops">-</div>
                        <div class="stat-label">总店铺数</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <div class="stat-value" id="totalOrders">-</div>
                        <div class="stat-label">总订单数</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <div class="stat-value" id="totalSalesAmount">-</div>
                        <div class="stat-label">总净销售额</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <div class="stat-value" id="totalProfit">-</div>
                        <div class="stat-label">总利润</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 趋势图表 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card chart-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-graph-up me-2"></i>销售趋势分析
                        </h5>
                        <div class="d-flex align-items-center flex-wrap gap-2">
                            <div class="me-2">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="yearCompareSwitch">
                                    <label class="form-check-label text-white fw-semibold" for="yearCompareSwitch">
                                        年度对比
                                    </label>
                                </div>
                            </div>
                            <div class="btn-group btn-group-sm me-2" role="group" id="yearCompareControls" style="display: none;">
                                <select class="form-select form-select-sm" id="compareYear1" style="width: auto;">
                                    <option value="2024">2024年</option>
                                    <option value="2023">2023年</option>
                                    <option value="2022">2022年</option>
                                </select>
                                <span class="btn btn-outline-light btn-sm disabled">vs</span>
                                <select class="form-select form-select-sm" id="compareYear2" style="width: auto;">
                                    <option value="2025" selected>2025年</option>
                                    <option value="2024">2024年</option>
                                    <option value="2023">2023年</option>
                                </select>
                            </div>
                            <div class="btn-group btn-group-sm me-2" role="group">
                                <button type="button" class="btn btn-outline-light active" data-chart-type="line">
                                    <i class="bi bi-graph-up"></i>
                                </button>
                                <button type="button" class="btn btn-outline-light" data-chart-type="bar">
                                    <i class="bi bi-bar-chart"></i>
                                </button>
                                <button type="button" class="btn btn-outline-light" data-chart-type="area">
                                    <i class="bi bi-area-chart"></i>
                                </button>
                            </div>
                            <button class="btn btn-outline-light btn-sm" id="refreshTrendBtn" title="刷新趋势图">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="trendChart" style="height: 500px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选面板 -->
        <div class="filter-panel">
            <div class="row">
                <div class="col-12 mb-3">
                    <h5><i class="bi bi-funnel me-2"></i>数据筛选</h5>
                </div>
                <div class="col-lg-4 col-md-6 mb-3">
                    <label class="form-label">日期范围</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="dateRange" placeholder="选择日期范围">
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                快捷选择
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="setQuickDate('2025')">2025年数据</a></li>
                                <li><a class="dropdown-item" href="#" onclick="setQuickDate('2024')">2024年数据</a></li>
                                <li><a class="dropdown-item" href="#" onclick="setQuickDate('2023')">2023年数据</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="setQuickDate('last30')">最近30天</a></li>
                                <li><a class="dropdown-item" href="#" onclick="setQuickDate('last90')">最近90天</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <label class="form-label">时间粒度</label>
                    <select class="form-select" id="granularity">
                        <option value="daily" selected>按日</option>
                        <option value="weekly">按周</option>
                        <option value="monthly">按月</option>
                    </select>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <label class="form-label">数据展示模式</label>
                    <select class="form-select" id="displayMode">
                        <option value="separate" selected>分店铺展示</option>
                        <option value="total">全部店铺汇总</option>
                    </select>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <label class="form-label">排序方式</label>
                    <select class="form-select" id="sortBy">
                                                        <option value="sales_amount" selected>按净销售额</option>
                        <option value="sales_orders">按订单数</option>
                        <option value="sales_quantity">按销售数量</option>
                        <option value="shop_name">按店铺名称</option>
                    </select>
                </div>
                <div class="col-lg-2 col-md-6 mb-3">
                    <label class="form-label">搜索</label>
                    <div class="search-container">
                        <i class="bi bi-search search-icon"></i>
                        <input type="text" class="form-control search-input" id="searchInput" placeholder="    搜索店铺...">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <button class="btn btn-primary me-2" onclick="applyFilters()">
                        <i class="bi bi-search me-1"></i>应用筛选
                    </button>
                    <button class="btn btn-outline-primary me-2" onclick="resetFilters()">
                        <i class="bi bi-arrow-clockwise me-1"></i>重置
                    </button>
                    <button class="btn btn-outline-primary" onclick="exportData()">
                        <i class="bi bi-download me-1"></i>导出数据
                    </button>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-table me-2"></i>销售数据详情
                </h5>
                                    <div class="d-flex align-items-center">
                        <span class="me-2">每页显示：</span>
                        <select class="form-select form-select-sm" id="pageSize" style="width: auto;">
                            <option value="25" selected>25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
            </div>
            <div class="card-body p-0">
                <div class="loading-spinner" id="tableLoading" style="display: none;">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2 text-muted">数据加载中...</p>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="salesTable">
                        <thead>
                            <tr>
                                <th width="50">
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th>店铺名称</th>
                                <th>销售订单</th>
                                <th>实际订单</th>
                                <th>销售数量</th>
                                <th>净销售金额</th>
                                <th>平均订单价值</th>
                            </tr>
                        </thead>
                        <tbody id="salesTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <nav aria-label="分页导航">
                    <ul class="pagination mb-0" id="pagination">
                    </ul>
                </nav>
            </div>
        </div>


    `;
    
    // 插入HTML内容
    container.innerHTML = saleAnalyticsHTML;
    
    // 添加必要的CSS样式
    if (!document.getElementById('sale-analytics-styles')) {
        const style = document.createElement('style');
        style.id = 'sale-analytics-styles';
        style.textContent = `
            :root {
                --primary-color: #6366f1;
                --primary-dark: #4f46e5;
                --secondary-color: #06b6d4;
                --accent-color: #8b5cf6;
                --success-color: #10b981;
                --warning-color: #f59e0b;
                --danger-color: #ef4444;
                --gray-50: #f8fafc;
                --gray-100: #f1f5f9;
                --gray-200: #e2e8f0;
                --gray-300: #cbd5e1;
                --gray-400: #94a3b8;
                --gray-500: #64748b;
                --gray-600: #475569;
                --gray-700: #334155;
                --gray-800: #1e293b;
                --gray-900: #0f172a;
                --light-color: #ffffff;
                --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
                --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
                --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
                --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
                --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
                --radius-sm: 0.375rem;
                --radius: 0.5rem;
                --radius-md: 0.75rem;
                --radius-lg: 1rem;
                --radius-xl: 1.5rem;
            }

            /* 全局样式优化 */
            .container {
                background: linear-gradient(135deg, var(--gray-50) 0%, #f0f4f8 100%);
                min-height: 100vh;
                padding: 2rem;
                font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .header h1 {
                background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                font-weight: 800;
                font-size: 2.5rem;
                margin-bottom: 2rem;
                text-align: center;
            }

            /* 卡片样式 */
            .card {
                border: 1px solid var(--gray-200);
                border-radius: var(--radius-xl);
                box-shadow: var(--shadow);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                background: var(--light-color);
                overflow: hidden;
                position: relative;
                backdrop-filter: blur(20px);
            }

            .card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 1px;
                background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.3), transparent);
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            .card:hover {
                transform: translateY(-4px);
                box-shadow: var(--shadow-xl);
                border-color: var(--primary-color);
            }

            .card:hover::before {
                opacity: 1;
            }

            /* 统计卡片 */
            .stat-card {
                background: linear-gradient(135deg, 
                    rgba(99, 102, 241, 0.08) 0%, 
                    rgba(139, 92, 246, 0.08) 50%, 
                    rgba(6, 182, 212, 0.08) 100%);
                border: 1px solid rgba(99, 102, 241, 0.2);
                position: relative;
                overflow: hidden;
                backdrop-filter: blur(20px);
            }

            .stat-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 4px;
                height: 100%;
                background: linear-gradient(180deg, var(--primary-color), var(--accent-color));
            }

            .stat-card:hover {
                background: linear-gradient(135deg, 
                    rgba(99, 102, 241, 0.12) 0%, 
                    rgba(139, 92, 246, 0.12) 50%, 
                    rgba(6, 182, 212, 0.12) 100%);
            }

            .stat-value {
                font-size: 2.25rem;
                font-weight: 800;
                color: var(--primary-color);
                line-height: 1.2;
                margin-bottom: 0.5rem;
                background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }

            .stat-label {
                font-size: 0.8125rem;
                color: var(--gray-600);
                font-weight: 500;
                text-transform: uppercase;
                letter-spacing: 0.05em;
                margin: 0;
            }

            /* 图表卡片 */
            .chart-card {
                background: var(--light-color);
                border: 1px solid var(--gray-200);
                backdrop-filter: blur(20px);
                position: relative;
            }

            .chart-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 2px;
                background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--secondary-color));
                opacity: 0.8;
            }

            .chart-card .card-header {
                background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 50%, var(--secondary-color) 100%);
                color: white;
                border-radius: var(--radius-xl) var(--radius-xl) 0 0;
                border: none;
                padding: 1.5rem;
            }

            .chart-card .card-header h5 {
                margin: 0;
                font-weight: 700;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            /* 筛选面板 */
            .filter-panel {
                background: var(--light-color);
                border: 1px solid var(--gray-200);
                border-radius: var(--radius-xl);
                padding: 2rem;
                margin-bottom: 2rem;
                box-shadow: var(--shadow);
                position: relative;
                backdrop-filter: blur(20px);
            }

            .filter-panel::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 3px;
                background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--secondary-color));
                border-radius: var(--radius-xl) var(--radius-xl) 0 0;
            }

            /* 搜索框 */
            .search-container {
                position: relative;
            }

            .search-icon {
                position: absolute;
                left: 1rem;
                top: 50%;
                transform: translateY(-50%);
                color: var(--gray-400);
                font-size: 1rem;
                z-index: 2;
            }

            .search-input {
                border: 2px solid var(--gray-200);
                border-radius: var(--radius-md);
                padding: 0.875rem 1rem 0.875rem 3rem;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                background: var(--light-color);
                font-size: 0.9375rem;
                color: var(--gray-900);
            }

            .search-input:focus {
                border-color: var(--primary-color);
                box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
                outline: none;
                background: var(--light-color);
            }

            .search-input::placeholder {
                color: var(--gray-400);
            }

            /* 按钮样式 */
            .btn {
                border-radius: var(--radius-md);
                padding: 0.75rem 1.25rem;
                font-weight: 600;
                font-size: 0.9375rem;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                border: none;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                gap: 0.5rem;
                text-decoration: none;
                cursor: pointer;
            }

            .btn:focus {
                outline: none;
                box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
            }

            .btn-primary {
                background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
                color: white;
                box-shadow: var(--shadow-sm);
            }

            .btn-primary:hover {
                background: linear-gradient(135deg, var(--primary-dark) 0%, #7c3aed 100%);
                transform: translateY(-2px);
                box-shadow: var(--shadow-lg);
                color: white;
            }

            .btn-outline-primary {
                border: 2px solid var(--primary-color);
                color: var(--primary-color);
                background: transparent;
            }

            .btn-outline-primary:hover {
                background: var(--primary-color);
                color: white;
                transform: translateY(-2px);
                box-shadow: var(--shadow-md);
            }

            .btn-outline-light {
                border: 2px solid rgba(255, 255, 255, 0.3);
                color: white;
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
            }

            .btn-outline-light:hover {
                background: rgba(255, 255, 255, 0.2);
                border-color: rgba(255, 255, 255, 0.5);
                color: white;
            }

            .btn-sm {
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
            }

            .btn-group .btn {
                border-radius: 0;
            }

            .btn-group .btn:first-child {
                border-radius: var(--radius-md) 0 0 var(--radius-md);
            }

            .btn-group .btn:last-child {
                border-radius: 0 var(--radius-md) var(--radius-md) 0;
            }

            .btn-group .btn.active {
                background: var(--primary-color);
                color: white;
                box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            /* 表单样式 */
            .form-label {
                font-weight: 600;
                color: var(--gray-700);
                margin-bottom: 0.5rem;
                font-size: 0.9375rem;
            }

            .form-select, .form-control {
                border: 2px solid var(--gray-200);
                border-radius: var(--radius-md);
                padding: 0.875rem 1rem;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                background: var(--light-color);
                font-size: 0.9375rem;
                color: var(--gray-900);
            }

            .form-select:focus, .form-control:focus {
                border-color: var(--primary-color);
                box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
                outline: none;
                background: var(--light-color);
            }

            .form-select:hover, .form-control:hover {
                border-color: var(--gray-300);
            }

            /* 表格样式 */
            .table-responsive {
                border-radius: var(--radius-xl);
                overflow: hidden;
                box-shadow: var(--shadow-sm);
            }

            .table {
                margin-bottom: 0;
                font-size: 0.9375rem;
            }

            .table thead th {
                background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 50%, var(--secondary-color) 100%);
                color: white;
                border: none;
                font-weight: 600;
                padding: 1.25rem 1rem;
                text-transform: uppercase;
                letter-spacing: 0.025em;
                font-size: 0.875rem;
                position: sticky;
                top: 0;
                z-index: 10;
            }

            .table tbody tr {
                border-bottom: 1px solid var(--gray-200);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                background: var(--light-color);
            }

            .table tbody tr:hover {
                background: linear-gradient(135deg, 
                    rgba(99, 102, 241, 0.03) 0%, 
                    rgba(139, 92, 246, 0.03) 50%, 
                    rgba(6, 182, 212, 0.03) 100%);
                transform: translateX(4px);
                box-shadow: 4px 0 0 var(--primary-color);
            }

            .table tbody td {
                padding: 1.25rem 1rem;
                vertical-align: middle;
                color: var(--gray-800);
            }

            /* 徽章样式 */
            .badge {
                padding: 0.375rem 0.875rem;
                border-radius: var(--radius);
                font-weight: 600;
                font-size: 0.8125rem;
                text-transform: uppercase;
                letter-spacing: 0.025em;
            }

            .bg-success {
                background: linear-gradient(135deg, var(--success-color), #34d399) !important;
                color: white;
            }

            .bg-warning {
                background: linear-gradient(135deg, var(--warning-color), #fbbf24) !important;
                color: white;
            }

            .bg-danger {
                background: linear-gradient(135deg, var(--danger-color), #f87171) !important;
                color: white;
            }

            /* 分页样式 */
            .pagination {
                justify-content: center;
                margin-top: 2rem;
                gap: 0.25rem;
            }

            .page-link {
                border: 2px solid var(--gray-200);
                border-radius: var(--radius-md);
                padding: 0.75rem 1rem;
                color: var(--gray-700);
                background: var(--light-color);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                font-weight: 500;
                text-decoration: none;
            }

            .page-link:hover {
                background: var(--primary-color);
                border-color: var(--primary-color);
                color: white;
                transform: translateY(-2px);
                box-shadow: var(--shadow-md);
            }

            .page-item.active .page-link {
                background: var(--primary-color);
                border-color: var(--primary-color);
                color: white;
                box-shadow: var(--shadow);
            }

            /* 加载动画 */
            .loading-spinner {
                text-align: center;
                padding: 3rem;
            }

            .spinner-border {
                color: var(--primary-color);
                width: 3rem;
                height: 3rem;
            }

            /* 年度对比提示 */
            .year-compare-hint {
                background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
                border: 1px solid rgba(99, 102, 241, 0.2);
                border-radius: var(--radius-md);
                padding: 1rem 1.5rem;
                margin-bottom: 1rem;
                color: var(--primary-color);
                backdrop-filter: blur(10px);
            }

            /* 下拉菜单 */
            .dropdown-menu {
                border: 1px solid var(--gray-200);
                border-radius: var(--radius-md);
                box-shadow: var(--shadow-lg);
                background: var(--light-color);
                backdrop-filter: blur(20px);
                padding: 0.5rem;
            }

            .dropdown-item {
                border-radius: var(--radius-sm);
                padding: 0.75rem 1rem;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                color: var(--gray-700);
                font-weight: 500;
            }

            .dropdown-item:hover {
                background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
                color: var(--primary-color);
                transform: translateX(4px);
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                .container {
                    padding: 1rem;
                }
                
                .card-body {
                    padding: 1rem;
                }
                
                .stat-value {
                    font-size: 1.75rem;
                }

                .filter-panel {
                    padding: 1.5rem;
                }

                .btn {
                    padding: 0.625rem 1rem;
                    font-size: 0.875rem;
                }
            }
        `;
        document.head.appendChild(style);
    }
    
    // 等待DOM更新后初始化销售分析功能
    setTimeout(() => {
        // 首先确保jQuery已加载
        if (typeof $ === 'undefined') {
            console.log('jQuery未加载，正在加载...');
            const jqueryScript = document.createElement('script');
            jqueryScript.src = 'https://code.jquery.com/jquery-3.6.0.min.js';
            jqueryScript.onload = () => {
                console.log('jQuery加载完成');
                loadMoment();
            };
            document.head.appendChild(jqueryScript);
        } else {
            loadMoment();
        }
        
        function loadMoment() {
            // 确保必要的库已加载
            if (typeof moment === 'undefined') {
                console.log('Moment.js未加载，正在加载...');
                // 加载moment.js
                const momentScript = document.createElement('script');
                momentScript.src = 'https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js';
                momentScript.onload = () => {
                    console.log('Moment.js加载完成');
                    loadDateRangePicker();
                };
                document.head.appendChild(momentScript);
            } else {
                loadDateRangePicker();
            }
        }
        
        function loadDateRangePicker() {
            // 加载daterangepicker CSS
            if (!document.querySelector('link[href*="daterangepicker"]')) {
                const dateRangeCSS = document.createElement('link');
                dateRangeCSS.rel = 'stylesheet';
                dateRangeCSS.href = 'https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css';
                document.head.appendChild(dateRangeCSS);
            }
            
            // 加载daterangepicker JS
            if (typeof $.fn.daterangepicker === 'undefined') {
                console.log('DateRangePicker未加载，正在加载...');
                const dateRangeScript = document.createElement('script');
                dateRangeScript.src = 'https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js';
                dateRangeScript.onload = () => {
                    console.log('DateRangePicker加载完成');
                    initializeSaleAnalytics();
                };
                document.head.appendChild(dateRangeScript);
            } else {
                initializeSaleAnalytics();
            }
        }
        
        function initializeSaleAnalytics() {
            // 确保Bootstrap Icons已加载
            if (!document.querySelector('link[href*="bootstrap-icons"]')) {
                const bootstrapIconsCSS = document.createElement('link');
                bootstrapIconsCSS.rel = 'stylesheet';
                bootstrapIconsCSS.href = 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css';
                document.head.appendChild(bootstrapIconsCSS);
            }
            
            console.log('开始初始化销售分析功能...');
            // 初始化销售分析功能
            window.saleAnalytics = new SaleAnalytics();
        }
    }, 100);
}

// 初始化（仅在直接访问页面时使用）
$(document).ready(function() {
    // 只有在页面直接加载时才初始化，避免在shop.html中重复初始化
    if (window.location.pathname.includes('sale-analytics.html')) {
        window.saleAnalytics = new SaleAnalytics();
    }
});
