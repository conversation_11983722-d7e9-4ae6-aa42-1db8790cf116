---
description: 
globs: 
alwaysApply: true
---
# 故障排除指南

## 🚨 常见问题及解决方案

基于 [app.py](mdc:app.py) 和相关工具的分析，整理常见问题及解决方案：

## 🔐 权限相关问题

### 问题1: 用户登录失败
**现象**: 用户无法登录系统，返回认证失败

**可能原因**:
1. [宜承账号.json](mdc:宜承账号.json) 文件不存在或格式错误
2. 用户名或密码错误
3. 账号被禁用

**排查步骤**:
```bash
# 1. 检查账号文件是否存在
ls -la 宜承账号.json

# 2. 验证JSON格式
python -m json.tool 宜承账号.json

# 3. 检查文件权限
chmod 644 宜承账号.json
```

**解决方案**:
```python
# 在app.py中的read_accounts函数会自动创建默认admin账号
# 如果文件损坏，删除文件重启应用即可自动重建
```

### 问题2: 权限不足错误
**现象**: API调用返回403错误，提示"权限不足，需要管理员权限"

**排查步骤**:
1. 检查用户Cookie中的userRole值
2. 确认用户账号的role字段设置正确
3. 验证@admin_required装饰器是否正确应用

**解决方案**:
```python
# 检查Cookie设置
user_role = request.cookies.get('userRole')
print(f"当前用户角色: {user_role}")

# 确认角色有效性
VALID_ROLES = ['admin', 'yunying', 'art', 'caigou', 'caiwu', 'kefu']
```

## 💾 数据库相关问题

### 问题3: 数据库连接失败
**现象**: 应用启动时无法连接到SQLite数据库

**可能原因**:
1. 数据库文件路径错误
2. 文件权限问题
3. 数据库文件损坏

**排查步骤**:
```bash
# 检查数据库文件
ls -la static/db/
file static/db/sale_data.db
sqlite3 static/db/sale_data.db ".tables"
```

**解决方案**:
```bash
# 创建数据库目录
mkdir -p static/db

# 设置正确权限
chmod 755 static/db
chmod 664 static/db/*.db
```

### 问题4: 数据导入失败
**现象**: 使用[sale_data_importer.py](mdc:sale_data_importer.py)导入数据时出错

**排查步骤**:
```bash
# 1. 检查Excel文件格式
python -c "import pandas as pd; print(pd.read_excel('path/to/file.xlsx').columns.tolist())"

# 2. 检查数据库表结构
sqlite3 static/db/sale_data.db ".schema sale_data"

# 3. 运行验证脚本
python check_sales_data.py
```

**解决方案**:
```bash
# 重新运行导入工具
python sale_data_importer.py --file "specific_file.xlsx"

# 查看详细日志
python sale_data_importer.py --verbose
```

## 📁 文件处理问题

### 问题5: 文件上传失败
**现象**: 任务附件或数据文件上传失败

**可能原因**:
1. 文件大小超限
2. 文件类型不支持
3. 存储目录权限问题

**排查步骤**:
```bash
# 检查上传目录权限
ls -la static/uploads/
ls -la static/task_attachments/

# 检查磁盘空间
df -h
```

**解决方案**:
```bash
# 创建必要目录
mkdir -p static/uploads static/task_attachments static/temp

# 设置权限
chmod 755 static/uploads static/task_attachments static/temp
```

### 问题6: Excel文件编码问题
**现象**: 读取CSV或Excel文件时出现中文乱码

**解决方案**:
```python
# 使用多种编码尝试读取
encodings = ['gbk', 'utf-8', 'gb18030', 'gb2312', 'cp936']
for encoding in encodings:
    try:
        data = pd.read_csv(file_path, encoding=encoding)
        break
    except UnicodeDecodeError:
        continue
```

## 🌐 API相关问题

### 问题7: 跨域请求失败
**现象**: 前端调用API时出现CORS错误

**解决方案**:
```python
# 确保在app.py中启用了CORS
from flask_cors import CORS
CORS(app, supports_credentials=True)
```

### 问题8: API响应格式错误
**现象**: 前端无法正确解析API响应

**标准响应格式**:
```python
# 成功响应
return jsonify({
    "success": True,
    "data": data,
    "message": "操作成功"
})

# 错误响应
return jsonify({
    "success": False,
    "message": "具体错误信息",
    "error_code": "ERROR_CODE"
}), 400
```

## 📊 数据分析问题

### 问题9: 趋势数据计算错误
**现象**: 仪表盘显示的趋势数据不准确

**排查步骤**:
1. 检查[utils/sales_data.py](mdc:utils/sales_data.py)中的计算逻辑
2. 验证日期范围参数
3. 检查数据库中的原始数据

**调试方法**:
```python
# 在calculate_trends函数中添加调试信息
def calculate_trends(data, date_range):
    print(f"输入数据量: {len(data)}")
    print(f"日期范围: {date_range}")
    # 计算逻辑...
    print(f"计算结果: {result}")
    return result
```

### 问题10: SPU数据不匹配
**现象**: SPU数据分析结果与预期不符

**排查步骤**:
```bash
# 检查SPU数据导入工具
python spu_data_importer.py --check

# 验证数据迁移
python spu_data_migration.py --verify
```

## 🎨 任务管理问题

### 问题11: 任务附件下载失败
**现象**: 点击任务附件下载链接无响应

**排查步骤**:
```bash
# 检查附件文件是否存在
ls -la static/task_attachments/<task_id>/

# 检查文件权限
chmod 644 static/task_attachments/<task_id>/*
```

### 问题12: 任务状态更新失败
**现象**: 任务状态修改后不生效

**可能原因**:
1. 权限不足
2. 任务ID不存在
3. 状态值无效

**解决方案**:
```python
# 验证任务状态值
VALID_TASK_STATUSES = ['pending', 'in_progress', 'completed', 'cancelled']

# 检查任务是否存在
task = get_task_by_id(task_id)
if not task:
    return jsonify({"success": False, "message": "任务不存在"}), 404
```

## 🔧 系统维护问题

### 问题13: 系统性能下降
**现象**: 页面加载缓慢，API响应时间长

**排查步骤**:
```bash
# 检查数据库大小
ls -lh static/db/*.db

# 检查系统资源使用
top
df -h
```

**优化方案**:
```sql
-- 重建数据库索引
REINDEX;

-- 分析数据库统计信息
ANALYZE;

-- 清理临时文件
VACUUM;
```

### 问题14: 日志文件过大
**现象**: 系统日志文件占用大量磁盘空间

**解决方案**:
```bash
# 清理旧日志
find . -name "*.log" -mtime +7 -delete

# 实现日志轮转
logrotate /etc/logrotate.d/app
```

## 🔍 调试技巧

### 开启调试模式
```python
# 在app.py中添加调试配置
app.debug = True
app.config['DEBUG'] = True
```

### 添加日志记录
```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)

# 在关键位置添加日志
logging.info(f"处理请求: {request.path}")
logging.error(f"错误详情: {str(e)}")
```

### 数据库查询调试
```python
# 打印SQL查询
def debug_query(query, params=None):
    print(f"执行查询: {query}")
    if params:
        print(f"参数: {params}")
```

## 📞 紧急处理流程

### 严重错误处理
1. **立即停止服务**: `pkill -f app.py`
2. **备份数据**: `cp -r static/db/ backup/`
3. **检查日志**: `tail -f app.log`
4. **修复问题**: 根据错误信息修复
5. **重启服务**: `python app.py`

### 数据恢复
```bash
# 恢复数据库备份
cp backup/sale_data.db static/db/

# 重新导入最新数据
python sale_data_importer.py --file "latest_*.xlsx"
```

## 📋 预防措施

### 定期维护
- 每日备份数据库
- 每周清理临时文件
- 每月检查系统性能
- 季度更新依赖包

### 监控设置
```python
# 添加健康检查端点
@app.route('/health')
def health_check():
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "database": check_database_connection(),
        "disk_usage": get_disk_usage()
    })
```

### 错误通知
```python
# 配置错误邮件通知
import smtplib
from email.mime.text import MIMEText

def send_error_notification(error_msg):
    # 发送错误通知邮件
    pass
```

