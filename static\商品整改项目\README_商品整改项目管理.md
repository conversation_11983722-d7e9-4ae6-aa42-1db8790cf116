# 商品整改项目管理系统

## 概述

商品整改项目管理系统是宜承店铺数据管理系统的一个重要组成部分，用于管理和跟踪商品整改项目的状态和进度。该系统提供了完整的CRUD操作、数据筛选、分页显示、数据导出等功能。

## 功能特性

### 1. 数据展示
- **统计卡片**: 显示总计、待处理、已提交、未提交的项目数量
- **数据表格**: 以表格形式展示整改项目列表
- **分页显示**: 支持自定义每页显示数量（10/20/50/100条）
- **响应式设计**: 适配不同屏幕尺寸

### 2. 搜索和筛选
- **关键词搜索**: 支持按商品名称、店铺名称、商品ID搜索
- **店铺筛选**: 按店铺名称筛选项目
- **状态筛选**: 按项目状态筛选（待处理/已提交/未提交）
- **问题类型筛选**: 按问题标签筛选
- **日期范围筛选**: 按创建时间范围筛选

### 3. 数据管理
- **查看详情**: 点击表格行查看项目详细信息
- **数据导出**: 支持导出筛选后的数据为CSV格式
- **实时刷新**: 支持手动刷新数据
- **缓存机制**: 30分钟本地缓存，提升加载速度

### 4. 权限管理
- **角色权限**: 不同角色拥有不同的访问权限
- **菜单集成**: 与主系统导航菜单无缝集成
- **权限验证**: 基于用户角色的权限验证

## 技术架构

### 前端技术
- **JavaScript**: 原生JavaScript，使用模块化命名空间
- **Bootstrap 5**: 响应式UI框架
- **ECharts**: 图表库（预留扩展）
- **本地缓存**: localStorage实现数据缓存

### 后端技术
- **Flask**: Python Web框架
- **SQLite**: 数据库存储
- **RESTful API**: 标准REST接口设计

### 数据库结构
```sql
-- 整改项目表
CREATE TABLE rectification_items (
    id INTEGER PRIMARY KEY,
    goods_id TEXT NOT NULL,
    mall_id TEXT NOT NULL,
    shop_name TEXT NOT NULL,
    type INTEGER NOT NULL,
    status INTEGER NOT NULL,
    questionnaire_id INTEGER DEFAULT 0,
    problem_label TEXT,
    base_info TEXT,
    advise_info TEXT,
    merchant_plan TEXT,
    adviser INTEGER,
    created_at_timestamp BIGINT,
    updated_at_timestamp BIGINT,
    ext_map TEXT,
    data_category TEXT NOT NULL,
    inserted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 店铺汇总表
CREATE TABLE shop_summary (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    shop_name TEXT NOT NULL UNIQUE,
    mall_id TEXT NOT NULL,
    init_counts INTEGER DEFAULT 0,
    submit_counts INTEGER DEFAULT 0,
    no_submit_counts INTEGER DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## API接口

### 1. 获取整改数据
```
GET /api/product-rectification
```
**参数**:
- `page`: 页码（默认1）
- `pageSize`: 每页数量（默认20）
- `keyword`: 搜索关键词
- `shop`: 店铺筛选
- `status`: 状态筛选
- `problemType`: 问题类型筛选
- `startDate`: 开始日期
- `endDate`: 结束日期
- `sortField`: 排序字段
- `sortOrder`: 排序方向

### 2. 获取项目详情
```
GET /api/product-rectification/{id}
```

### 3. 创建新项目
```
POST /api/product-rectification
```

### 4. 更新项目
```
PUT /api/product-rectification/{id}
```

### 5. 删除项目
```
DELETE /api/product-rectification/{id}
```

### 6. 导出数据
```
GET /api/product-rectification/export
```

## 文件结构

```
static/商品整改项目/
├── generateProductRectification.js    # 前端主文件
├── production_rectification.db        # 生产数据库
├── demo_rectification.db             # 演示数据库
├── sqlite_demo.py                     # 数据库操作类
├── database_query_tool.py             # 数据库查询工具
├── integrated_data_collector.py       # 数据收集器
└── README_商品整改项目管理.md          # 本文档
```

## 使用说明

### 1. 访问系统
1. 登录宜承店铺数据管理系统
2. 在主导航菜单中点击"商品整改项目管理"
3. 系统将自动加载商品整改管理界面

### 2. 查看数据
- 系统默认显示所有整改项目
- 顶部统计卡片显示项目概况
- 表格显示项目详细列表

### 3. 搜索筛选
- 在搜索框输入关键词进行搜索
- 使用下拉菜单进行筛选
- 设置日期范围筛选
- 点击"应用筛选"按钮执行筛选

### 4. 查看详情
- 点击表格中的任意行查看项目详情
- 或点击操作列中的"查看"按钮
- 详情模态框显示完整项目信息

### 5. 导出数据
- 点击"导出数据"按钮
- 系统将导出当前筛选条件下的所有数据
- 文件格式为CSV，包含中文表头

## 权限配置

### 角色权限
- **管理员**: 拥有所有权限
- **运营**: 拥有除财务外的所有权限（包括商品整改）
- **美工**: 拥有美工任务、违规管理、商品整改权限
- **其他角色**: 根据具体配置分配权限

### 权限标识
- 权限ID: `product-rectification-management`
- 权限名称: 商品整改项目管理
- 图标: 🔧

## 注意事项

1. **数据库文件**: 系统优先使用`production_rectification.db`，如不存在则使用`demo_rectification.db`
2. **缓存机制**: 数据缓存30分钟，可手动刷新获取最新数据
3. **权限验证**: 确保用户拥有相应权限才能访问功能
4. **数据安全**: 所有API操作都包含错误处理和数据验证

## 故障排除

### 常见问题
1. **数据加载失败**: 检查数据库文件是否存在
2. **权限不足**: 确认用户角色和权限配置
3. **搜索无结果**: 检查搜索条件和数据范围
4. **导出失败**: 确认服务器权限和磁盘空间

### 日志查看
- 前端日志: 浏览器开发者工具Console
- 后端日志: Flask应用日志
- 数据库日志: SQLite操作日志

## 更新日志

### v1.0.0 (2025-07-05)
- 初始版本发布
- 实现基础CRUD功能
- 添加搜索筛选功能
- 集成权限管理
- 支持数据导出

## 联系支持

如有问题或建议，请联系系统管理员或开发团队。
