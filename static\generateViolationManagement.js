/**
 * 商品违规整改管理
 * 用于显示和管理商品降权违规整改数据
 */

// 创建违规管理命名空间，避免与其他模块冲突
window.ViolationManagement = (function() {
    'use strict';
    
    // 私有变量，避免全局污染
    let currentPage = 1;
    let totalRecords = 0;
    let pageSize = 20;
    let sortField = 'start_time';
    let sortOrder = 'desc';
    let keyword = '';
    let shopFilter = '';
    let startDate = '';
    let endDate = '';
    let statusFilter = 'all';
    let punishTypeFilter = 'all';
    let operatorFilter = 'all';
    
    // 统计数据
    let statsData = {
        today: 0,
        weekly: 0,
        monthly: 0,
        dailyGrowth: 0,
        weeklyGrowth: 0,
        monthlyGrowth: 0
    };

    // 设置缓存过期时间（毫秒）
    const VIOLATION_CACHE_EXPIRY = 30 * 60 * 1000; // 30分钟

    // CSV字段映射到显示字段
    const fieldMapping = {
        'recordId': '记录ID',
        'goodsId': 'goodsID',
        'productTitle': '商品名称',
        'targetTypeId': '违规目标',
        'punishMeasure': '处罚措施',
        'punishReason': '处罚原因',
        'startTime': 'startTime',
        'realEndTime': 'realEndTime',
        'endTime': 'endTime',
        'punishStatus': '处罚状态',
        'appealStatus': 'appealStatus',
        'appealId': 'appealID',
        'rectificationMeasure': 'rectificationMeasure',
        'operationTypeList': 'operationTypeList',
        'shopName': '店铺名称',
        'operator': '运营人员',
        'shopDisplayName': '店铺展示名称',
        'startTimeFormat': '开始时间',
        'endTimeFormat': '结束时间',
        'realEndTimeFormat': '实际结束时间',
        'statusDisplay': '状态',
        'targetType': '目标类型'
    };

    // 显示状态映射
    const statusMapping = {
        '0': '处理中',
        '1': '处理中',
        '2': '已结束',
        '3': '已撤销'
    };

    // 处罚类型映射
    const punishTypeMapping = {
        '商品降权': 'badge bg-danger',
        '违规信息删除': 'badge bg-warning',
        '其他处罚': 'badge bg-info'
    };

    // 目标类型映射
    const targetTypeMapping = {
        'goods': '商品链接',
        'mall': '店铺'
    };

    // 加载违规整改数据
    function loadViolationData() {
        // 显示加载状态
        const container = document.querySelector('.container');
        container.innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-3">正在加载违规整改数据...</p>
            </div>
        `;

        // 尝试从缓存加载数据
        const cachedData = loadFromCache();
        if (cachedData) {
            // 使用缓存数据进行初始渲染
            const { page, pageSize: cachedPageSize, data, total, statistics } = cachedData;
            // 设置全局变量
            currentPage = page || 1;
            pageSize = cachedPageSize || 20;
            totalRecords = total || 0;
            statsData = statistics || statsData;
            
            console.log("已从缓存加载数据，共加载 " + totalRecords + " 条记录");
            
            // 保存当前数据供图表更新使用
            window.currentViolationData = data;
            
            renderViolationManagement(data);
            
            // 在后台继续请求最新数据
            fetchViolationData();
        } else {
            // 直接请求数据
            fetchViolationData();
        }
    }

    // 保存数据到本地缓存
    function saveToCache(responseData) {
        try {
            const cacheData = {
                timestamp: Date.now(),
                page: responseData.page,
                pageSize: responseData.pageSize,
                total: responseData.total,
                data: responseData.data,
                statistics: responseData.statistics
            };
            localStorage.setItem('violationDataCache', JSON.stringify(cacheData));
            console.log("数据已缓存到本地，共 " + responseData.total + " 条记录");
            return true;
        } catch (error) {
            console.error("缓存数据失败:", error);
            return false;
        }
    }

    // 从本地缓存加载数据
    function loadFromCache() {
        try {
            const cacheData = localStorage.getItem('violationDataCache');
            if (!cacheData) return null;
            
            const parsedCache = JSON.parse(cacheData);
            const now = Date.now();
            
            // 检查缓存是否过期
            if (now - parsedCache.timestamp > VIOLATION_CACHE_EXPIRY) {
                console.log("缓存已过期，需要重新加载数据");
                localStorage.removeItem('violationDataCache');
                return null;
            }
            
            return parsedCache;
        } catch (error) {
            console.error("读取缓存数据失败:", error);
            return null;
        }
    }

    // 从API获取违规数据
    function fetchViolationData() {
        // 显示加载状态
        const tableBody = document.getElementById('violationTableBody');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载数据...</p>
                    </td>
                </tr>
            `;
        }
        
        // 构建查询URL
        let url = `/api/violation-data?page=${currentPage}&pageSize=${pageSize}&sortField=${sortField}&sortOrder=${sortOrder}`;
        
        // 添加关键词搜索参数
        if (keyword) {
            url += `&keyword=${encodeURIComponent(keyword)}`;
        }
        
        // 添加店铺筛选参数
        if (shopFilter && shopFilter !== 'all') {
            url += `&shop=${encodeURIComponent(shopFilter)}`;
        }
        
        // 添加运营人员筛选参数
        if (operatorFilter && operatorFilter !== 'all') {
            url += `&operator=${encodeURIComponent(operatorFilter)}`;
        }
        
        // 添加状态筛选参数 - 添加这个参数传递到后端
        if (statusFilter && statusFilter !== 'all') {
            url += `&status=${encodeURIComponent(statusFilter)}`;
        }
        
        // 添加处罚类型筛选参数 - 添加这个参数传递到后端
        if (punishTypeFilter && punishTypeFilter !== 'all') {
            url += `&punishType=${encodeURIComponent(punishTypeFilter)}`;
        }
        
        // 添加日期范围参数
        if (startDate) {
            url += `&startDate=${encodeURIComponent(startDate)}`;
        }
        
        if (endDate) {
            url += `&endDate=${encodeURIComponent(endDate)}`;
        }
        
        console.log('发送数据请求URL:', url); // 添加调试日志
        
        fetch(url)
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    // 更新统计数据
                    if (result.statistics) {
                        statsData = result.statistics;
                    }
                    
                    // 更新总记录数
                    totalRecords = result.total || 0;
                    
                    // 保存到缓存
                    saveToCache(result);
                    
                    // 保存当前数据供图表更新使用
                    window.currentViolationData = result.data;
                    
                    // 渲染数据
                    renderViolationManagement(result.data);
                    
                    // 渲染筛选选项
                    generateFilterOptions(result.data, result.operators);
                    
                    console.log(`成功加载数据: ${result.data.length} 条记录，共 ${totalRecords} 条`);
                } else {
                    throw new Error(result.message || '加载数据失败');
                }
            })
            .catch(error => {
                console.error('获取违规数据出错:', error);
                const container = document.querySelector('.container');
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <h4 class="alert-heading">加载失败</h4>
                        <p>${error.message || '无法加载违规整改数据，请刷新页面重试'}</p>
                    </div>
                `;
            });
    }

    // 格式化时间戳
    function formatTimestamp(timestamp) {
        if (!timestamp) return '';
        
        try {
            const date = new Date(Number(timestamp));
            if (isNaN(date.getTime())) return timestamp;
            
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            }).replace(/\//g, '-');
        } catch (e) {
            return timestamp;
        }
    }

    // 渲染违规整改管理页面
    function renderViolationManagement(data) {
        const container = document.querySelector('.container');
        
        // 保存当前筛选值
        const currentShopFilter = shopFilter;
        const currentStatusFilter = statusFilter;
        const currentPunishTypeFilter = punishTypeFilter;
        const currentStartDate = startDate;
        const currentEndDate = endDate;
        const currentPageSizeValue = pageSize;
        const currentKeyword = keyword;
        
        // 构建页面内容
        container.innerHTML = `
            <div class="violation-management">
                <div class="header">
                    <h1>商品违规整改管理</h1>
                    <p>管理和跟踪商品降权违规整改状态</p>
                </div>
                
                <!-- 数据概览卡片 -->
                <div class="data-overview">
                    <div class="row g-3 mb-4">
                        <div class="col-md-3">
                            <div class="overview-card">
                                <div class="overview-icon bg-danger">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="overview-info">
                                    <h3>${statsData.today || 0}</h3>
                                    <p>今日违规</p>
                                </div>
                                <div class="overview-trend ${(statsData.dailyGrowth || 0) >= 0 ? 'up' : 'down'}">
                                    <span>${Math.abs(statsData.dailyGrowth || 0)}%</span>
                                    <i class="fas fa-arrow-${(statsData.dailyGrowth || 0) >= 0 ? 'up' : 'down'}"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="overview-card">
                                <div class="overview-icon bg-primary">
                                    <i class="fas fa-calendar-week"></i>
                                </div>
                                <div class="overview-info">
                                    <h3>${statsData.weekly || 0}</h3>
                                    <p>近一周违规</p>
                                </div>
                                <div class="overview-trend ${(statsData.weeklyGrowth || 0) >= 0 ? 'up' : 'down'}">
                                    <span>${Math.abs(statsData.weeklyGrowth || 0)}%</span>
                                    <i class="fas fa-arrow-${(statsData.weeklyGrowth || 0) >= 0 ? 'up' : 'down'}"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="overview-card">
                                <div class="overview-icon bg-success">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div class="overview-info">
                                    <h3>${statsData.monthly || 0}</h3>
                                    <p>近一月违规</p>
                                </div>
                                <div class="overview-trend ${(statsData.monthlyGrowth || 0) >= 0 ? 'up' : 'down'}">
                                    <span>${Math.abs(statsData.monthlyGrowth || 0)}%</span>
                                    <i class="fas fa-arrow-${(statsData.monthlyGrowth || 0) >= 0 ? 'up' : 'down'}"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="overview-card">
                                <div class="overview-icon bg-warning">
                                    <i class="fas fa-store"></i>
                                </div>
                                <div class="overview-info">
                                    <h3>${statsData.shopCount || '-'}</h3>
                                    <p>近一月涉及店铺</p>
                                </div>
                                <div class="overview-trend ${(statsData.shopGrowth || 0) >= 0 ? 'up' : 'down'}">
                                    <span>${Math.abs(statsData.shopGrowth || 0)}%</span>
                                    <i class="fas fa-arrow-${(statsData.shopGrowth || 0) >= 0 ? 'up' : 'down'}"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="control-panel">
                    <div class="search-filters">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" id="searchInput" class="form-control" placeholder="搜索商品ID、标题、店铺或违规原因..." value="${currentKeyword || ''}">
                            <button class="btn btn-primary" id="searchBtn">搜索</button>
                        </div>
                        <div class="filters mt-3">
                            <!-- 第一行：主要筛选条件 -->
                            <div class="row g-3 mb-3">
                                <div class="col-lg-2 col-md-3 col-sm-6">
                                    <div class="filter-item">
                                        <label class="form-label">状态筛选</label>
                                        <select id="statusFilter" class="form-select form-select-sm">
                                            <option value="all">所有状态</option>
                                            <option value="处理中" ${currentStatusFilter === '处理中' ? 'selected' : ''}>处理中</option>
                                            <option value="已结束" ${currentStatusFilter === '已结束' ? 'selected' : ''}>已结束</option>
                                            <option value="已撤销" ${currentStatusFilter === '已撤销' ? 'selected' : ''}>已撤销</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-4 col-sm-6">
                                    <div class="filter-item">
                                        <label class="form-label">店铺筛选</label>
                                        <select id="shopFilter" class="form-select form-select-sm">
                                            <option value="all">所有店铺</option>
                                            <!-- 店铺选项将动态生成 -->
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-2 col-md-3 col-sm-6">
                                    <div class="filter-item">
                                        <label class="form-label">运营人员</label>
                                        <select id="operatorFilter" class="form-select form-select-sm">
                                            <option value="all">所有运营</option>
                                            <!-- 运营人员选项将动态生成 -->
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-4 col-sm-6">
                                    <div class="filter-item">
                                        <label class="form-label">处罚类型</label>
                                        <select id="punishTypeFilter" class="form-select form-select-sm">
                                            <option value="all">所有处罚类型</option>
                                            <!-- 处罚类型选项将动态生成 -->
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-6">
                                    <div class="filter-item">
                                        <label class="form-label">每页显示</label>
                                        <select id="pageSizeSelector" class="form-select form-select-sm">
                                            <option value="10" ${currentPageSizeValue === 10 ? 'selected' : ''}>10条</option>
                                            <option value="20" ${currentPageSizeValue === 20 ? 'selected' : ''}>20条</option>
                                            <option value="50" ${currentPageSizeValue === 50 ? 'selected' : ''}>50条</option>
                                            <option value="100" ${currentPageSizeValue === 100 ? 'selected' : ''}>100条</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 第二行：日期筛选和操作按钮 -->
                            <div class="row g-3 align-items-end">
                                <div class="col-lg-5 col-md-6">
                                    <div class="filter-item">
                                        <label class="form-label">日期范围筛选</label>
                                        <div class="input-group input-group-sm">
                                            <input type="date" id="startDateFilter" class="form-control" placeholder="开始日期" value="${currentStartDate || ''}">
                                            <span class="input-group-text">至</span>
                                            <input type="date" id="endDateFilter" class="form-control" placeholder="结束日期" value="${currentEndDate || ''}">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <div class="filter-actions d-flex gap-2">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                                            <i class="fas fa-eraser"></i> 清空筛选
                                        </button>
                                        <button type="button" class="btn btn-primary btn-sm" onclick="applyFilters()">
                                            <i class="fas fa-filter"></i> 应用筛选
                                        </button>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-12">
                                    <div class="export-actions d-flex justify-content-end gap-2">
                                        <div class="dropdown">
                                            <button class="btn btn-outline-primary btn-sm dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="fas fa-file-export"></i> 导出数据
                                            </button>
                                            <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                                                <li><a class="dropdown-item" href="#" id="exportAllBtn">
                                                    <i class="fas fa-download"></i> 导出当前筛选结果
                                                </a></li>
                                            </ul>
                                        </div>
                                        <button type="button" class="btn btn-outline-success btn-sm" onclick="refreshData()">
                                            <i class="fas fa-sync-alt"></i> 刷新数据
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 违规趋势图表 -->
                <div class="chart-container">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-line"></i> 违规趋势分析
                                <small class="text-muted ms-2">实时展示违规数据变化趋势</small>
                            </h5>
                        </div>
                        <div class="card-body" id="chartCardBody">
                            <div id="violationTrendChart" style="height: 450px;"></div>
                        </div>
                    </div>
                </div>
                
                <div class="data-container">
                    <div class="table-responsive">
                        <table class="table table-hover violation-table">
                            <thead>
                                <tr>
                                    <th class="text-nowrap">商品ID</th>
                                    <th style="min-width: 200px;">商品名称</th>
                                    <th class="text-nowrap">违规目标</th>
                                    <th class="text-nowrap">处罚措施</th>
                                    <th style="min-width: 150px;">处罚原因</th>
                                    <th class="text-nowrap">店铺名称</th>
                                    <th class="text-nowrap">运营人员</th>
                                    <th class="text-nowrap">开始时间</th>
                                    <th class="text-nowrap">结束时间</th>
                                    <th class="text-nowrap">状态</th>
                                </tr>
                            </thead>
                            <tbody id="violationTableBody">
                                <!-- 这里将动态填充违规数据 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页和记录信息 -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div class="record-info">
                            显示 <span id="startRecord">0</span> 到 <span id="endRecord">0</span> 条，共 <span id="totalRecords">0</span> 条记录
                        </div>
                        <nav aria-label="违规数据分页">
                            <ul class="pagination" id="paginationContainer">
                                <!-- 这里将动态填充分页控件 -->
                            </ul>
                        </nav>
                    </div>
                </div>
                
                <!-- 导出字段选择模态框 -->
                <div class="modal fade" id="exportFieldsModal" tabindex="-1" aria-labelledby="exportFieldsModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="exportFieldsModalLabel">选择导出字段</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row" id="exportFieldsContainer">
                                    <!-- 这里将动态填充导出字段选项 -->
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" id="confirmExportBtn">确认导出</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 违规详情模态框 -->
                <div class="modal fade" id="violationDetailsModal" tabindex="-1" aria-labelledby="violationDetailsModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="violationDetailsModalLabel">违规详情</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body" id="violationDetailsContent">
                                <!-- 这里将动态填充违规详情 -->
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                <button type="button" class="btn btn-primary edit-violation-btn">编辑</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 违规编辑模态框 -->
                <div class="modal fade" id="violationEditModal" tabindex="-1" aria-labelledby="violationEditModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="violationEditModalLabel">编辑违规记录</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form id="violationEditForm">
                                    <!-- 这里将动态填充编辑表单字段 -->
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" id="saveViolationBtn">保存</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 填充表格数据
        renderViolationTable(data);
        
        // 渲染违规趋势图表
        renderViolationTrendChart();
        
        // 生成筛选选项
        generateFilterOptions(data);
        
        // 设置筛选控件的值（在生成选项后立即设置）
        const shopFilterElem = document.getElementById('shopFilter');
        if (shopFilterElem && currentShopFilter) {
            shopFilterElem.value = currentShopFilter;
        }
        
        const statusFilterElem = document.getElementById('statusFilter');
        if (statusFilterElem && currentStatusFilter) {
            statusFilterElem.value = currentStatusFilter;
        }
        
        const punishTypeFilterElem = document.getElementById('punishTypeFilter');
        if (punishTypeFilterElem && currentPunishTypeFilter) {
            punishTypeFilterElem.value = currentPunishTypeFilter;
        }
        
        const operatorFilterElem = document.getElementById('operatorFilter');
        if (operatorFilterElem && operatorFilter) {
            operatorFilterElem.value = operatorFilter;
        }
        
        const pageSizeSelectorElem = document.getElementById('pageSizeSelector');
        if (pageSizeSelectorElem) {
            pageSizeSelectorElem.value = currentPageSizeValue.toString();
        }
        
        const startDateFilterElem = document.getElementById('startDateFilter');
        if (startDateFilterElem && currentStartDate) {
            startDateFilterElem.value = currentStartDate;
        }
        
        const endDateFilterElem = document.getElementById('endDateFilter');
        if (endDateFilterElem && currentEndDate) {
            endDateFilterElem.value = currentEndDate;
        }
        
        const searchInputElem = document.getElementById('searchInput');
        if (searchInputElem && currentKeyword) {
            searchInputElem.value = currentKeyword;
        }
        
        // 绑定事件处理程序
        bindEventHandlers();
        
        // 添加样式
        addViolationManagementStyles();
    }

    // 渲染违规表格
    function renderViolationTable(data) {
        const tableBody = document.getElementById('violationTableBody');
        
        if (!tableBody) return;
        
        // 更新记录信息显示
        const startRecord = (currentPage - 1) * pageSize + 1;
        const endRecord = Math.min(startRecord + data.length - 1, totalRecords);
        
        document.getElementById('startRecord').textContent = totalRecords > 0 ? startRecord : 0;
        document.getElementById('endRecord').textContent = endRecord;
        document.getElementById('totalRecords').textContent = totalRecords;
        
        // 如果没有数据，显示提示信息
        if (!data || data.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="10" class="text-center">暂无数据</td>
                </tr>
            `;
            renderPagination();
            return;
        }
        
        // 生成表格行
        let tableContent = '';
        for (const item of data) {
            // 处理处罚措施显示
            let punishMeasureDisplay = '';
            if (item.punishMeasureDisplay) {
                punishMeasureDisplay = item.punishMeasureDisplay;
            } else if (typeof item.punishMeasure === 'string') {
                // 尝试处理JSON格式的字符串
                if (item.punishMeasure.includes('[')) {
                    try {
                        const measures = JSON.parse(item.punishMeasure.replace(/'/g, '"'));
                        punishMeasureDisplay = measures.join(', ');
                    } catch (e) {
                        punishMeasureDisplay = item.punishMeasure;
                    }
                } else {
                    punishMeasureDisplay = item.punishMeasure;
                }
            } else if (Array.isArray(item.punishMeasure)) {
                // 如果已经是数组
                punishMeasureDisplay = item.punishMeasure.join(', ');
            } else {
                punishMeasureDisplay = item.punishMeasure || '-';
            }
            
            // 确定处罚类型样式
            let badgeClass = 'badge bg-secondary';
            if (punishMeasureDisplay.includes('商品降权')) {
                badgeClass = punishTypeMapping['商品降权'];
            } else if (punishMeasureDisplay.includes('违规信息删除')) {
                badgeClass = punishTypeMapping['违规信息删除'];
            } else {
                badgeClass = punishTypeMapping['其他处罚'];
            }
            
            // 转换目标类型ID的显示
            const targetTypeId = item.targetTypeId || '';
            let targetTypeDisplay = targetTypeId;
            if (targetTypeMapping[targetTypeId.toLowerCase()]) {
                targetTypeDisplay = targetTypeMapping[targetTypeId.toLowerCase()];
            }
            
            // 确保商品ID没有小数点
            let goodsIdDisplay = item.goodsId || '-';
            if (goodsIdDisplay && goodsIdDisplay.includes('.')) {
                goodsIdDisplay = goodsIdDisplay.split('.')[0];
            }
            
            // 获取运营人员信息
            const operator = item.operator || '-';
            
            // 处理店铺名称
            const shopName = item.shopName || '-';
            
            tableContent += `
                <tr data-id="${item.id}" class="violation-row">
                    <td class="text-nowrap">${goodsIdDisplay}</td>
                    <td>
                        <div class="product-title" title="${item.productTitle || ''}">
                            ${item.productTitle || '-'}
                        </div>
                    </td>
                    <td class="text-center">
                        <span class="badge ${targetTypeId.toLowerCase() === 'goods' ? 'bg-primary' : (targetTypeId.toLowerCase() === 'mall' ? 'bg-success' : 'bg-secondary')}">
                            ${targetTypeDisplay}
                        </span>
                    </td>
                    <td><span class="${badgeClass}">${punishMeasureDisplay || '-'}</span></td>
                    <td>
                        <div class="punish-reason" title="${item.punishReason || ''}">
                            ${item.punishReason || '-'}
                        </div>
                    </td>
                    <td class="text-nowrap">${shopName}</td>
                    <td class="text-nowrap">${operator}</td>
                    <td class="text-nowrap">${item.startTimeFormat || '-'}</td>
                    <td class="text-nowrap">${item.endTimeFormat || '-'}</td>
                    <td class="text-center">
                        <span class="badge ${getStatusBadgeClass(item.statusDisplay || '未知')}">
                            ${item.statusDisplay || '未知'}
                        </span>
                    </td>
                </tr>
            `;
        }
        
        tableBody.innerHTML = tableContent;
        
        // 为每行添加点击事件，打开详情模态框
        document.querySelectorAll('.violation-row').forEach(row => {
            row.addEventListener('click', function() {
                const id = this.dataset.id;
                if (id) {
                    viewViolationDetails(id, data);
                }
            });
        });
        
        // 渲染分页控件
        renderPagination();
    }

    // 获取状态对应的样式类
    function getStatusBadgeClass(status) {
        switch(status) {
            case '处理中': return 'bg-warning';
            case '已结束': return 'bg-success';
            case '已撤销': return 'bg-danger';
            default: return 'bg-secondary';
        }
    }

    // 生成导出字段选择列表
    function generateExportFieldsList() {
        const container = document.getElementById('exportFieldsContainer');
        if (!container) return;
        
        // 获取可导出的字段
        const exportFields = [
            { id: 'recordId', name: '记录ID' },
            { id: 'goodsId', name: '商品ID' },
            { id: 'productTitle', name: '商品名称' },
            { id: 'targetTypeId', name: '违规目标' },
            { id: 'punishMeasure', name: '处罚措施' },
            { id: 'punishReason', name: '处罚原因' },
            { id: 'shopName', name: '店铺名称' },
            { id: 'operator', name: '运营人员' },
            { id: 'startTimeFormat', name: '开始时间' },
            { id: 'endTimeFormat', name: '结束时间' },
            { id: 'realEndTimeFormat', name: '实际结束时间' },
            { id: 'statusDisplay', name: '状态' },
            { id: 'targetType', name: '目标类型' },
            { id: 'rectificationMeasure', name: '整改措施' }
        ];
        
        // 生成字段选择框
        container.innerHTML = '';
        exportFields.forEach(field => {
            const col = document.createElement('div');
            col.className = 'col-md-6';
            col.innerHTML = `
                <div class="form-check mb-2">
                    <input class="form-check-input export-field" type="checkbox" id="field_${field.id}" 
                           data-field="${field.id}" checked>
                    <label class="form-check-label" for="field_${field.id}">
                        ${field.name}
                    </label>
                </div>
            `;
            container.appendChild(col);
        });
        
        // 添加店铺选择区域
        const shopFilterDiv = document.createElement('div');
        shopFilterDiv.className = 'col-12 mt-3';
        shopFilterDiv.innerHTML = `
            <div class="form-group">
                <label for="exportShopFilter" class="form-label">按店铺筛选</label>
                <select id="exportShopFilter" class="form-select">
                    <option value="all">所有店铺</option>
                    <!-- 店铺选项将动态生成 -->
                </select>
            </div>
        `;
        container.appendChild(shopFilterDiv);
        
        // 添加运营人员选择区域
        const operatorFilterDiv = document.createElement('div');
        operatorFilterDiv.className = 'col-12 mt-3';
        operatorFilterDiv.innerHTML = `
            <div class="form-group">
                <label for="exportOperatorFilter" class="form-label">按运营人员筛选</label>
                <select id="exportOperatorFilter" class="form-select">
                    <option value="all">所有运营</option>
                    <!-- 运营人员选项将动态生成 -->
                </select>
            </div>
        `;
        container.appendChild(operatorFilterDiv);
        
        // 添加处罚类型选择区域
        const punishTypeFilterDiv = document.createElement('div');
        punishTypeFilterDiv.className = 'col-12 mt-3';
        punishTypeFilterDiv.innerHTML = `
            <div class="form-group">
                <label for="exportPunishTypeFilter" class="form-label">按处罚类型筛选</label>
                <select id="exportPunishTypeFilter" class="form-select">
                    <option value="all">所有处罚类型</option>
                    <!-- 处罚类型选项将动态生成 -->
                </select>
            </div>
        `;
        container.appendChild(punishTypeFilterDiv);
        
        // 添加时间范围选择区域
        const dateFilter = document.createElement('div');
        dateFilter.className = 'col-12 mt-3';
        dateFilter.innerHTML = `
            <div class="form-group">
                <label class="form-label">按时间筛选</label>
                <div class="d-flex gap-2">
                    <input type="date" id="exportStartDateFilter" class="form-control" placeholder="开始日期">
                    <span class="align-self-center">至</span>
                    <input type="date" id="exportEndDateFilter" class="form-control" placeholder="结束日期">
                </div>
            </div>
        `;
        container.appendChild(dateFilter);
        
        // 填充店铺选项
        const exportShopFilter = document.getElementById('exportShopFilter');
        const mainShopFilter = document.getElementById('shopFilter');
        if (exportShopFilter && mainShopFilter) {
            exportShopFilter.innerHTML = mainShopFilter.innerHTML;
            // 与主界面的店铺筛选同步
            exportShopFilter.value = shopFilter === 'all' ? 'all' : shopFilter;
        }
        
        // 填充运营人员选项
        const exportOperatorFilter = document.getElementById('exportOperatorFilter');
        const mainOperatorFilter = document.getElementById('operatorFilter');
        if (exportOperatorFilter && mainOperatorFilter) {
            exportOperatorFilter.innerHTML = mainOperatorFilter.innerHTML;
            // 与主界面的运营人员筛选同步
            exportOperatorFilter.value = operatorFilter === 'all' ? 'all' : operatorFilter;
        }
        
        // 填充处罚类型选项
        const exportPunishTypeFilter = document.getElementById('exportPunishTypeFilter');
        const mainPunishTypeFilter = document.getElementById('punishTypeFilter');
        if (exportPunishTypeFilter && mainPunishTypeFilter) {
            exportPunishTypeFilter.innerHTML = mainPunishTypeFilter.innerHTML;
            // 与主界面的处罚类型筛选同步
            exportPunishTypeFilter.value = punishTypeFilter === 'all' ? 'all' : punishTypeFilter;
        }
        
        // 与主界面的日期筛选同步
        const exportStartDateFilter = document.getElementById('exportStartDateFilter');
        const exportEndDateFilter = document.getElementById('exportEndDateFilter');
        
        if (exportStartDateFilter) {
            exportStartDateFilter.value = startDate;
        }
        if (exportEndDateFilter) {
            exportEndDateFilter.value = endDate;
        }
        
        // 添加全选/取消全选复选框
        const selectAllDiv = document.createElement('div');
        selectAllDiv.className = 'col-12 mb-3';
        selectAllDiv.innerHTML = `
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="selectAllFields" checked>
                <label class="form-check-label" for="selectAllFields">
                    <strong>全选/取消全选</strong>
                </label>
            </div>
        `;
        container.insertBefore(selectAllDiv, container.firstChild);
        
        // 绑定全选/取消全选事件
        document.getElementById('selectAllFields').addEventListener('change', function() {
            const isChecked = this.checked;
            document.querySelectorAll('.export-field').forEach(checkbox => {
                checkbox.checked = isChecked;
            });
        });
    }

    // 渲染分页控件
    function renderPagination() {
        const paginationContainer = document.querySelector('.pagination');
        
        if (!paginationContainer) return;
        
        const totalPages = Math.ceil(totalRecords / pageSize);
        
        let paginationHtml = '';
        
        // 上一页按钮
        paginationHtml += `
            <button class="page-btn" data-page="prev" ${currentPage === 1 ? 'disabled' : ''}>
                上一页
            </button>
        `;
        
        // 页码按钮
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, startPage + 4);
        
        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `
                <button class="page-btn ${i === currentPage ? 'active' : ''}" data-page="${i}">
                    ${i}
                </button>
            `;
        }
        
        // 下一页按钮
        paginationHtml += `
            <button class="page-btn" data-page="next" ${currentPage === totalPages || totalPages === 0 ? 'disabled' : ''}>
                下一页
            </button>
        `;
        
        paginationContainer.innerHTML = paginationHtml;
        
        // 绑定分页按钮点击事件
        document.querySelectorAll('.page-btn').forEach(button => {
            button.addEventListener('click', function() {
                if (this.disabled) return;
                
                const page = this.dataset.page;
                
                if (page === 'prev') {
                    currentPage--;
                } else if (page === 'next') {
                    currentPage++;
                } else {
                    currentPage = parseInt(page);
                }
                
                fetchViolationData();
            });
        });
    }

    // 生成筛选选项
    function generateFilterOptions(data, operators = []) {
        if (!data || !Array.isArray(data) || data.length === 0) return;
        
        // 获取所有店铺
        const shops = [...new Set(data.map(item => item.shopName).filter(Boolean))];
        const shopFilterElement = document.getElementById('shopFilter');
        
        if (shopFilterElement && shops.length > 0) {
            // 保存当前选中的值
            const currentSelectedShop = shopFilter || 'all';
            
            let shopOptions = '<option value="all">所有店铺</option>';
            shops.sort().forEach(shop => {
                if (shop) {
                    shopOptions += `<option value="${shop}">${shop}</option>`;
                }
            });
            
            // 更新选项
            shopFilterElement.innerHTML = shopOptions;
            
            // 恢复选中状态
            shopFilterElement.value = currentSelectedShop;
        }
        
        // 获取运营人员筛选下拉框
        const operatorFilterElement = document.getElementById('operatorFilter');
        if (operatorFilterElement && operators && operators.length > 0) {
            // 保存当前选中的值
            const currentSelectedOperator = operatorFilter || 'all';
            
            let operatorOptions = '<option value="all">所有运营</option>';
            operators.sort().forEach(operator => {
                if (operator) {
                    operatorOptions += `<option value="${operator}">${operator}</option>`;
                }
            });
            
            // 更新选项
            operatorFilterElement.innerHTML = operatorOptions;
            
            // 恢复选中状态
            operatorFilterElement.value = currentSelectedOperator;
        }
        
        // 从API获取处罚类型选项
        fetchPunishTypes().then(punishTypes => {
            const punishTypeFilterElement = document.getElementById('punishTypeFilter');
            if (punishTypeFilterElement && punishTypes.length > 0) {
                // 保存当前选中的值
                const currentSelectedPunishType = punishTypeFilter || 'all';
                
                let punishTypeOptions = '<option value="all">所有处罚类型</option>';
                punishTypes.forEach(punishType => {
                    punishTypeOptions += `<option value="${punishType}">${punishType}</option>`;
                });
                
                // 更新选项
                punishTypeFilterElement.innerHTML = punishTypeOptions;
                
                // 恢复选中状态
                punishTypeFilterElement.value = currentSelectedPunishType;
            }
        }).catch(error => {
            console.error('获取处罚类型失败:', error);
            // 如果API失败，使用默认的处罚类型
            const defaultPunishTypes = [
                '商品降权',
                '违规信息删除',
                '商品下架',
                '店铺降权',
                '限制发布',
                '封禁账号',
                '其他处罚'
            ];
            
            const punishTypeFilterElement = document.getElementById('punishTypeFilter');
            if (punishTypeFilterElement) {
                const currentSelectedPunishType = punishTypeFilter || 'all';
                let punishTypeOptions = '<option value="all">所有处罚类型</option>';
                defaultPunishTypes.forEach(punishType => {
                    punishTypeOptions += `<option value="${punishType}">${punishType}</option>`;
                });
                punishTypeFilterElement.innerHTML = punishTypeOptions;
                punishTypeFilterElement.value = currentSelectedPunishType;
            }
        });
    }

    // 绑定事件处理函数
    function bindEventHandlers() {
        // 防抖计时器
        let filterDebounceTimer = null;
        
        // 防抖应用筛选函数
        function debounceApplyFilters(delay = 300) {
            if (filterDebounceTimer) {
                clearTimeout(filterDebounceTimer);
            }
            filterDebounceTimer = setTimeout(() => {
                applyFilters();
            }, delay);
        }
        
        // 搜索按钮
        document.getElementById('searchBtn')?.addEventListener('click', function() {
            applyFilters();
        });
        
        // 状态筛选
        document.getElementById('statusFilter')?.addEventListener('change', function() {
            statusFilter = this.value;
            debounceApplyFilters(200);
        });
        
        // 店铺筛选
        document.getElementById('shopFilter')?.addEventListener('change', function() {
            shopFilter = this.value;
            debounceApplyFilters(200);
        });
        
        // 运营人员筛选
        document.getElementById('operatorFilter')?.addEventListener('change', function() {
            operatorFilter = this.value;
            debounceApplyFilters(200);
        });
        
        // 处罚类型筛选
        document.getElementById('punishTypeFilter')?.addEventListener('change', function() {
            punishTypeFilter = this.value;
            debounceApplyFilters(200);
        });
        
        // 日期筛选
        document.getElementById('startDateFilter')?.addEventListener('change', function() {
            startDate = this.value;
            debounceApplyFilters(300);
            // 更新图表（延时以避免频繁更新）
            setTimeout(() => {
                renderViolationTrendChart(true);
            }, 300);
        });
        
        document.getElementById('endDateFilter')?.addEventListener('change', function() {
            endDate = this.value;
            debounceApplyFilters(300);
            // 更新图表（延时以避免频繁更新）
            setTimeout(() => {
                renderViolationTrendChart(true);
            }, 300);
        });
        
        // 搜索框输入事件（添加防抖）
        document.getElementById('searchInput')?.addEventListener('input', function() {
            keyword = this.value;
            debounceApplyFilters(500); // 搜索输入延时稍长，避免频繁触发
        });
        
        // 搜索框回车键
        document.getElementById('searchInput')?.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                if (filterDebounceTimer) {
                    clearTimeout(filterDebounceTimer);
                }
                applyFilters();
            }
        });
        
        // 每页显示数量选择
        document.getElementById('pageSizeSelector')?.addEventListener('change', function() {
            pageSize = parseInt(this.value);
            currentPage = 1; // 切换每页条数后，重置为第一页
            fetchViolationData();
        });
        
        // 导出所有字段按钮
        document.getElementById('exportAllBtn')?.addEventListener('click', function() {
            exportData();
        });
        
        // 导出选定字段按钮
        document.getElementById('exportSelectedBtn')?.addEventListener('click', function() {
            // 生成字段列表
            generateExportFieldsList();
            
            // 显示字段选择模态框
            const modal = new bootstrap.Modal(document.getElementById('exportFieldsModal'));
            modal.show();
        });
        
        // 确认导出按钮
        document.getElementById('confirmExportBtn')?.addEventListener('click', function() {
            // 获取选定的字段
            const selectedFields = [];
            document.querySelectorAll('.export-field:checked').forEach(checkbox => {
                selectedFields.push(checkbox.dataset.field);
            });
            
            if (selectedFields.length === 0) {
                alert('请至少选择一个导出字段');
                return;
            }
            
            // 获取导出筛选条件
            const exportShopFilter = document.getElementById('exportShopFilter')?.value || 'all';
            const exportOperatorFilter = document.getElementById('exportOperatorFilter')?.value || 'all';
            const exportPunishTypeFilter = document.getElementById('exportPunishTypeFilter')?.value || 'all';
            const exportStartDate = document.getElementById('exportStartDateFilter')?.value || '';
            const exportEndDate = document.getElementById('exportEndDateFilter')?.value || '';
            
            // 导出选定字段的数据
            exportData(selectedFields, exportShopFilter, exportOperatorFilter, exportPunishTypeFilter, exportStartDate, exportEndDate);
            
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('exportFieldsModal')).hide();
        });
    }

    // 应用筛选条件
    function applyFilters() {
        // 获取搜索文本
        keyword = document.getElementById('searchInput').value;
        
        // 获取状态筛选值
        statusFilter = document.getElementById('statusFilter').value;
        
        // 获取店铺筛选值
        shopFilter = document.getElementById('shopFilter').value;
        
        // 获取运营人员筛选值
        operatorFilter = document.getElementById('operatorFilter').value;
        
        // 获取处罚类型筛选值
        punishTypeFilter = document.getElementById('punishTypeFilter').value;
        
        // 获取日期筛选值
        startDate = document.getElementById('startDateFilter').value;
        endDate = document.getElementById('endDateFilter').value;
        
        // 打印筛选条件，用于调试
        console.log('应用筛选条件:', {
            keyword,
            statusFilter,
            shopFilter,
            operatorFilter,
            punishTypeFilter,
            startDate,
            endDate
        });
        
        // 重置到第一页
        currentPage = 1;
        
        // 获取新数据
        fetchViolationData();
    }

    // 清空筛选条件
    function clearFilters() {
        // 重置搜索框
        const searchInput = document.getElementById('searchInput');
        if (searchInput) searchInput.value = '';
        
        // 重置状态筛选
        const statusFilterEl = document.getElementById('statusFilter');
        if (statusFilterEl) statusFilterEl.value = 'all';
        
        // 重置店铺筛选
        const shopFilterEl = document.getElementById('shopFilter');
        if (shopFilterEl) shopFilterEl.value = 'all';
        
        // 重置运营人员筛选
        const operatorFilterEl = document.getElementById('operatorFilter');
        if (operatorFilterEl) operatorFilterEl.value = 'all';
        
        // 重置处罚类型筛选
        const punishTypeFilterEl = document.getElementById('punishTypeFilter');
        if (punishTypeFilterEl) punishTypeFilterEl.value = 'all';
        
        // 重置日期筛选
        const startDateFilterEl = document.getElementById('startDateFilter');
        if (startDateFilterEl) startDateFilterEl.value = '';
        
        const endDateFilterEl = document.getElementById('endDateFilter');
        if (endDateFilterEl) endDateFilterEl.value = '';
        
        // 重置每页显示数量
        const pageSizeSelectorEl = document.getElementById('pageSizeSelector');
        if (pageSizeSelectorEl) pageSizeSelectorEl.value = '20';
        
        // 重置所有全局变量
        keyword = '';
        statusFilter = 'all';
        shopFilter = 'all';
        operatorFilter = 'all';
        punishTypeFilter = 'all';
        startDate = '';
        endDate = '';
        pageSize = 20;
        currentPage = 1;
        
        // 提示用户
        console.log('已清空所有筛选条件');
        
        // 重新获取数据
        fetchViolationData();
    }

    // 刷新数据
    function refreshData() {
        // 清除缓存
        localStorage.removeItem('violationDataCache');
        
        // 显示刷新提示
        console.log('正在刷新数据...');
        
        // 重新获取数据
        fetchViolationData();
        
        // 重新渲染图表
        setTimeout(() => {
            renderViolationTrendChart(true);
        }, 500);
    }

    // 导出数据为CSV
    function exportData(selectedFields = null, shopFilterValue = null, operatorFilterValue = null, punishTypeFilterValue = null, startDateValue = null, endDateValue = null) {
        // 构建请求参数
        const exportOptions = {
            fields: selectedFields || [
                'recordId', 'goodsId', 'productTitle', 'targetTypeId', 'punishMeasure', 
                'punishReason', 'shopName', 'operator', 'startTimeFormat', 'endTimeFormat', 'statusDisplay'
            ],
            shop: shopFilterValue || shopFilter,
            operator: operatorFilterValue || operatorFilter,
            punishType: punishTypeFilterValue || punishTypeFilter,
            startDate: startDateValue || startDate,
            endDate: endDateValue || endDate
        };
        
        // 发送导出请求
        fetch('/api/violation-data/export', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(exportOptions)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('导出失败');
            }
            return response.blob();
        })
        .then(blob => {
            // 创建下载链接
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            
            // 生成包含筛选条件的文件名
            let filename = "商品违规整改数据";
            
            // 添加店铺筛选信息
            if (exportOptions.shop && exportOptions.shop !== 'all') {
                filename += `_${exportOptions.shop}`;
            }
            
            // 添加运营人员筛选信息
            if (exportOptions.operator && exportOptions.operator !== 'all') {
                filename += `_${exportOptions.operator}`;
            }
            
            // 添加处罚类型筛选信息
            if (exportOptions.punishType && exportOptions.punishType !== 'all') {
                filename += `_${exportOptions.punishType}`;
            }
            
            // 添加日期筛选信息
            if (exportOptions.startDate || exportOptions.endDate) {
                if (exportOptions.startDate && exportOptions.endDate) {
                    filename += `_${exportOptions.startDate}至${exportOptions.endDate}`;
                } else if (exportOptions.startDate) {
                    filename += `_从${exportOptions.startDate}`;
                } else if (exportOptions.endDate) {
                    filename += `_至${exportOptions.endDate}`;
                }
            }
            
            // 添加当前日期
            filename += `_${new Date().toISOString().slice(0, 10)}`;
            
            a.download = `${filename}.csv`;
            document.body.appendChild(a);
            a.click();
            
            // 清理
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        })
        .catch(error => {
            console.error('导出失败:', error);
            alert('导出失败：' + error.message);
        });
    }

    // 查看违规详情
    function viewViolationDetails(id, data) {
        const violation = data.find(item => item.id === id);
        
        if (!violation) {
            alert('未找到该记录');
            return;
        }
        
        // 处理处罚措施显示
        let punishMeasureDisplay = '';
        if (violation.punishMeasureDisplay) {
            punishMeasureDisplay = violation.punishMeasureDisplay;
        } else if (typeof violation.punishMeasure === 'string') {
            // 尝试处理JSON格式的字符串
            if (violation.punishMeasure.includes('[')) {
                try {
                    const measures = JSON.parse(violation.punishMeasure.replace(/'/g, '"'));
                    punishMeasureDisplay = measures.join(', ');
                } catch (e) {
                    punishMeasureDisplay = violation.punishMeasure;
                }
            } else {
                punishMeasureDisplay = violation.punishMeasure;
            }
        } else if (Array.isArray(violation.punishMeasure)) {
            // 如果已经是数组
            punishMeasureDisplay = violation.punishMeasure.join(', ');
        } else {
            punishMeasureDisplay = violation.punishMeasure || '-';
        }
        
        // 设置详情内容
        const detailsModalBody = document.getElementById('violationDetailsContent');
        detailsModalBody.innerHTML = `
            <div class="violation-details">
                <div class="detail-item">
                    <div class="detail-label">记录ID</div>
                    <div class="detail-value">${violation.recordId || '-'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">商品ID</div>
                    <div class="detail-value">${violation.goodsId || '-'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">商品标题</div>
                    <div class="detail-value">${violation.productTitle || '-'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">店铺</div>
                    <div class="detail-value">${violation.shopName || '-'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">运营人员</div>
                    <div class="detail-value">${violation.operator || '-'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">违规目标</div>
                    <div class="detail-value">${targetTypeMapping[violation.targetTypeId?.toLowerCase()] || violation.targetTypeId || '-'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">处罚措施</div>
                    <div class="detail-value">${punishMeasureDisplay}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">处罚原因</div>
                    <div class="detail-value">${violation.punishReason || '-'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">开始时间</div>
                    <div class="detail-value">${violation.startTimeFormat || '-'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">结束时间</div>
                    <div class="detail-value">${violation.endTimeFormat || '-'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">实际结束时间</div>
                    <div class="detail-value">${violation.realEndTimeFormat || '-'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">状态</div>
                    <div class="detail-value">${violation.statusDisplay || '-'}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">整改措施</div>
                    <div class="detail-value">${violation.rectificationMeasure || '-'}</div>
                </div>
            </div>
        `;
        
        // 显示详情模态框
        const modal = new bootstrap.Modal(document.getElementById('violationDetailsModal'));
        modal.show();
    }

    // 打开添加/编辑违规记录模态框
    function openViolationModal(violation = null) {
        // 设置模态框标题
        document.getElementById('violationModalTitle').textContent = violation ? '编辑违规记录' : '添加违规记录';
        
        // 重置表单
        document.getElementById('violationForm').reset();
        
        // 如果是编辑模式，填充表单数据
        if (violation) {
            document.getElementById('violationId').value = violation.id || violation.记录ID || '';
            document.getElementById('goodsId').value = violation.goodsId || violation.goodsID || '';
            document.getElementById('shopName').value = violation.shopName || violation.店铺名称 || '';
            document.getElementById('productTitle').value = violation.productTitle || violation.商品名称 || '';
            
            // 处理处罚措施字段
            let punishMeasure = violation.punishMeasure || violation.处罚措施 || '';
            if (typeof punishMeasure === 'string' && punishMeasure.includes('[')) {
                try {
                    const measures = JSON.parse(punishMeasure.replace(/'/g, '"'));
                    if (measures.includes('商品降权')) {
                        document.getElementById('punishMeasure').value = '商品降权';
                    } else if (measures.includes('违规信息删除')) {
                        document.getElementById('punishMeasure').value = '违规信息删除';
                    } else {
                        document.getElementById('punishMeasure').value = '其他处罚';
                    }
                } catch (e) {
                    document.getElementById('punishMeasure').value = punishMeasure;
                }
            } else {
                document.getElementById('punishMeasure').value = punishMeasure;
            }
            
            document.getElementById('targetType').value = violation.targetType || violation.目标类型 || 'goods';
            document.getElementById('punishReason').value = violation.punishReason || violation.处罚原因 || '';
            
            // 处理时间字段
            if (violation.startTimeFormat || violation.开始时间) {
                // 将日期时间格式转换为HTML datetime-local接受的格式
                const startTime = violation.startTimeFormat || violation.开始时间;
                document.getElementById('startTimeFormat').value = formatDateTimeForInput(startTime);
            } else if (violation.startTime && !isNaN(violation.startTime)) {
                // 如果是时间戳，转换为datetime-local格式
                const date = new Date(Number(violation.startTime));
                document.getElementById('startTimeFormat').value = formatDateForDatetimeLocal(date);
            }
            
            if (violation.endTimeFormat || violation.结束时间) {
                const endTime = violation.endTimeFormat || violation.结束时间;
                document.getElementById('endTimeFormat').value = formatDateTimeForInput(endTime);
            } else if (violation.endTime && !isNaN(violation.endTime)) {
                const date = new Date(Number(violation.endTime));
                document.getElementById('endTimeFormat').value = formatDateForDatetimeLocal(date);
            }
            
            // 处罚状态
            document.getElementById('punishStatus').value = violation.punishStatus || violation.处罚状态 || '2';
            
            // 整改措施
            document.getElementById('rectificationMeasure').value = violation.rectificationMeasure || '';
        } else {
            // 设置默认值
            document.getElementById('targetType').value = 'goods';
            document.getElementById('punishStatus').value = '1';
            
            // 设置默认开始时间为今天
            const now = new Date();
            document.getElementById('startTimeFormat').value = formatDateForDatetimeLocal(now);
        }
        
        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('violationModal'));
        modal.show();
    }

    // 格式化日期时间字符串为input datetime-local接受的格式
    function formatDateTimeForInput(dateTimeStr) {
        if (!dateTimeStr) return '';
        
        try {
            // 尝试解析日期字符串
            const date = new Date(dateTimeStr);
            if (isNaN(date.getTime())) {
                // 如果无法直接解析，尝试一些常见格式
                if (dateTimeStr.includes('/')) {
                    // 如果包含/，可能是 MM/DD/YYYY 格式
                    dateTimeStr = dateTimeStr.replace(/(\d+)\/(\d+)\/(\d+)/, '$3-$1-$2');
                }
                
                // 尝试解析自定义格式 YYYY-MM-DD HH:mm:ss
                const parts = dateTimeStr.match(/(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})/);
                if (parts) {
                    return `${parts[1]}-${parts[2]}-${parts[3]}T${parts[4]}:${parts[5]}`;
                }
                
                return '';
            }
            
            // 格式化为datetime-local接受的格式
            return formatDateForDatetimeLocal(date);
        } catch (e) {
            console.error('日期格式化错误:', e);
            return '';
        }
    }

    // 格式化日期对象为datetime-local接受的格式 YYYY-MM-DDTHH:mm
    function formatDateForDatetimeLocal(date) {
        if (!date || isNaN(date.getTime())) return '';
        
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        
        return `${year}-${month}-${day}T${hours}:${minutes}`;
    }

    // 保存违规记录
    function saveViolation() {
        // 获取表单数据
        const form = document.getElementById('violationForm');
        
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }
        
        const id = document.getElementById('violationId').value || String(Date.now());
        const goodsId = document.getElementById('goodsId').value;
        const shopName = document.getElementById('shopName').value;
        const productTitle = document.getElementById('productTitle').value;
        const punishMeasure = document.getElementById('punishMeasure').value;
        const targetType = document.getElementById('targetType').value;
        const punishReason = document.getElementById('punishReason').value;
        const startTimeFormat = document.getElementById('startTimeFormat').value;
        const endTimeFormat = document.getElementById('endTimeFormat').value;
        const punishStatus = document.getElementById('punishStatus').value;
        const rectificationMeasure = document.getElementById('rectificationMeasure').value;
        
        // 转换datetime-local值为时间戳
        let startTime = '';
        let endTime = '';
        
        if (startTimeFormat) {
            startTime = new Date(startTimeFormat).getTime().toString();
        }
        
        if (endTimeFormat) {
            endTime = new Date(endTimeFormat).getTime().toString();
        }
        
        // 构建处罚措施数组
        const punishMeasureArray = punishMeasure ? `['${punishMeasure}']` : '';
        
        // 映射状态显示
        const statusDisplay = statusMapping[punishStatus] || '未知';
        
        const violation = {
            id,
            记录ID: id,
            goodsId,
            goodsID: goodsId,
            shopName,
            店铺名称: shopName,
            productTitle,
            商品名称: productTitle,
            punishMeasure: punishMeasureArray,
            处罚措施: punishMeasureArray,
            targetType,
            目标类型: targetType,
            punishReason,
            处罚原因: punishReason,
            startTime,
            startTimeFormat: formatTimestamp(startTime) || startTimeFormat,
            开始时间: formatTimestamp(startTime) || startTimeFormat,
            endTime,
            endTimeFormat: formatTimestamp(endTime) || endTimeFormat,
            结束时间: formatTimestamp(endTime) || endTimeFormat,
            punishStatus,
            处罚状态: punishStatus,
            rectificationMeasure,
            punishMeasureDisplay: punishMeasure,
            statusDisplay
        };
        
        // 保存到API或本地数据中
        saveViolationToAPI(violation)
            .then(() => {
                // 关闭模态框
                bootstrap.Modal.getInstance(document.getElementById('violationModal')).hide();
                
                // 刷新数据
                alert('保存成功！');
                
                // 更新本地数据
                const existingIndex = data.findIndex(item => item.id === violation.id);
                if (existingIndex !== -1) {
                    data[existingIndex] = { ...data[existingIndex], ...violation };
                } else {
                    data.push(violation);
                }
                
                // 更新筛选后的数据
                data = [...data];
                applyFilters();
            })
            .catch(error => {
                console.error('保存违规记录失败:', error);
                alert('保存失败，请重试。' + (error.message || ''));
            });
    }

    // 保存违规记录到API
    function saveViolationToAPI(violation) {
        // 实际项目中应该请求后端API进行持久化存储
        // 这里仅模拟API调用，实际上数据只存在于当前页面会话中
        return new Promise((resolve, reject) => {
            // 模拟API延迟
            setTimeout(() => {
                try {
                    // 检查是否为编辑现有记录
                    const existingIndex = data.findIndex(item => 
                        (item.id === violation.id) || (item.记录ID === violation.id)
                    );
                    
                    if (existingIndex !== -1) {
                        console.log('更新记录:', violation.id);
                        // 合并原有数据和新数据
                        data[existingIndex] = { ...data[existingIndex], ...violation };
                    } else {
                        console.log('新增记录:', violation.id);
                        data.push(violation);
                    }
                    
                    resolve();
                } catch (error) {
                    reject(error);
                }
            }, 300);
        });
    }

    // 编辑违规记录
    function editViolation(id) {
        const violation = data.find(item => 
            (item.id === id) || (item.记录ID === id)
        );
        
        if (!violation) {
            alert('未找到该记录');
            return;
        }
        
        openViolationModal(violation);
    }

    // 删除违规记录
    function deleteViolation(id) {
        if (!confirm('确定要删除此违规记录吗？此操作不可恢复。')) {
            return;
        }
        
        // 从数据中删除
        const index = data.findIndex(item => 
            (item.id === id) || (item.记录ID === id)
        );
        
        if (index !== -1) {
            data.splice(index, 1);
            
            // 更新筛选后的数据
            data = data.filter(item => 
                (item.id !== id) && (item.记录ID !== id)
            );
            
            // 如果当前页没有数据了，返回上一页
            if (data.length > 0 && (currentPage - 1) * pageSize >= data.length) {
                currentPage--;
            }
            
            // 重新渲染表格
            renderViolationTable();
            
            alert('删除成功！');
        } else {
            alert('未找到要删除的记录');
        }
    }

    // 添加自定义样式
    function addViolationManagementStyles() {
        // 删除已有的样式
        const existingStyle = document.getElementById('violation-management-styles');
        if (existingStyle) {
            existingStyle.remove();
        }
        
        // 创建新的样式元素
        const styleElement = document.createElement('style');
        styleElement.id = 'violation-management-styles';
        
        // 样式内容
        styleElement.textContent = `
            .violation-management {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }
            
            .violation-management .header {
                margin-bottom: 2rem;
                border-bottom: 1px solid #f0f0f0;
                padding-bottom: 1rem;
            }
            
            .violation-management .header h1 {
                font-size: 1.8rem;
                font-weight: 600;
                color: #2c3e50;
                margin-bottom: 0.5rem;
            }
            
            .violation-management .header p {
                color: #7f8c8d;
                margin-bottom: 0;
            }
            
            .data-overview .overview-card {
                background-color: #fff;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
                display: flex;
                padding: 1.25rem;
                position: relative;
                transition: transform 0.2s ease-in-out;
                height: 100%;
            }
            
            .data-overview .overview-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            }
            
            .data-overview .overview-icon {
                width: 48px;
                height: 48px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 1rem;
            }
            
            .data-overview .overview-icon i {
                color: white;
                font-size: 1.2rem;
            }
            
            .data-overview .overview-info {
                flex-grow: 1;
            }
            
            .data-overview .overview-info h3 {
                font-size: 1.5rem;
                font-weight: 700;
                margin-bottom: 0.25rem;
                color: #2c3e50;
            }
            
            .data-overview .overview-info p {
                color: #7f8c8d;
                margin-bottom: 0;
                font-size: 0.9rem;
            }
            
            .data-overview .overview-trend {
                position: absolute;
                top: 1rem;
                right: 1rem;
                font-size: 0.8rem;
                padding: 3px 8px;
                border-radius: 12px;
                display: flex;
                align-items: center;
                gap: 4px;
            }
            
            .data-overview .overview-trend.up {
                background-color: rgba(46, 204, 113, 0.1);
                color: #2ecc71;
            }
            
            .data-overview .overview-trend.down {
                background-color: rgba(231, 76, 60, 0.1);
                color: #e74c3c;
            }
            
            .control-panel {
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                border-radius: 12px;
                padding: 2rem;
                margin-bottom: 2rem;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                border: 1px solid #e9ecef;
            }
            
            .filter-item {
                margin-bottom: 0.5rem;
            }
            
            .filter-item label {
                font-weight: 600;
                color: #495057;
                font-size: 0.875rem;
                margin-bottom: 0.5rem;
                display: block;
            }
            
            .filter-item .form-select {
                border: 1px solid #ced4da;
                border-radius: 6px;
                font-size: 0.875rem;
                transition: all 0.2s ease;
            }
            
            .filter-item .form-select:focus {
                border-color: #007bff;
                box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            }
            
            .filter-item .form-control {
                border: 1px solid #ced4da;
                border-radius: 6px;
                font-size: 0.875rem;
                transition: all 0.2s ease;
            }
            
            .filter-item .form-control:focus {
                border-color: #007bff;
                box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            }
            
            .filter-actions {
                padding-top: 1.5rem;
            }
            
            .filter-actions .btn {
                font-size: 0.875rem;
                font-weight: 500;
                border-radius: 6px;
                padding: 0.5rem 1rem;
                transition: all 0.2s ease;
            }
            
            .filter-actions .btn:hover {
                transform: translateY(-1px);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
            }
            
            .export-actions {
                padding-top: 1.5rem;
            }
            
            .export-actions .btn {
                font-size: 0.875rem;
                font-weight: 500;
                border-radius: 6px;
                padding: 0.5rem 1rem;
                transition: all 0.2s ease;
            }
            
            .export-actions .btn:hover {
                transform: translateY(-1px);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
            }
            
            .input-group-text {
                background-color: #e9ecef;
                border-color: #ced4da;
                font-size: 0.875rem;
                font-weight: 500;
            }
            
            /* 响应式优化 */
            @media (max-width: 768px) {
                .control-panel {
                    padding: 1.5rem;
                }
                
                .filter-actions,
                .export-actions {
                    justify-content: center;
                    padding-top: 1rem;
                }
                
                .filter-actions .btn,
                .export-actions .btn {
                    flex: 1;
                    margin-bottom: 0.5rem;
                }
            }
            
            .violation-table {
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            }
            
            .violation-table thead {
                background-color: #f8fafc;
            }
            
            .violation-table thead th {
                font-weight: 600;
                color: #4a5568;
                border-top: none;
                padding: 0.75rem 1rem;
                vertical-align: middle;
            }
            
            .violation-table tbody td {
                padding: 0.75rem 1rem;
                vertical-align: middle;
            }
            
            .violation-row {
                cursor: pointer;
                transition: background-color 0.2s ease;
            }
            
            .violation-row:hover {
                background-color: #f1f5f9;
            }
            
            .product-title {
                max-height: 2.8em;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                line-height: 1.4;
            }
            
            .punish-reason {
                max-height: 2.8em;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                line-height: 1.4;
            }
            
            .action-buttons {
                display: flex;
                justify-content: center;
                gap: 0.5rem;
            }
            
            .action-buttons .btn {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                width: 2rem;
                height: 2rem;
                padding: 0;
                transition: transform 0.15s ease;
            }
            
            .action-buttons .btn:hover {
                transform: scale(1.1);
            }
            
            .record-info {
                color: #718096;
                font-size: 0.9rem;
            }
            
            .pagination .page-link {
                color: #4a5568;
                border-color: #e2e8f0;
                margin: 0 2px;
            }
            
            .pagination .page-item.active .page-link {
                background-color: #4299e1;
                border-color: #4299e1;
            }
            
            .pagination .page-item.disabled .page-link {
                color: #cbd5e0;
            }
            
            /* 模态框样式 */
            .modal-content {
                border-radius: 10px;
                border: none;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            }
            
            .modal-header {
                background-color: #f8fafc;
                border-bottom: 1px solid #edf2f7;
            }
            
            .modal-title {
                font-weight: 600;
                color: #2d3748;
            }
            
            .modal-body {
                padding: 1.5rem;
            }
            
            .modal-footer {
                border-top: 1px solid #edf2f7;
                padding: 1rem 1.5rem;
            }
            
            /* 违规详情样式 */
            .violation-details {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 1.25rem;
            }
            
            .violation-details .detail-group {
                margin-bottom: 1rem;
            }
            
            .violation-details .detail-group label {
                display: block;
                font-size: 0.875rem;
                color: #64748b;
                margin-bottom: 0.25rem;
            }
            
            .violation-details .detail-group .value {
                font-weight: 500;
                color: #334155;
                word-break: break-word;
            }
            
            .violation-details .full-width {
                grid-column: 1 / -1;
            }
            
            /* 状态标签样式 */
            .violation-table .badge {
                font-weight: 500;
                padding: 0.4em 0.7em;
                white-space: nowrap;
            }
            
            /* 图表容器样式 */
            .chart-container {
                position: relative;
                z-index: 10;
                margin-bottom: 2.5rem;
                background-color: white;
            }
            
            .chart-container .card {
                border: 1px solid #e9ecef;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                margin-bottom: 0;
                border-radius: 12px;
                overflow: hidden;
            }
            
            .chart-container .card-header {
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                border-bottom: 1px solid #e9ecef;
                padding: 1rem 1.5rem;
            }
            
            .chart-container .card-title {
                color: #495057;
                font-weight: 600;
                font-size: 1.1rem;
            }
            
            .chart-container .card-body {
                padding: 1.5rem;
                background-color: white;
                position: relative;
                z-index: 11;
            }
            
            #violationTrendChart {
                width: 100%;
                height: 450px;
                min-height: 400px;
                max-height: 500px;
            }
            

            
            /* 数据容器样式优化 */
            .data-container {
                position: relative;
                z-index: 5;
                background-color: white;
                clear: both;
                margin-top: 18rem;
                padding-top: 1rem;
                border-radius: 12px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            }
            
            /* 确保表格不被遮挡 */
            .violation-table {
                position: relative;
                z-index: 6;
                background-color: white;
            }
            
            /* 表格容器 */
            .table-responsive {
                background-color: white;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            }
        `;
        
        // 添加到文档头部
        document.head.appendChild(styleElement);
    }

    // 根据当前用户权限设置操作按钮的可见性
    function setActionButtonsVisibility() {
        const userInfo = getCurrentUserInfo();
        const isAdmin = userInfo.role === 'admin';
        
        // 编辑和删除按钮只有管理员可见
        document.querySelectorAll('.edit-btn, .delete-btn').forEach(btn => {
            btn.style.display = isAdmin ? 'inline-block' : 'none';
        });
    }

    // 从API获取处罚类型数据
    function fetchPunishTypes() {
        return new Promise((resolve, reject) => {
            fetch('/api/violation-data/punish-types')
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        resolve(result.data);
                    } else {
                        reject(new Error(result.message || '获取处罚类型失败'));
                    }
                })
                .catch(error => {
                    console.error('获取处罚类型数据失败:', error);
                    reject(error);
                });
        });
    }

    // 从API获取违规趋势数据
    function fetchTrendData(days = 30) {
        return new Promise((resolve, reject) => {
            // 构建API URL
            let url = `/api/violation-data/trend?days=${days}`;
            
            // 如果有自定义日期范围，添加到URL
            if (startDate && endDate) {
                url += `&startDate=${encodeURIComponent(startDate)}&endDate=${encodeURIComponent(endDate)}`;
            }
            
            fetch(url)
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        resolve({
                            labels: result.data.labels,
                            data: result.data.values,
                            statistics: result.data.statistics
                        });
                    } else {
                        reject(new Error(result.message || '获取趋势数据失败'));
                    }
                })
                .catch(error => {
                    console.error('获取违规趋势数据失败:', error);
                    // 如果API失败，使用本地数据作为备选方案
                    resolve(generateTrendDataFallback(window.currentViolationData || [], days));
                });
        });
    }

    // 备选方案：从本地数据生成趋势数据
    function generateTrendDataFallback(data, days = 30) {
        let chartStartDate = new Date();
        let chartEndDate = new Date();
        chartStartDate.setDate(chartEndDate.getDate() - days + 1);
        
        // 如果有自定义日期范围，使用自定义范围
        if (startDate && endDate) {
            const customStartDate = new Date(startDate);
            const customEndDate = new Date(endDate);
            
            if (!isNaN(customStartDate.getTime()) && !isNaN(customEndDate.getTime())) {
                chartStartDate = customStartDate;
                chartEndDate = customEndDate;
                days = Math.ceil((chartEndDate - chartStartDate) / (1000 * 60 * 60 * 24)) + 1;
            }
        }
        
        // 创建日期数组
        const dateLabels = [];
        const currentDate = new Date(chartStartDate);
        
        for (let i = 0; i < days; i++) {
            dateLabels.push(currentDate.toISOString().split('T')[0]);
            currentDate.setDate(currentDate.getDate() + 1);
        }
        
        // 统计每日违规数量
        const dailyCounts = {};
        dateLabels.forEach(date => {
            dailyCounts[date] = 0;
        });
        
        // 计算每日违规数量
        data.forEach(item => {
            let itemDate = null;
            
            // 尝试从不同的时间字段获取日期
            if (item.startTimeFormat) {
                itemDate = new Date(item.startTimeFormat);
            } else if (item.startTime && !isNaN(item.startTime)) {
                itemDate = new Date(Number(item.startTime));
            } else if (item['开始时间']) {
                itemDate = new Date(item['开始时间']);
            }
            
            if (itemDate && !isNaN(itemDate.getTime())) {
                const dateStr = itemDate.toISOString().split('T')[0];
                if (dailyCounts.hasOwnProperty(dateStr)) {
                    dailyCounts[dateStr]++;
                }
            }
        });
        
        const total = Object.values(dailyCounts).reduce((sum, count) => sum + count, 0);
        const dataValues = dateLabels.map(date => dailyCounts[date]);
        
        return {
            labels: dateLabels,
            data: dataValues,
            statistics: {
                total: total,
                max_daily: Math.max(...dataValues),
                min_daily: Math.min(...dataValues),
                avg_daily: total / dateLabels.length,
                days: days
            }
        };
    }

    // 渲染违规趋势图表
    function renderViolationTrendChart(forceRefresh = false) {
        const chartContainer = document.getElementById('violationTrendChart');
        if (!chartContainer) return;
        
        // 检查是否有 ECharts 库
        if (typeof echarts === 'undefined') {
            chartContainer.innerHTML = `
                <div class="text-center text-muted py-5">
                    <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                    <p>图表库未加载，请确保已引入 ECharts 库</p>
                    <small>您可以在页面中添加：&lt;script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"&gt;&lt;/script&gt;</small>
                </div>
            `;
            return;
        }
        
        // 显示加载状态
        chartContainer.innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-3">正在加载趋势数据...</p>
            </div>
        `;
        
        // 从API获取趋势数据
        fetchTrendData(30)
            .then(trendData => {
                // 重新创建图表容器
                chartContainer.innerHTML = '<div style="height: 450px;"></div>';
                const chartElement = chartContainer.firstElementChild;
                
                // 初始化图表
                const chart = echarts.init(chartElement);
                
                const option = {
                    title: {
                        text: `违规趋势 (${trendData.labels.length}天)`,
                        subtext: `总计: ${trendData.statistics.total} 条违规记录 | 平均: ${trendData.statistics.avg_daily} 条/天${trendData.statistics.growth_rate !== undefined ? ` | 增长率: ${trendData.statistics.growth_rate}%` : ''}`,
                        left: 'left'
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            const date = params[0].axisValue;
                            const count = params[0].value;
                            return `${date}<br/>违规数量: ${count} 条`;
                        }
                    },
                    legend: {
                        data: ['违规数量'],
                        top: '50px'
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '12%',
                        top: '80px',
                        containLabel: true
                    },
                    toolbox: {
                        feature: {
                            saveAsImage: { title: '保存图片' },
                            dataZoom: { title: { zoom: '区域缩放', back: '缩放还原' } },
                            restore: { title: '还原' }
                        },
                        right: '20px',
                        top: '10px'
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: trendData.labels,
                        axisLabel: {
                            formatter: function(value) {
                                // 只显示月-日
                                return value.substring(5);
                            },
                            rotate: 45
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '违规数量',
                        minInterval: 1,
                        min: 0
                    },
                    series: [
                        {
                            name: '违规数量',
                            type: 'line',
                            smooth: true,
                            data: trendData.data,
                            itemStyle: {
                                color: '#e74c3c'
                            },
                            areaStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 0,
                                    y2: 1,
                                    colorStops: [
                                        { offset: 0, color: 'rgba(231, 76, 60, 0.3)' },
                                        { offset: 1, color: 'rgba(231, 76, 60, 0.1)' }
                                    ]
                                }
                            },
                            markPoint: {
                                data: [
                                    { type: 'max', name: '最大值' },
                                    { type: 'min', name: '最小值' }
                                ]
                            },
                            markLine: {
                                data: [
                                    { type: 'average', name: '平均值' }
                                ]
                            }
                        }
                    ],
                    dataZoom: [
                        {
                            type: 'inside',
                            start: 0,
                            end: 100
                        },
                        {
                            start: 0,
                            end: 100,
                            height: 30,
                            bottom: 20
                        }
                    ]
                };
                
                chart.setOption(option);
                
                // 响应式处理
                const resizeHandler = function() {
                    chart.resize();
                };
                
                // 移除之前的事件监听器
                if (window.chartResizeHandler) {
                    window.removeEventListener('resize', window.chartResizeHandler);
                }
                
                // 添加新的事件监听器
                window.addEventListener('resize', resizeHandler);
                window.chartResizeHandler = resizeHandler;
                
                // 保存图表实例到全局变量，方便后续更新
                window.violationTrendChart = chart;
                
                console.log('违规趋势图表渲染完成');
            })
            .catch(error => {
                console.error('渲染违规趋势图表失败:', error);
                chartContainer.innerHTML = `
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                        <p>加载趋势数据失败</p>
                        <small>${error.message}</small>
                    </div>
                `;
            });
    }



    // 获取当前用户信息
    function getCurrentUserInfo() {
        // 解析Cookie获取用户信息
        const cookies = document.cookie.split(';');
        const userRoleCookie = cookies.find(cookie => cookie.trim().startsWith('userRole='));
        const userRole = userRoleCookie ? decodeURIComponent(userRoleCookie.split('=')[1]) : null;
        
        return {
            role: userRole
        };
    }

    // 初始化函数，对外暴露
    function generateViolationManagement() {
        // 加载违规整改数据
        loadViolationData();
    }

    // 返回公共API
    return {
        generateViolationManagement: generateViolationManagement,
        applyFilters: applyFilters,
        clearFilters: clearFilters,
        refreshData: refreshData
    };
})();

// 将主函数暴露到全局作用域
window.generateViolationManagement = window.ViolationManagement.generateViolationManagement;
window.applyFilters = window.ViolationManagement.applyFilters;
window.clearFilters = window.ViolationManagement.clearFilters;
window.refreshData = window.ViolationManagement.refreshData;